import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_kit/src/features/location/services/location_cache_service.dart';
import 'package:flutter_kit/src/features/location/logic/location_logic.dart';
import 'package:flutter_kit/src/datasource/models/area_model_entity.dart';

void main() {
  group('Location Module Tests', () {
    test('LocationCacheService should cache and retrieve data', () async {
      final cacheService = LocationCacheService();
      
      // 创建测试数据
      final testAreas = [
        AreaModelEntity()
          ..id = '1'
          ..name = '北京市'
          ..parent = '',
        AreaModelEntity()
          ..id = '2'
          ..name = '上海市'
          ..parent = '',
      ];
      
      // 测试缓存
      final cacheSuccess = await cacheService.cacheAreaData(testAreas);
      expect(cacheSuccess, true);
      
      // 测试读取
      final cachedData = await cacheService.getCachedAreaData();
      expect(cachedData, isNotNull);
      expect(cachedData!.length, 2);
      expect(cachedData[0].name, '北京市');
      expect(cachedData[1].name, '上海市');
      
      // 清理
      await cacheService.clearCache();
    });

    test('AreaSelectionResult should format address correctly', () {
      final province = AreaModelEntity()
        ..id = '1'
        ..name = '北京市'
        ..parent = '';
        
      final city = AreaModelEntity()
        ..id = '2'
        ..name = '朝阳区'
        ..parent = '1';
        
      final result = AreaSelectionResult(
        province: province,
        city: city,
        level: AreaLevel.city,
      );
      
      expect(result.fullAddress, '北京市 朝阳区');
      expect(result.selectedArea.name, '朝阳区');
    });

    test('GroupedAreaData should group areas correctly', () {
      final areas = [
        AreaModelEntity()..id = '1'..name = '安庆'..parent = '',
        AreaModelEntity()..id = '2'..name = '安阳'..parent = '',
      ];
      
      final groupedData = GroupedAreaData(
        letter: 'A',
        areas: areas,
      );
      
      expect(groupedData.letter, 'A');
      expect(groupedData.areas.length, 2);
      expect(groupedData.areas[0].name, '安庆');
    });
  });
}
