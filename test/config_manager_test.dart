import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_kit/src/shared/config/managers/config_manager.dart';
import 'package:flutter_kit/src/shared/config/models/config_models.dart';
import 'package:flutter_kit/src/shared/config/parsers/xml_config_parser.dart';

void main() {
  group('ConfigManager Tests', () {
    late ConfigManager configManager;

    setUp(() {
      configManager = ConfigManager();
    });

    test('should parse default XML configuration', () {
      // 测试默认配置解析
      final defaultConfig = XmlConfigParser.createDefaultConfig();
      
      expect(defaultConfig.version, isNotEmpty);
      expect(defaultConfig.groups, isNotEmpty);
      
      // 检查是否包含预期的配置组
      final groupNames = defaultConfig.groupNames;
      expect(groupNames, contains(ConfigNames.gender));
      expect(groupNames, contains(ConfigNames.welfare));
      expect(groupNames, contains(ConfigNames.jobNature));
    });

    test('should validate XML format correctly', () {
      // 测试有效的XML格式
      const validXml = '''<?xml version="1.0" encoding="utf-8"?>
<Constant version="1.0.0">
  <Item name="Gender">
    <Node key="1" value="男" />
    <Node key="2" value="女" />
  </Item>
</Constant>''';
      
      expect(XmlConfigParser.validateXmlFormat(validXml), isTrue);
      
      // 测试无效的XML格式
      const invalidXml = '<invalid>xml</invalid>';
      expect(XmlConfigParser.validateXmlFormat(invalidXml), isFalse);
    });

    test('should parse XML string correctly', () {
      const xmlContent = '''<?xml version="1.0" encoding="utf-8"?>
<Constant version="1.0.0">
  <Item name="Gender">
    <Node key="1" value="男" />
    <Node key="2" value="女" />
  </Item>
  <Item name="JobNature">
    <Node key="1" value="全职" />
    <Node key="2" value="兼职" />
  </Item>
</Constant>''';

      final config = XmlConfigParser.parseXmlString(xmlContent);
      
      expect(config.version, equals('1.0.0'));
      expect(config.groups.length, equals(2));
      
      // 检查性别配置组
      final genderGroup = config.getGroupByName('Gender');
      expect(genderGroup, isNotNull);
      expect(genderGroup!.items.length, equals(2));
      expect(genderGroup.getValueByKey('1'), equals('男'));
      expect(genderGroup.getValueByKey('2'), equals('女'));
      
      // 检查工作性质配置组
      final jobNatureGroup = config.getGroupByName('JobNature');
      expect(jobNatureGroup, isNotNull);
      expect(jobNatureGroup!.items.length, equals(2));
      expect(jobNatureGroup.getValueByKey('1'), equals('全职'));
      expect(jobNatureGroup.getValueByKey('2'), equals('兼职'));
    });

    test('should handle key-value conversions', () {
      const xmlContent = '''<?xml version="1.0" encoding="utf-8"?>
<Constant version="1.0.0">
  <Item name="Gender">
    <Node key="1" value="男" />
    <Node key="2" value="女" />
  </Item>
</Constant>''';

      final config = XmlConfigParser.parseXmlString(xmlContent);
      
      // 测试key到value的转换
      expect(config.getValueByKey('Gender', '1'), equals('男'));
      expect(config.getValueByKey('Gender', '2'), equals('女'));
      expect(config.getValueByKey('Gender', '3'), isNull);
      
      // 测试value到key的转换
      expect(config.getKeyByValue('Gender', '男'), equals('1'));
      expect(config.getKeyByValue('Gender', '女'), equals('2'));
      expect(config.getKeyByValue('Gender', '未知'), isNull);
    });

    test('should handle empty or invalid configurations gracefully', () {
      // 测试空XML
      expect(() => XmlConfigParser.parseXmlString(''), throwsException);
      
      // 测试无效XML
      expect(() => XmlConfigParser.parseXmlString('<invalid>'), throwsException);
      
      // 测试缺少必要元素的XML
      const incompleteXml = '''<?xml version="1.0" encoding="utf-8"?>
<Constant>
</Constant>''';
      
      final config = XmlConfigParser.parseXmlString(incompleteXml);
      expect(config.version, equals('1.0.0')); // 应该使用默认版本
      expect(config.groups, isEmpty);
    });

    test('should create default configuration when needed', () {
      final defaultConfig = XmlConfigParser.createDefaultConfig();
      
      expect(defaultConfig.version, isNotEmpty);
      expect(defaultConfig.groups, isNotEmpty);
      
      // 检查默认性别配置
      final genderGroup = defaultConfig.getGroupByName(ConfigNames.gender);
      expect(genderGroup, isNotNull);
      expect(genderGroup!.items, isNotEmpty);
      
      // 检查默认工作性质配置
      final jobNatureGroup = defaultConfig.getGroupByName(ConfigNames.jobNature);
      expect(jobNatureGroup, isNotNull);
      expect(jobNatureGroup!.items, isNotEmpty);
    });

    test('should handle ConfigItem equality correctly', () {
      final item1 = ConfigItem(key: '1', value: '男');
      final item2 = ConfigItem(key: '1', value: '男');
      final item3 = ConfigItem(key: '2', value: '女');
      
      expect(item1, equals(item2));
      expect(item1, isNot(equals(item3)));
    });

    test('should handle ConfigGroup operations correctly', () {
      final items = [
        ConfigItem(key: '1', value: '男'),
        ConfigItem(key: '2', value: '女'),
      ];
      
      final group = ConfigGroup(name: 'Gender', items: items);
      
      expect(group.name, equals('Gender'));
      expect(group.items.length, equals(2));
      expect(group.keys, containsAll(['1', '2']));
      expect(group.values, containsAll(['男', '女']));
      
      expect(group.getItemByKey('1')?.value, equals('男'));
      expect(group.getItemByValue('女')?.key, equals('2'));
      expect(group.getValueByKey('1'), equals('男'));
      expect(group.getKeyByValue('女'), equals('2'));
    });
  });
}
