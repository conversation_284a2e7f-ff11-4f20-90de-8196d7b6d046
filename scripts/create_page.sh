#!/bin/bash

# Flutter页面模块创建脚本
# 使用方法: ./scripts/create_page.sh <页面名称>
# 示例: ./scripts/create_page.sh user_profile

set -e

# 检查参数
if [ $# -eq 0 ]; then
    echo "❌ 错误: 请提供页面名称"
    echo "使用方法: ./scripts/create_page.sh <页面名称>"
    echo "示例: ./scripts/create_page.sh user_profile"
    exit 1
fi

PAGE_NAME=$1

echo "🚀 开始创建Flutter页面模块: $PAGE_NAME"
echo "=================================================="

# 1. 运行Dart脚本创建页面
echo "📝 步骤1: 创建页面文件和更新配置..."
dart scripts/create_page.dart "$PAGE_NAME"

if [ $? -ne 0 ]; then
    echo "❌ 页面创建失败"
    exit 1
fi

echo ""
echo "📝 步骤2: 生成路由代码..."

# 2. 生成路由代码
dart run build_runner build --delete-conflicting-outputs

if [ $? -eq 0 ]; then
    echo "✅ 路由代码生成成功"
else
    echo "⚠️  路由代码生成失败，请手动运行:"
    echo "   dart run build_runner build --delete-conflicting-outputs"
fi

echo ""
echo "=================================================="
echo "🎉 页面模块创建完成!"
echo ""
echo "📋 创建的文件:"
echo "   - lib/src/features/$PAGE_NAME/logic/${PAGE_NAME}_logic.dart"
echo "   - lib/src/features/$PAGE_NAME/ui/${PAGE_NAME}_screen.dart"
echo "   - lib/src/features/$PAGE_NAME/ui/widget/${PAGE_NAME}_view.dart"
echo "   - lib/src/datasource/repositories/${PAGE_NAME}_repository.dart"
echo ""
echo "📋 更新的文件:"
echo "   - lib/src/shared/locator.dart (添加了依赖注入配置)"
echo "   - lib/src/core/routing/app_router.dart (添加了路由配置)"
echo ""
echo "🔧 使用方法:"
PAGE_NAME_PASCAL=$(echo "$PAGE_NAME" | sed -r 's/(^|_)([a-z])/\U\2/g')
echo "   context.router.push(${PAGE_NAME_PASCAL}Route());"
echo ""
echo "📝 下一步:"
echo "   1. 根据需求修改 ${PAGE_NAME}_logic.dart 中的业务逻辑"
echo "   2. 根据需求修改 ${PAGE_NAME}_repository.dart 中的API调用"
echo "   3. 根据需求修改 ${PAGE_NAME}_view.dart 中的UI界面"
echo "   4. 如果需要参数传递，请手动修改相关文件"
echo ""
