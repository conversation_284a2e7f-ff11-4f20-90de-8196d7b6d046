#!/usr/bin/env dart

import 'dart:io';

void main(List<String> arguments) {
  if (arguments.isEmpty) {
    print('❌ 错误: 请提供页面名称');
    print('使用方法: dart scripts/create_page.dart <页面名称>');
    print('示例: dart scripts/create_page.dart user_profile');
    exit(1);
  }

  final pageName = arguments[0];
  final pageNameCamelCase = toCamelCase(pageName);
  final pageNamePascalCase = toPascalCase(pageName);
  
  print('🚀 开始创建页面模块: $pageName');
  print('📁 CamelCase: $pageNameCamelCase');
  print('📁 PascalCase: $pageNamePascalCase');

  try {
    // 1. 创建目录结构
    createDirectoryStructure(pageName);
    
    // 2. 创建文件
    createLogicFile(pageName, pageNameCamelCase, pageNamePascalCase);
    createRepositoryFile(pageName, pageNameCamelCase, pageNamePascalCase);
    createScreenFile(pageName, pageNameCamelCase, pageNamePascalCase);
    createViewFile(pageName, pageNameCamelCase, pageNamePascalCase);
    
    // 3. 更新配置文件
    updateLocatorFile(pageName, pageNameCamelCase, pageNamePascalCase);
    updateAppRouterFile(pageName, pageNameCamelCase, pageNamePascalCase);
    
    print('✅ 页面模块创建完成!');
    print('');
    print('📋 接下来需要执行的步骤:');
    print('1. 运行命令生成路由代码:');
    print('   dart run build_runner build --delete-conflicting-outputs');
    print('');
    print('2. 在其他页面中使用跳转:');
    print('   context.router.push(${pageNamePascalCase}Route());');
    print('');
    print('3. 如果需要参数，请手动修改相关文件');
    
  } catch (e) {
    print('❌ 创建失败: $e');
    exit(1);
  }
}

// 创建目录结构
void createDirectoryStructure(String pageName) {
  final directories = [
    'lib/src/features/$pageName',
    'lib/src/features/$pageName/ui',
    'lib/src/features/$pageName/ui/widget',
    'lib/src/features/$pageName/logic',
  ];

  for (final dir in directories) {
    Directory(dir).createSync(recursive: true);
    print('📁 创建目录: $dir');
  }
}

// 创建Logic文件
void createLogicFile(String pageName, String camelCase, String pascalCase) {
  final content = '''import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/datasource/repositories/${pageName}_repository.dart';

/// ${pascalCase}页面逻辑
class ${pascalCase}Logic extends ViewStateLogic {
  final ${pascalCase}Repository repository;

  ${pascalCase}Logic({required this.repository}) {
    // 设置初始状态，避免一直loading
    setSuccess(null);
  }

  @override
  void loadData() {
    // TODO: 实现数据加载逻辑
    // 示例:
    // sendRequest<DataType>(
    //   repository.getData().timeout(
    //     const Duration(seconds: 10),
    //     onTimeout: () {
    //       throw Exception('请求超时，请检查网络连接');
    //     },
    //   ),
    //   successCallback: (data) {
    //     // 处理成功数据
    //   },
    //   failCallback: () {
    //     debugPrint('获取数据失败');
    //   },
    // );
  }

  /// 刷新数据
  void refreshData() {
    loadData();
  }
}
''';

  final file = File('lib/src/features/$pageName/logic/${pageName}_logic.dart');
  file.writeAsStringSync(content);
  print('📄 创建文件: ${file.path}');
}

// 创建Repository文件
void createRepositoryFile(String pageName, String camelCase, String pascalCase) {
  final content = '''import 'package:flutter_kit/src/datasource/http/api_service.dart';
import 'package:flutter_kit/src/datasource/http/dio_config.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/repositories/base_repository.dart';
import 'package:flutter_kit/src/shared/locator.dart';

/// ${pascalCase}相关数据仓库
class ${pascalCase}Repository extends BaseRepository {
  final ApiService apiService;

  ${pascalCase}Repository({
    ApiService? apiService,
  }) : apiService = apiService ?? ApiService(dio: locator<DioConfig>().dio);

  /// 获取${pascalCase}数据
  /// TODO: 根据实际需求修改返回类型和API调用
  Future<UnifiedResponse<String>> getData() async {
    return runApiCall(call: () async {
      // TODO: 替换为实际的API调用
      // return await apiService.getSomeData();
      
      // 临时返回成功响应，避免编译错误
      await Future.delayed(const Duration(milliseconds: 500));
      return UnifiedResponse.success('临时数据');
    });
  }
}
''';

  final file = File('lib/src/datasource/repositories/${pageName}_repository.dart');
  file.writeAsStringSync(content);
  print('📄 创建文件: ${file.path}');
}

// 创建Screen文件
void createScreenFile(String pageName, String camelCase, String pascalCase) {
  final content = '''import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/widgets/view_state_widget.dart';
import 'package:flutter_kit/src/features/$pageName/logic/${pageName}_logic.dart';
import 'package:flutter_kit/src/features/$pageName/ui/widget/${pageName}_view.dart';
import 'package:flutter_kit/src/shared/locator.dart';

@RoutePage()
class ${pascalCase}Screen extends ViewStateWidget<${pascalCase}Logic> {
  const ${pascalCase}Screen({super.key});

  @override
  ${pascalCase}Logic createController() {
    return locator<${pascalCase}Logic>();
  }

  @override
  PreferredSizeWidget? buildAppBar(BuildContext context, ${pascalCase}Logic logic) {
    return AppBar(
      title: const Text('${pascalCase}'),
      centerTitle: true,
    );
  }

  @override
  Widget buildBody(BuildContext context, ${pascalCase}Logic logic) {
    return const ${pascalCase}View();
  }
}
''';

  final file = File('lib/src/features/$pageName/ui/${pageName}_screen.dart');
  file.writeAsStringSync(content);
  print('📄 创建文件: ${file.path}');
}

// 创建View文件
void createViewFile(String pageName, String camelCase, String pascalCase) {
  final content = '''import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';

/// ${pascalCase}页面视图
class ${pascalCase}View extends StatelessWidget {
  const ${pascalCase}View({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.pageBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // TODO: 实现页面内容
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.construction,
                      size: 48.w,
                      color: AppColors.primary,
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      '${pascalCase}页面',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      '页面内容开发中...',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
''';

  final file = File('lib/src/features/$pageName/ui/widget/${pageName}_view.dart');
  file.writeAsStringSync(content);
  print('📄 创建文件: ${file.path}');
}

// 更新locator.dart文件
void updateLocatorFile(String pageName, String camelCase, String pascalCase) {
  final locatorFile = File('lib/src/shared/locator.dart');
  if (!locatorFile.existsSync()) {
    print('❌ 警告: locator.dart 文件不存在');
    return;
  }

  String content = locatorFile.readAsStringSync();

  // 添加导入
  final repositoryImport = "import 'package:flutter_kit/src/datasource/repositories/${pageName}_repository.dart';";
  final logicImport = "import 'package:flutter_kit/src/features/$pageName/logic/${pageName}_logic.dart';";

  // 查找最后一个import语句的位置
  final importLines = content.split('\n').where((line) => line.trim().startsWith('import')).toList();
  if (importLines.isNotEmpty) {
    final lastImport = importLines.last;
    final lastImportIndex = content.indexOf(lastImport) + lastImport.length;
    content = content.substring(0, lastImportIndex) + '\n$repositoryImport\n$logicImport' + content.substring(lastImportIndex);
  }

  // 添加注册代码
  final repositoryRegistration = "  ..registerLazySingleton(() => ${pascalCase}Repository())";
  final logicRegistration = "  ..registerFactory(() => ${pascalCase}Logic(repository: locator<${pascalCase}Repository>()))";

  // 查找最后一个注册语句并处理分号
  final lines = content.split('\n');
  int insertIndex = -1;
  for (int i = lines.length - 1; i >= 0; i--) {
    if (lines[i].trim().startsWith('..register')) {
      // 移除最后一个注册语句的分号（如果有的话）
      if (lines[i].trim().endsWith(');')) {
        lines[i] = lines[i].replaceAll(');', ')');
      }
      insertIndex = i + 1;
      break;
    }
  }

  if (insertIndex != -1) {
    lines.insert(insertIndex, repositoryRegistration);
    lines.insert(insertIndex + 1, logicRegistration + ';'); // 在最后一个注册语句后添加分号
    content = lines.join('\n');
  }

  locatorFile.writeAsStringSync(content);
  print('📄 更新文件: ${locatorFile.path}');
}

// 更新app_router.dart文件
void updateAppRouterFile(String pageName, String camelCase, String pascalCase) {
  final routerFile = File('lib/src/core/routing/app_router.dart');
  if (!routerFile.existsSync()) {
    print('❌ 警告: app_router.dart 文件不存在');
    return;
  }

  String content = routerFile.readAsStringSync();

  // 添加导入
  final screenImport = "import '../../features/$pageName/ui/${pageName}_screen.dart';";

  // 查找最后一个import语句的位置
  final importLines = content.split('\n').where((line) => line.trim().startsWith('import')).toList();
  if (importLines.isNotEmpty) {
    final lastImport = importLines.last;
    final lastImportIndex = content.indexOf(lastImport) + lastImport.length;
    content = content.substring(0, lastImportIndex) + '\n$screenImport' + content.substring(lastImportIndex);
  }

  // 添加路由配置
  final routeConfig = "    AutoRoute(page: ${pascalCase}Route.page),";

  // 查找routes数组的结束位置
  final lines = content.split('\n');
  int insertIndex = -1;
  for (int i = 0; i < lines.length; i++) {
    if (lines[i].trim() == '];' && i > 0 && lines[i-1].trim().contains('AutoRoute(')) {
      insertIndex = i;
      break;
    }
  }

  if (insertIndex != -1) {
    lines.insert(insertIndex, routeConfig);
    content = lines.join('\n');
  }

  routerFile.writeAsStringSync(content);
  print('📄 更新文件: ${routerFile.path}');
}

// 字符串转换工具函数
String toCamelCase(String input) {
  return input.split('_').map((word) => word.toLowerCase()).join('');
}

String toPascalCase(String input) {
  return input.split('_').map((word) =>
    word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1).toLowerCase()
  ).join('');
}
