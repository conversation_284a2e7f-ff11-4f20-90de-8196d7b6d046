# Flutter 页面模块自动生成脚本

## 📋 功能说明

这个脚本可以自动创建完整的Flutter页面模块，包括：

- ✅ 创建完整的目录结构
- ✅ 生成Logic类（继承ViewStateLogic）
- ✅ 生成Repository类（继承BaseRepository）
- ✅ 生成Screen类（使用@RoutePage注解）
- ✅ 生成View类（UI界面）
- ✅ 自动更新locator.dart（依赖注入配置）
- ✅ 自动更新app_router.dart（路由配置）
- ✅ 自动生成路由代码

## 🚀 使用方法

### 方法1: 使用Shell脚本（推荐）

```bash
# 给脚本执行权限（只需要执行一次）
chmod +x scripts/create_page.sh

# 创建页面模块
./scripts/create_page.sh user_profile
```

### 方法2: 直接使用Dart脚本

```bash
# 只创建文件，不生成路由代码
dart scripts/create_page.dart user_profile

# 手动生成路由代码
dart run build_runner build --delete-conflicting-outputs
```

## 📁 生成的文件结构

执行脚本后会创建以下文件：

```
lib/src/features/user_profile/
├── ui/
│   ├── user_profile_screen.dart      # 页面Screen类
│   └── widget/
│       └── user_profile_view.dart    # 页面View类
├── logic/
│   └── user_profile_logic.dart       # 页面Logic类
└── 
lib/src/datasource/repositories/
└── user_profile_repository.dart      # 数据Repository类
```

## 📝 自动更新的配置文件

### locator.dart
自动添加依赖注入配置：
```dart
import 'package:flutter_kit/src/datasource/repositories/user_profile_repository.dart';
import 'package:flutter_kit/src/features/user_profile/logic/user_profile_logic.dart';

final GetIt locator = GetIt.instance
  // ...
  ..registerLazySingleton(() => UserProfileRepository())
  ..registerFactory(() => UserProfileLogic(repository: locator<UserProfileRepository>()))
  // ...
```

### app_router.dart
自动添加路由配置：
```dart
import '../../features/user_profile/ui/user_profile_screen.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen,Route')
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> routes = [
    // ...
    AutoRoute(page: UserProfileRoute.page),
    // ...
  ];
}
```

## 🔧 使用生成的页面

### 页面跳转
```dart
// 简单跳转
context.router.push(UserProfileRoute());

// 带参数跳转（需要手动修改相关文件）
context.router.push(UserProfileRoute(userId: '123'));
```

### 自定义开发
生成的文件包含了完整的模板代码，你可以根据需求进行修改：

1. **Logic类**: 修改业务逻辑和数据处理
2. **Repository类**: 修改API调用和数据获取
3. **View类**: 修改UI界面和交互
4. **Screen类**: 修改AppBar和页面配置

## 📋 命名规范

### 输入格式
- 使用下划线分隔的小写字母：`user_profile`
- 支持多个单词：`user_profile_settings`

### 自动转换
- **文件名**: `user_profile` (保持原样)
- **类名**: `UserProfile` (PascalCase)
- **变量名**: `userprofile` (camelCase)

## ⚠️ 注意事项

1. **页面名称**: 请使用有意义的英文名称，避免中文和特殊字符
2. **重复创建**: 脚本不会检查重复，重复执行会覆盖现有文件
3. **手动修改**: 生成后请根据实际需求修改模板代码
4. **参数传递**: 如需要页面参数，请手动修改Screen类和Route配置

## 🐛 故障排除

### 问题1: 权限错误
```bash
chmod +x scripts/create_page.sh
```

### 问题2: 路由生成失败
```bash
dart run build_runner build --delete-conflicting-outputs
```

### 问题3: 依赖注入错误
检查locator.dart文件是否正确更新了注册代码

### 问题4: 路由跳转失败
检查app_router.dart文件是否正确添加了路由配置

## 📚 示例

### 创建用户资料页面
```bash
./scripts/create_page.sh user_profile
```

### 创建设置页面
```bash
./scripts/create_page.sh settings
```

### 创建订单详情页面
```bash
./scripts/create_page.sh order_detail
```

每次执行后都会自动完成所有配置，可以直接使用页面跳转功能。
