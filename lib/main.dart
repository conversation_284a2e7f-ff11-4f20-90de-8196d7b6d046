import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_kit/src/core/app_initializer.dart';
import 'package:flutter_kit/src/core/application.dart';

void main() {
  final AppInitializer appInitializer = AppInitializer();

  runZonedGuarded(
    () async {
      WidgetsFlutterBinding.ensureInitialized();

      await appInitializer.preAppRun();

      // 🚀 直接运行应用，性能优化在 app_initializer 中完成
      runApp(Application());

      appInitializer.postAppRun();
    },
    (error, stack) {
      // 全局错误处理
      debugPrint('🚨 全局错误: $error');
      debugPrint('🚨 错误堆栈: $stack');
    },
  );
}
