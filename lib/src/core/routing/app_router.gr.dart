// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

/// generated route for
/// [BaseInfoScreen]
class BaseInfoRoute extends PageRouteInfo<void> {
  const BaseInfoRoute({List<PageRouteInfo>? children})
      : super(BaseInfoRoute.name, initialChildren: children);

  static const String name = 'BaseInfoRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return BaseInfoScreen();
    },
  );
}

/// generated route for
/// [DiscoverScreen]
class DiscoverRoute extends PageRouteInfo<void> {
  const DiscoverRoute({List<PageRouteInfo>? children})
      : super(DiscoverRoute.name, initialChildren: children);

  static const String name = 'DiscoverRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const DiscoverScreen();
    },
  );
}

/// generated route for
/// [EnterpriseDetailScreen]
class EnterpriseDetailRoute extends PageRouteInfo<EnterpriseDetailRouteArgs> {
  EnterpriseDetailRoute({
    Key? key,
    required String entId,
    List<PageRouteInfo>? children,
  }) : super(
          EnterpriseDetailRoute.name,
          args: EnterpriseDetailRouteArgs(key: key, entId: entId),
          initialChildren: children,
        );

  static const String name = 'EnterpriseDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<EnterpriseDetailRouteArgs>();
      return EnterpriseDetailScreen(key: args.key, entId: args.entId);
    },
  );
}

class EnterpriseDetailRouteArgs {
  const EnterpriseDetailRouteArgs({this.key, required this.entId});

  final Key? key;

  final String entId;

  @override
  String toString() {
    return 'EnterpriseDetailRouteArgs{key: $key, entId: $entId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! EnterpriseDetailRouteArgs) return false;
    return key == other.key && entId == other.entId;
  }

  @override
  int get hashCode => key.hashCode ^ entId.hashCode;
}

/// generated route for
/// [HomeScreenNew]
class HomeRouteNew extends PageRouteInfo<void> {
  const HomeRouteNew({List<PageRouteInfo>? children})
      : super(HomeRouteNew.name, initialChildren: children);

  static const String name = 'HomeRouteNew';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const HomeScreenNew();
    },
  );
}

/// generated route for
/// [JobDetailScreen]
class JobDetailRoute extends PageRouteInfo<JobDetailRouteArgs> {
  JobDetailRoute({
    Key? key,
    required String jobId,
    List<PageRouteInfo>? children,
  }) : super(
          JobDetailRoute.name,
          args: JobDetailRouteArgs(key: key, jobId: jobId),
          initialChildren: children,
        );

  static const String name = 'JobDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<JobDetailRouteArgs>();
      return JobDetailScreen(key: args.key, jobId: args.jobId);
    },
  );
}

class JobDetailRouteArgs {
  const JobDetailRouteArgs({this.key, required this.jobId});

  final Key? key;

  final String jobId;

  @override
  String toString() {
    return 'JobDetailRouteArgs{key: $key, jobId: $jobId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! JobDetailRouteArgs) return false;
    return key == other.key && jobId == other.jobId;
  }

  @override
  int get hashCode => key.hashCode ^ jobId.hashCode;
}

/// generated route for
/// [LoginScreen]
class LoginRoute extends PageRouteInfo<void> {
  const LoginRoute({List<PageRouteInfo>? children})
      : super(LoginRoute.name, initialChildren: children);

  static const String name = 'LoginRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const LoginScreen();
    },
  );
}

/// generated route for
/// [MainTabScreen]
class MainTabRoute extends PageRouteInfo<void> {
  const MainTabRoute({List<PageRouteInfo>? children})
      : super(MainTabRoute.name, initialChildren: children);

  static const String name = 'MainTabRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const MainTabScreen();
    },
  );
}

/// generated route for
/// [MessagesScreen]
class MessagesRoute extends PageRouteInfo<void> {
  const MessagesRoute({List<PageRouteInfo>? children})
      : super(MessagesRoute.name, initialChildren: children);

  static const String name = 'MessagesRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const MessagesScreen();
    },
  );
}

/// generated route for
/// [ProfileScreen]
class ProfileRoute extends PageRouteInfo<void> {
  const ProfileRoute({List<PageRouteInfo>? children})
      : super(ProfileRoute.name, initialChildren: children);

  static const String name = 'ProfileRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ProfileScreen();
    },
  );
}

/// generated route for
/// [ResumeLibraryScreen]
class ResumeLibraryRoute extends PageRouteInfo<void> {
  const ResumeLibraryRoute({List<PageRouteInfo>? children})
      : super(ResumeLibraryRoute.name, initialChildren: children);

  static const String name = 'ResumeLibraryRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ResumeLibraryScreen();
    },
  );
}

/// generated route for
/// [ResumeScreen]
class ResumeRoute extends PageRouteInfo<void> {
  const ResumeRoute({List<PageRouteInfo>? children})
      : super(ResumeRoute.name, initialChildren: children);

  static const String name = 'ResumeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ResumeScreen();
    },
  );
}

/// generated route for
/// [SplashScreen]
class SplashRoute extends PageRouteInfo<void> {
  const SplashRoute({List<PageRouteInfo>? children})
      : super(SplashRoute.name, initialChildren: children);

  static const String name = 'SplashRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const SplashScreen();
    },
  );
}
