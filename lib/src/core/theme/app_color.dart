


import 'dart:ui';

class AppColors{

  static const Color color_f02e4b = Color(0xFFF02E4B);

  static const Color color_0xAEF02E4B = Color(0xFFF18091);

  static const Color color_white = Color(0xffffffff);
  static const Color color_cccccc = Color(0xFFCCCCCC);
  static const Color color_eeeeee = Color(0xFFeeeeee);

  static const Color color_background = Color(0xFFF6F9FE);
  static const Color color_f6f8fb = Color(0xFFF6F8FB);
  static const Color color_686868 = Color(0xFF686868);

  static const Color color_333333 = Color(0xFF333333);
  static const Color color_666666 = Color(0xFF666666);
  static const Color color_999999 = Color(0xFF999999);

  static const Color color_ff0027 = Color(0x1DFFABB4);

  static const Color alpha_05_white = Color(0x0DFFFFFF);
  static const Color alpha_10_white = Color(0x1AFFFFFF);
  static const Color alpha_15_white = Color(0x26FFFFFF);
  static const Color alpha_20_white = Color(0x33FFFFFF);
  static const Color alpha_25_white = Color(0x40FFFFFF);
  static const Color alpha_30_white = Color(0x4DFFFFFF);
  static const Color alpha_35_white = Color(0x59FFFFFF);
  static const Color alpha_40_white = Color(0x66FFFFFF);
  static const Color alpha_45_white = Color(0x73FFFFFF);
  static const Color alpha_50_white = Color(0x80FFFFFF);
  static const Color alpha_55_white = Color(0x8CFFFFFF);
  static const Color alpha_60_white = Color(0x9AFFFFFF);
  static const Color alpha_65_white = Color(0xA6FFFFFF);
  static const Color alpha_70_white = Color(0xB3FFFFFF);
  static const Color alpha_75_white = Color(0xBFFFFFFF);
  static const Color alpha_80_white = Color(0xCDFFFFFF);
  static const Color alpha_85_white = Color(0xD9FFFFFF);
  static const Color alpha_90_white = Color(0xE6FFFFFF);
  static const Color alpha_95_white = Color(0xF2FFFFFF);
  static const Color white = Color(0xFFFFFFFF);
  static const Color white_pressed = Color(0xFFF3F3F3);

  static const Color alpha_05_black = Color(0x0D000000);
  static const Color alpha_10_black = Color(0x1A000000);
  static const Color alpha_15_black = Color(0x26000000);
  static const Color alpha_20_black = Color(0x33000000);
  static const Color alpha_25_black = Color(0x40000000);
  static const Color alpha_30_black = Color(0x4D000000);
  static const Color alpha_35_black = Color(0x59000000);
  static const Color alpha_40_black = Color(0x66000000);
  static const Color alpha_45_black = Color(0x73000000);
  static const Color alpha_50_black = Color(0x80000000);
  static const Color alpha_55_black = Color(0x8C000000);
  static const Color alpha_60_black = Color(0x99000000);
  static const Color alpha_65_black = Color(0xA6000000);
  static const Color alpha_70_black = Color(0xB3000000);
  static const Color alpha_75_black = Color(0xBF000000);
  static const Color alpha_80_black = Color(0xCC000000);
  static const Color alpha_85_black = Color(0xD9000000);
  static const Color alpha_90_black = Color(0xE6000000);
  static const Color alpha_95_black = Color(0xF2000000);
  // 🔥 统一的应用主题颜色定义
  static const Color primary = Color(0xFFED536D);           // 主色调
  static const Color primaryLight = Color(0xFFFFE8EC);      // 主色调浅色版本
  static const Color secondary = Color(0xFFFFE8EC);         // 次要色
  static const Color pageBackground = Color(0xFFF6F9FE);    // 页面背景色 0xFFF6F9FE 0xFFEEF7FF
  static const Color cardBackground = Color(0xFFFFFFFF);    // 卡片/表面色
  static const Color borderColor = Color(0xFFF4F4F4);       // 边框色
  static const Color textPrimary = Color(0xFF1F2329);       // 文字主色
  static const Color textBody = Color(0xFF515A6E);          // 文字正文色（保持兼容性）
  static const Color textSecondary = Color(0xFF515A6E);     // 文字次色
  static const Color textPlaceholder = Color(0xFFCCCCCC);   // 占位文字色
  static const Color requiredColor = Color(0xFFED536D);     // 必填项标识色




  static const Color abcInputMethodNavigationGuard = Color(0xFF000000);
  static const Color abcSearchUrlTextNormal = Color(0xFF7FA87F);
  static const Color abcSearchUrlTextPressed = Color(0xFF000000);
  static const Color abcSearchUrlTextSelected = Color(0xFF000000);
  static const Color accentMaterialDark = Color(0xFF80CBC4); // material_deep_teal_200
  static const Color accentMaterialLight = Color(0xFF009688); // material_deep_teal_500
  static const Color backgroundFloatingMaterialDark = Color(0xFF424242); // material_grey_800
  static const Color backgroundFloatingMaterialLight = Color(0xFFFFFFFF);
  static const Color backgroundMaterialDark = Color(0xFF303030); // material_grey_850
  static const Color backgroundMaterialLight = Color(0xFFFAFAFA); // material_grey_50
  static const Color brightForegroundDisabledMaterialDark = Color(0x80FFFFFF);
  static const Color brightForegroundDisabledMaterialLight = Color(0x80000000);
  static const Color brightForegroundInverseMaterialDark = Color(0xFFFFFFFF); // bright_foreground_material_light
  static const Color brightForegroundInverseMaterialLight = Color(0xFF000000); // bright_foreground_material_dark
  static const Color brightForegroundMaterialDark = Color(0xFFFFFFFF);
  static const Color brightForegroundMaterialLight = Color(0xFF000000);
  static const Color buttonMaterialDark = Color(0xFF5A595B);
  static const Color buttonMaterialLight = Color(0xFFD6D7D7);
  static const Color dadada = Color(0xFFDADADA);
  static const Color dimForegroundDisabledMaterialDark = Color(0x80BEBEBE);
  static const Color dimForegroundDisabledMaterialLight = Color(0x80323232);
  static const Color dimForegroundMaterialDark = Color(0xFFBEBEBE);
  static const Color dimForegroundMaterialLight = Color(0xFF323232);
  static const Color e4e4e4 = Color(0xFFE4E4E4);
  static const Color errorColorMaterialDark = Color(0xFFFF7043);
  static const Color errorColorMaterialLight = Color(0xFFFF5722);
  static const Color ffffff = Color(0xFFFFFFFF);
  static const Color foregroundMaterialDark = Color(0xFFFFFFFF);
  static const Color foregroundMaterialLight = Color(0xFF000000);
  static const Color highlightedTextMaterialDark = Color(0x6680CBC4);
  static const Color highlightedTextMaterialLight = Color(0x66009688);
  static const Color imageColorAccent = Color(0xFF4CA1FB);
  static const Color imageColorBlack = Color(0xFF000000);
  static const Color imageColorBlue = Color(0xFF0078FF);
  static const Color imageColorCyan = Color(0xFF20FFCD);
  static const Color imageColorGreen = Color(0xFF04B00C);
  static const Color imageColorPrimary = Color(0xFFFFFFFF);
  static const Color imageColorPurple = Color(0xFF945AFC);
  static const Color imageColorRed = Color(0xFFFF5151);
  static const Color imageColorWhite = Color(0xFFFFFFFF);
  static const Color imageColorYellow = Color(0xFFFFCC00);
  static const Color imagePickTitleBtnNormal = Color(0x00000000);
  static const Color imagePickTitleBtnPressed = Color(0xFFF4F4F4);
  static const Color imeBackground = Color(0xFFCCCCCC);
  static const Color materialBlueGrey800 = Color(0xFF37474F);
  static const Color materialBlueGrey900 = Color(0xFF263238);
  static const Color materialBlueGrey950 = Color(0xFF21272B);
  static const Color materialDeepTeal200 = Color(0xFF80CBC4);
  static const Color materialDeepTeal500 = Color(0xFF009688);
  static const Color materialGrey100 = Color(0xFFF5F5F5);
  static const Color materialGrey300 = Color(0xFFE0E0E0);
  static const Color materialGrey50 = Color(0xFFFAFAFA);
  static const Color materialGrey600 = Color(0xFF757575);
  static const Color materialGrey800 = Color(0xFF424242);
  static const Color materialGrey850 = Color(0xFF303030);
  static const Color materialGrey900 = Color(0xFF212121);
  static const Color nightBG = Color(0xFF222527);
  static const Color nightBlueBtnTitle = Color(0xFF537DDA);
  static const Color nightLightBtnTitle = Color(0xFFCCCCCC);
  static const Color nightLine = Color(0xFF404040);
  static const Color nightText = Color(0xFFA0A0A0);
  static const Color notificationActionColorFilter = Color(0x8A000000); // secondary_text_default_material_light
  static const Color notificationIconBgColor = Color(0xFF9E9E9E);
  static const Color notificationMaterialBackgroundMediaDefaultColor = Color(0xFF424242);
  static const Color primaryDarkMaterialDark = Color(0xFF000000);
  static const Color primaryDarkMaterialLight = Color(0xFF757575); // material_grey_600
  static const Color primaryMaterialDark = Color(0xFF212121); // material_grey_900
  static const Color primaryMaterialLight = Color(0xFFF5F5F5); // material_grey_100
  static const Color primaryTextDefaultMaterialDark = Color(0xFFFFFFFF);
  static const Color primaryTextDefaultMaterialLight = Color(0xDE000000);
  static const Color primaryTextDisabledMaterialDark = Color(0x4DFFFFFF);
  static const Color primaryTextDisabledMaterialLight = Color(0x39000000);
  static const Color rippleMaterialDark = Color(0x33FFFFFF);
  static const Color rippleMaterialLight = Color(0x1F000000);
  static const Color secondaryTextDefaultMaterialDark = Color(0xB3FFFFFF);
  static const Color secondaryTextDefaultMaterialLight = Color(0x8A000000);
  static const Color secondaryTextDisabledMaterialDark = Color(0x36FFFFFF);
  static const Color secondaryTextDisabledMaterialLight = Color(0x24000000);
  static const Color switchThumbDisabledMaterialDark = Color(0xFF616161);
  static const Color switchThumbDisabledMaterialLight = Color(0xFFBDBDBD);
  static const Color switchThumbNormalMaterialDark = Color(0xFFBDBDBD);
  static const Color switchThumbNormalMaterialLight = Color(0xFFF1F1F1);
  static const Color tooltipBackgroundDark = Color(0xE6616161);
  static const Color tooltipBackgroundLight = Color(0xE6FFFFFF);
  static const Color videoBackgroundGray = Color(0xFFA5A5A5);
  static const Color videoBackgroundGrayLight = Color(0xFFBABABA);
  static const Color videoBgVideoView = Color(0x80000000);
  static const Color videoRecoverScreenText = Color(0xFFF06292);
  static const Color videoSeekBackground = Color(0x809E9E9E);
  static const Color videoSeekBall = Color(0xFFFFFFFF);
  static const Color videoSeekProgress = Color(0xFFFFFFFF);
  static const Color videoSeekSecondaryProgress = Color(0x809E9898);

}