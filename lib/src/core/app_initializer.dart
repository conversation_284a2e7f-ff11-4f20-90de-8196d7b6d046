import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:flutter_kit/src/shared/services/storage/storage.dart';
import 'package:flutter_kit/src/shared/config/managers/config_manager.dart';
import 'package:flutter_kit/src/core/performance/performance_service.dart';

class AppInitializer {
  /// Initialize services, plugins, etc. before the app runs.
  Future<void> preAppRun() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    await locator<Storage>().init();

    // 🎯 启动性能优化服务
    _startPerformanceOptimization();

    // 初始化配置管理器（异步，不阻塞启动）
    debugPrint('开始异步初始化配置管理器');
    ConfigManager().initialize().then((_) {
      debugPrint('配置管理器初始化完成');
    }).catchError((e) {
      debugPrint('配置管理器初始化失败: $e');
    });
  }

  /// 启动性能优化（异步执行，不阻塞应用启动）
  void _startPerformanceOptimization() {
    // 使用 Future.microtask 确保在下一个事件循环中执行
    Future.microtask(() async {
      final performanceService = PerformanceService();

      try {
        // 启动性能监控
        await performanceService.startMonitoring();

        // 执行着色器预热
        await performanceService.warmUpShaders();

        // 打印状态报告
        if (kDebugMode) {
          performanceService.printStatusReport();
        }

      } catch (e) {
        debugPrint('❌ AppInitializer - 性能优化失败: $e');
      }
    });
  }

  /// Initialize services, plugins, etc. after the app runs.
  Future<void> postAppRun() async {
    // Hide RSOD in release mode.
    if (kReleaseMode) {
      ErrorWidget.builder = (FlutterErrorDetails details) => const SizedBox();
    }
  }
}
