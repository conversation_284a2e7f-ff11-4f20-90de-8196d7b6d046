import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';

/// 🎯 着色器预热工具
///
/// 功能特性：
/// - ✅ 预热常用的着色器效果
/// - ✅ 减少首次渲染卡顿
/// - ✅ 支持自定义预热场景
/// - ✅ 异步预热，不阻塞启动
class AppShaderWarmUp {
  static bool _isWarmedUp = false;
  static bool _isWarming = false;

  /// 执行着色器预热
  static Future<void> warmUp() async {
    if (_isWarmedUp || _isWarming) return;
    
    _isWarming = true;
    debugPrint('🔥 ShaderWarmUp - 开始着色器预热...');
    
    try {
      // 创建离屏渲染上下文
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      const size = Size(100, 100);
      
      // 预热基础绘制操作
      await _warmUpBasicDrawing(canvas, size);
      
      // 预热动画相关着色器
      await _warmUpAnimationShaders(canvas, size);
      
      // 预热UI组件着色器
      await _warmUpUIComponentShaders(canvas, size);
      
      // 完成预热
      final picture = recorder.endRecording();
      final image = await picture.toImage(size.width.toInt(), size.height.toInt());
      
      // 清理资源
      image.dispose();
      picture.dispose();
      
      _isWarmedUp = true;
      debugPrint('✅ ShaderWarmUp - 着色器预热完成');
    } catch (e) {
      debugPrint('❌ ShaderWarmUp - 着色器预热失败: $e');
    } finally {
      _isWarming = false;
    }
  }

  /// 预热基础绘制操作
  static Future<void> _warmUpBasicDrawing(Canvas canvas, Size size) async {
    // 基础形状绘制
    final paint = Paint();
    
    // 实心矩形
    paint.color = Colors.blue;
    canvas.drawRect(Rect.fromLTWH(0, 0, 50, 50), paint);
    
    // 圆角矩形
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(10, 10, 30, 30),
        const Radius.circular(8),
      ),
      paint,
    );
    
    // 圆形
    paint.color = Colors.red;
    canvas.drawCircle(const Offset(25, 25), 15, paint);
    
    // 线条
    paint.strokeWidth = 2;
    paint.style = PaintingStyle.stroke;
    canvas.drawLine(const Offset(0, 0), const Offset(50, 50), paint);
  }

  /// 预热动画相关着色器
  static Future<void> _warmUpAnimationShaders(Canvas canvas, Size size) async {
    final paint = Paint();
    
    // 渐变效果
    paint.shader = const LinearGradient(
      colors: [Colors.blue, Colors.red],
    ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);
    
    // 径向渐变
    paint.shader = const RadialGradient(
      colors: [Colors.white, Colors.black],
    ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    canvas.drawCircle(Offset(size.width / 2, size.height / 2), 30, paint);
    
    // 阴影效果
    paint.shader = null;
    paint.color = Colors.black.withOpacity(0.3);
    paint.maskFilter = const MaskFilter.blur(BlurStyle.normal, 5);
    canvas.drawRect(Rect.fromLTWH(10, 10, 40, 40), paint);
  }

  /// 预热UI组件着色器
  static Future<void> _warmUpUIComponentShaders(Canvas canvas, Size size) async {
    final paint = Paint();
    
    // 按钮样式效果
    paint.color = Colors.blue;
    paint.style = PaintingStyle.fill;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(5, 5, 40, 20),
        const Radius.circular(10),
      ),
      paint,
    );
    
    // 边框效果
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1;
    paint.color = Colors.grey;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(5, 30, 40, 20),
        const Radius.circular(5),
      ),
      paint,
    );
    
    // 文本渲染（模拟）
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'Test',
        style: TextStyle(color: Colors.black, fontSize: 12),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, const Offset(10, 60));
    textPainter.dispose();
  }

  /// 预热键盘动画着色器
  static Future<void> warmUpKeyboardAnimation() async {
    debugPrint('🔥 ShaderWarmUp - 预热键盘动画着色器...');
    
    try {
      // 模拟键盘弹出动画的着色器
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      
      // 模拟键盘背景
      final paint = Paint()..color = Colors.white;
      canvas.drawRect(const Rect.fromLTWH(0, 200, 400, 200), paint);
      
      // 模拟键盘阴影
      paint.color = Colors.black.withOpacity(0.1);
      paint.maskFilter = const MaskFilter.blur(BlurStyle.normal, 10);
      canvas.drawRect(const Rect.fromLTWH(0, 190, 400, 10), paint);
      
      final picture = recorder.endRecording();
      final image = await picture.toImage(400, 400);
      
      image.dispose();
      picture.dispose();
      
      debugPrint('✅ ShaderWarmUp - 键盘动画着色器预热完成');
    } catch (e) {
      debugPrint('❌ ShaderWarmUp - 键盘动画着色器预热失败: $e');
    }
  }

  /// 预热列表滚动着色器
  static Future<void> warmUpListScrollShaders() async {
    debugPrint('🔥 ShaderWarmUp - 预热列表滚动着色器...');
    
    try {
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      
      // 模拟列表项
      final paint = Paint();
      for (int i = 0; i < 10; i++) {
        paint.color = i % 2 == 0 ? Colors.white : Colors.grey[100]!;
        canvas.drawRect(Rect.fromLTWH(0, i * 60.0, 400, 60), paint);
        
        // 模拟分割线
        paint.color = Colors.grey[300]!;
        canvas.drawRect(Rect.fromLTWH(0, i * 60.0 + 59, 400, 1), paint);
      }
      
      // 模拟滚动指示器
      paint.color = Colors.grey;
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          const Rect.fromLTWH(395, 50, 5, 100),
          const Radius.circular(2.5),
        ),
        paint,
      );
      
      final picture = recorder.endRecording();
      final image = await picture.toImage(400, 600);
      
      image.dispose();
      picture.dispose();
      
      debugPrint('✅ ShaderWarmUp - 列表滚动着色器预热完成');
    } catch (e) {
      debugPrint('❌ ShaderWarmUp - 列表滚动着色器预热失败: $e');
    }
  }

  /// 重置预热状态（用于测试）
  static void reset() {
    _isWarmedUp = false;
    _isWarming = false;
  }

  /// 检查是否已预热
  static bool get isWarmedUp => _isWarmedUp;
}
