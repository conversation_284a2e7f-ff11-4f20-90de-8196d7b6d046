import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

/// 🎯 应用性能监控工具
/// 
/// 功能特性：
/// - ✅ 监控帧率和渲染性能
/// - ✅ 检测卡顿和掉帧
/// - ✅ 内存使用监控
/// - ✅ 启动时间统计
/// - ✅ 性能报告生成
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  // 监控状态
  bool _isMonitoring = false;
  Timer? _monitorTimer;
  
  // 性能数据
  final List<double> _frameRates = [];
  final List<Duration> _frameDurations = [];
  int _droppedFrames = 0;
  DateTime? _appStartTime;
  DateTime? _firstFrameTime;
  
  // 配置参数
  static const int _maxDataPoints = 100;
  static const Duration _monitorInterval = Duration(seconds: 1);
  static const double _targetFps = 60.0;
  static const Duration _frameThreshold = Duration(milliseconds: 16); // 60fps = 16ms per frame

  /// 开始性能监控
  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    _appStartTime = DateTime.now();
    
    debugPrint('🎯 PerformanceMonitor - 开始性能监控');
    
    // 监控帧率
    _startFrameRateMonitoring();
    
    // 定期收集性能数据
    _monitorTimer = Timer.periodic(_monitorInterval, (_) {
      _collectPerformanceData();
    });
    
    // 监控首帧渲染
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _firstFrameTime ??= DateTime.now();
      final startupTime = _firstFrameTime!.difference(_appStartTime!);
      debugPrint('🚀 PerformanceMonitor - 首帧渲染时间: ${startupTime.inMilliseconds}ms');
    });
  }

  /// 停止性能监控
  void stopMonitoring() {
    if (!_isMonitoring) return;
    
    _isMonitoring = false;
    _monitorTimer?.cancel();
    _monitorTimer = null;
    
    debugPrint('🎯 PerformanceMonitor - 停止性能监控');
    _generatePerformanceReport();
  }

  /// 开始帧率监控
  void _startFrameRateMonitoring() {
    SchedulerBinding.instance.addTimingsCallback((timings) {
      if (!_isMonitoring) return;
      
      for (final timing in timings) {
        final frameDuration = timing.totalSpan;
        _frameDurations.add(frameDuration);
        
        // 计算帧率
        final fps = 1000000 / frameDuration.inMicroseconds; // 1秒 = 1,000,000微秒
        _frameRates.add(fps);
        
        // 检测掉帧
        if (frameDuration > _frameThreshold) {
          _droppedFrames++;
          if (kDebugMode) {
            debugPrint('⚠️ PerformanceMonitor - 检测到掉帧: ${frameDuration.inMilliseconds}ms (${fps.toStringAsFixed(1)} fps)');
          }
        }
        
        // 限制数据点数量
        if (_frameRates.length > _maxDataPoints) {
          _frameRates.removeAt(0);
          _frameDurations.removeAt(0);
        }
      }
    });
  }

  /// 收集性能数据
  void _collectPerformanceData() {
    if (_frameRates.isEmpty) return;
    
    final currentFps = _frameRates.last;
    final averageFps = _frameRates.reduce((a, b) => a + b) / _frameRates.length;
    
    // 检查性能警告
    if (currentFps < _targetFps * 0.8) { // 低于48fps
      debugPrint('⚠️ PerformanceMonitor - 性能警告: 当前帧率 ${currentFps.toStringAsFixed(1)} fps');
    }
    
    if (kDebugMode && _frameRates.length % 10 == 0) {
      debugPrint('📊 PerformanceMonitor - 平均帧率: ${averageFps.toStringAsFixed(1)} fps, 掉帧数: $_droppedFrames');
    }
  }

  /// 生成性能报告
  void _generatePerformanceReport() {
    if (_frameRates.isEmpty) return;
    
    final report = PerformanceReport(
      averageFps: _frameRates.reduce((a, b) => a + b) / _frameRates.length,
      minFps: _frameRates.reduce((a, b) => a < b ? a : b),
      maxFps: _frameRates.reduce((a, b) => a > b ? a : b),
      droppedFrames: _droppedFrames,
      totalFrames: _frameRates.length,
      startupTime: _firstFrameTime?.difference(_appStartTime ?? DateTime.now()) ?? Duration.zero,
      monitoringDuration: DateTime.now().difference(_appStartTime ?? DateTime.now()),
    );
    
    debugPrint('📊 PerformanceMonitor - 性能报告:');
    debugPrint('   平均帧率: ${report.averageFps.toStringAsFixed(1)} fps');
    debugPrint('   最低帧率: ${report.minFps.toStringAsFixed(1)} fps');
    debugPrint('   最高帧率: ${report.maxFps.toStringAsFixed(1)} fps');
    debugPrint('   掉帧数量: ${report.droppedFrames}/${report.totalFrames}');
    debugPrint('   掉帧率: ${report.droppedFrameRate.toStringAsFixed(2)}%');
    debugPrint('   启动时间: ${report.startupTime.inMilliseconds}ms');
    debugPrint('   监控时长: ${report.monitoringDuration.inSeconds}s');
    
    // 性能评级
    final grade = _calculatePerformanceGrade(report);
    debugPrint('   性能评级: $grade');
    
    // 优化建议
    final suggestions = _generateOptimizationSuggestions(report);
    if (suggestions.isNotEmpty) {
      debugPrint('   优化建议:');
      for (final suggestion in suggestions) {
        debugPrint('   - $suggestion');
      }
    }
  }

  /// 计算性能评级
  String _calculatePerformanceGrade(PerformanceReport report) {
    if (report.averageFps >= 55 && report.droppedFrameRate < 5) {
      return '优秀 (A)';
    } else if (report.averageFps >= 45 && report.droppedFrameRate < 10) {
      return '良好 (B)';
    } else if (report.averageFps >= 35 && report.droppedFrameRate < 20) {
      return '一般 (C)';
    } else {
      return '需要优化 (D)';
    }
  }

  /// 生成优化建议
  List<String> _generateOptimizationSuggestions(PerformanceReport report) {
    final suggestions = <String>[];
    
    if (report.averageFps < 45) {
      suggestions.add('考虑减少UI复杂度，使用RepaintBoundary隔离重绘区域');
      suggestions.add('检查是否有不必要的rebuild，使用const构造函数');
    }
    
    if (report.droppedFrameRate > 15) {
      suggestions.add('检查主线程是否有阻塞操作，考虑使用Isolate');
      suggestions.add('优化列表渲染，使用ListView.builder而不是ListView');
    }
    
    if (report.startupTime.inMilliseconds > 2000) {
      suggestions.add('优化应用启动流程，延迟非关键初始化');
      suggestions.add('考虑使用着色器预热减少首次渲染卡顿');
    }
    
    return suggestions;
  }

  /// 记录自定义性能事件
  void recordEvent(String eventName, {Map<String, dynamic>? data}) {
    if (!_isMonitoring) return;
    
    developer.Timeline.instantSync(eventName, arguments: data);
    debugPrint('🎯 PerformanceMonitor - 事件: $eventName ${data != null ? data.toString() : ''}');
  }

  /// 开始性能追踪
  void startTrace(String traceName) {
    if (!_isMonitoring) return;
    
    developer.Timeline.startSync(traceName);
  }

  /// 结束性能追踪
  void endTrace(String traceName) {
    if (!_isMonitoring) return;
    
    developer.Timeline.finishSync();
  }

  /// 获取当前性能状态
  PerformanceStatus get currentStatus {
    if (_frameRates.isEmpty) {
      return PerformanceStatus.unknown;
    }
    
    final currentFps = _frameRates.last;
    if (currentFps >= 55) {
      return PerformanceStatus.excellent;
    } else if (currentFps >= 45) {
      return PerformanceStatus.good;
    } else if (currentFps >= 30) {
      return PerformanceStatus.fair;
    } else {
      return PerformanceStatus.poor;
    }
  }

  /// 重置监控数据
  void reset() {
    _frameRates.clear();
    _frameDurations.clear();
    _droppedFrames = 0;
    _appStartTime = null;
    _firstFrameTime = null;
  }
}

/// 性能报告数据类
class PerformanceReport {
  final double averageFps;
  final double minFps;
  final double maxFps;
  final int droppedFrames;
  final int totalFrames;
  final Duration startupTime;
  final Duration monitoringDuration;

  const PerformanceReport({
    required this.averageFps,
    required this.minFps,
    required this.maxFps,
    required this.droppedFrames,
    required this.totalFrames,
    required this.startupTime,
    required this.monitoringDuration,
  });

  double get droppedFrameRate => totalFrames > 0 ? (droppedFrames / totalFrames) * 100 : 0;
}

/// 性能状态枚举
enum PerformanceStatus {
  unknown,
  excellent,
  good,
  fair,
  poor,
}
