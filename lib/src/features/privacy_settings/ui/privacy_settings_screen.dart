import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:flutter_kit/src/features/resume/theme/resume_theme.dart';
import '../logic/privacy_settings_logic.dart';

/// 🔥 隐私设置页面 - 遵循项目架构规范
/// 
/// 功能特性：
/// - ✅ 3个设置项：个性化推荐、推送信息、通知信息
/// - ✅ 左右结构布局，左侧标题+副标题，右侧Switch组件
/// - ✅ 卡片式布局设计
/// - ✅ 使用Logic + ChangeNotifier状态管理
class PrivacySettingsScreen extends StatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  State<PrivacySettingsScreen> createState() => _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends State<PrivacySettingsScreen> {
  late PrivacySettingsLogic _logic;

  @override
  void initState() {
    super.initState();
    _logic = PrivacySettingsLogic();
    _loadSettings();
  }

  void _loadSettings() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _logic.loadSettings();
    });
  }

  @override
  void dispose() {
    _logic.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _logic,
      child: Scaffold(
        backgroundColor: ResumeTheme.backgroundColor,
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  /// 🔥 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: ResumeTheme.surfaceColor,
      elevation: 0,
      title: Text(
        '隐私设置',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: ResumeTheme.textPrimary,
        ),
      ),
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: Icon(
          Icons.arrow_back_ios,
          color: ResumeTheme.textPrimary,
          size: 20.w,
        ),
      ),
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(1.h),
        child: Container(
          height: 1.h,
          color: ResumeTheme.borderColor,
        ),
      ),
    );
  }

  /// 🔥 构建页面主体
  Widget _buildBody() {
    return Consumer<PrivacySettingsLogic>(
      builder: (context, logic, child) {
        return SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            children: [
              SizedBox(height: 8.h),
              // 隐私设置卡片
              _buildPrivacySettingsCard(logic),
              SizedBox(height: 16.h),
              // 说明文本
              _buildDescriptionCard(),
            ],
          ),
        );
      },
    );
  }

  /// 🔥 构建隐私设置卡片
  Widget _buildPrivacySettingsCard(PrivacySettingsLogic logic) {
    return Container(
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        children: [
          // 个性化推荐设置
          _buildSettingItem(
            icon: Icons.recommend,
            title: '个性化推荐设置',
            subtitle: '根据您的浏览和求职记录，为您推荐合适的职位',
            value: logic.personalizedRecommendation,
            onChanged: (value) => logic.updatePersonalizedRecommendation(value),
          ),
          _buildDivider(),
          // 推送信息设置
          _buildSettingItem(
            icon: Icons.notifications_active,
            title: '推送信息设置',
            subtitle: '接收职位推荐、面试邀请等重要消息推送',
            value: logic.pushNotification,
            onChanged: (value) => logic.updatePushNotification(value),
          ),
          _buildDivider(),
          // 通知信息设置
          _buildSettingItem(
            icon: Icons.message,
            title: '通知信息设置',
            subtitle: '接收系统通知、活动消息等一般性信息',
            value: logic.generalNotification,
            onChanged: (value) => logic.updateGeneralNotification(value),
            isLast: true,
          ),
        ],
      ),
    );
  }

  /// 🔥 构建设置项
  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
    bool isLast = false,
  }) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          // 图标
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: ResumeTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              icon,
              color: ResumeTheme.primaryColor,
              size: 20.w,
            ),
          ),
          SizedBox(width: 12.w),
          // 标题和副标题
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: ResumeTheme.textPrimary,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ResumeTheme.textSecondary,
                    height: 1.3,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          SizedBox(width: 12.w),
          // Switch开关
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: ResumeTheme.primaryColor,
            activeTrackColor: ResumeTheme.primaryColor.withOpacity(0.3),
            inactiveThumbColor: Colors.grey[400],
            inactiveTrackColor: Colors.grey[300],
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  /// 🔥 构建分割线
  Widget _buildDivider() {
    return Container(
      height: 1.h,
      margin: EdgeInsets.only(left: 68.w),
      color: ResumeTheme.borderColor,
    );
  }

  /// 🔥 构建说明卡片
  Widget _buildDescriptionCard() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ResumeTheme.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: ResumeTheme.primaryColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: ResumeTheme.primaryColor,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '隐私说明',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: ResumeTheme.primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            '• 个性化推荐：基于您的求职偏好和浏览记录，为您智能推荐匹配的职位\n'
            '• 推送通知：及时接收重要的求职相关消息，如面试邀请、职位推荐等\n'
            '• 一般通知：接收平台活动、功能更新等非紧急信息\n'
            '• 您可以随时在此页面调整这些设置，我们严格保护您的隐私数据',
            style: TextStyle(
              fontSize: 12.sp,
              color: ResumeTheme.textSecondary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}
