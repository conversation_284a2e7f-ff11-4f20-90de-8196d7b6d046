import 'package:flutter_kit/src/base/logic/view_state_load_logic.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';

/// 发现页面逻辑
class DiscoverLogic extends ViewStateLoadLogic {
  List<String> _discoverItems = [];
  
  List<String> get discoverItems => _discoverItems;
  
  @override
  void loadData() {
    // 模拟加载发现页面数据
    setLoading();
    
    // 模拟网络请求延迟
    Future.delayed(const Duration(milliseconds: 500), () {
      _discoverItems = [
        '热门话题',
        '推荐内容',
        '最新动态',
        '精选文章',
        '视频推荐',
      ];
      setSuccess(_discoverItems);
    });
  }
  
  /// 刷新数据
  void refreshData() {
    loadData();
  }
}
