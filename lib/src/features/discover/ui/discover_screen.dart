import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/widgets/view_state_load_widget.dart';
import 'package:flutter_kit/src/base/widgets/view_state_widget.dart';
import 'package:flutter_kit/src/features/discover/logic/discover_logic.dart';
import 'package:flutter_kit/src/features/discover/ui/widget/discover_view.dart';
import 'package:flutter_kit/src/shared/locator.dart';

@RoutePage()
class DiscoverScreen extends ViewStateLoadWidget<DiscoverLogic> {
  const DiscoverScreen({super.key});

  @override
  DiscoverLogic createController() {
    return locator<DiscoverLogic>();
  }

  @override
  PreferredSizeWidget? buildAppBar(BuildContext context, DiscoverLogic logic) {
    return null;
  }

  @override
  Widget buildBody(BuildContext context, DiscoverLogic logic) {
    // 使用新的DiscoveryPageView替代原来的简单列表
    return const DiscoveryPageView();
  }

  @override
  bool useScaffold() => false; // 不使用外层Scaffold，让DiscoveryPageView自己处理SafeArea
}
