import 'package:flutter/material.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';
import 'package:flutter_kit/src/shared/components/custom_list_view/custom_list_view_export.dart';
import '../../../base/base.dart';
import '../../../datasource/repositories/home_repository.dart';
import '../data/home_data_source.dart';

/// 🔥 首页业务逻辑 - 遵循项目架构规范
///
/// 功能特性：
/// - ✅ 继承ViewStatePagingLogic，符合项目架构
/// - ✅ 集成CustomListLogic进行列表管理
/// - ✅ 使用HomeDataSource进行数据获取
/// - ✅ 支持分页加载和下拉刷新
/// - ✅ 统一的错误处理和状态管理
class HomeLogic extends ViewStateLogic {
  final HomeReportsitory repository;
  late CustomListLogic<JobInfoEntity> listLogic;
  late CustomListDataSource<JobInfoEntity> dataSource;


  // 🔥 AppBar透明度状态管理
  double _appBarOpacity = 0.0;
  double _lastOpacity = 0.0;

  double get appBarOpacity => _appBarOpacity;

  HomeLogic({required this.repository}) {
    _initializeDataSource();
    _initializeListLogic();
  }

  /// 初始化数据源
  void _initializeDataSource() {
    dataSource = HomeDataSource(
      repository: repository,
      pageSize: 20,
    );
  }

  /// 初始化列表逻辑
  void _initializeListLogic() {
    listLogic = CustomListLogic<JobInfoEntity>(
      dataSource: dataSource,
    );

  }



  @override
  void loadData() {
    listLogic.loadData();
  }

  void refreshPaging() {
    listLogic.refreshPaging();
  }

  /// 兼容性方法
  Future<void> fetchJobs() async {
    loadData();
  }

  /// 🔥 更新AppBar透明度
  void updateAppBarOpacity(double scrollOffset) {
    // 计算透明度，添加防抖机制
    final double newOpacity = (scrollOffset / 100.0).clamp(0.0, 1.0);

    // 只有透明度变化超过阈值时才更新，减少不必要的重绘
    if ((newOpacity - _lastOpacity).abs() > 0.01) {
      _lastOpacity = newOpacity;
      _appBarOpacity = newOpacity;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    listLogic.dispose();
    super.dispose();
  }
}
