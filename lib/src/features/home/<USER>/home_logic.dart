import 'package:flutter/material.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';
import 'package:flutter_kit/src/shared/components/custom_list_view/custom_list_view_export.dart';
import '../../../base/base.dart';
import '../../../datasource/repositories/home_repository.dart';
import '../data/home_data_source.dart';

/// 🔥 首页业务逻辑 - 遵循项目架构规范
///
/// 功能特性：
/// - ✅ 继承ViewStatePagingLogic，符合项目架构
/// - ✅ 集成CustomListLogic进行列表管理
/// - ✅ 使用HomeDataSource进行数据获取
/// - ✅ 支持分页加载和下拉刷新
/// - ✅ 统一的错误处理和状态管理
class HomeLogic extends ViewStateLogic {
  final HomeReportsitory repository;
  late CustomListLogic<JobInfoEntity> listLogic;
  late CustomListDataSource<JobInfoEntity> dataSource;

  // 兼容性回调（保持原有接口）
  VoidCallback? onRefreshSuccess;
  VoidCallback? onRefreshStart;
  VoidCallback? onRefreshEnd;

  // 🔥 状态跟踪 - 区分刷新和加载更多
  bool _wasRefreshing = false;
  bool _wasLoadingMore = false;

  HomeLogic({required this.repository}) {
    _initializeDataSource();
    _initializeListLogic();
  }

  /// 初始化数据源
  void _initializeDataSource() {
    dataSource = HomeDataSource(
      repository: repository,
      pageSize: 20,
    );
  }

  /// 初始化列表逻辑
  void _initializeListLogic() {
    listLogic = CustomListLogic<JobInfoEntity>(
      dataSource: dataSource,
    );

    // 监听列表状态变化，同步到当前Logic
    listLogic.addListener(_onListStateChanged);
  }

  /// 列表状态变化监听
  void _onListStateChanged() {
    // 同步ViewState
    viewState = listLogic.viewState;

    // 🔥 检测刷新状态变化
    if (listLogic.isRefreshing && !_wasRefreshing) {
      // 开始刷新
      if (onRefreshStart != null) {
        onRefreshStart!();
      }
    } else if (!listLogic.isRefreshing && _wasRefreshing) {
      // 刷新结束
      if (onRefreshEnd != null) {
        onRefreshEnd!();
      }
      // 🔥 只有刷新完成且有数据时才触发刷新成功回调
      if (listLogic.items.isNotEmpty && onRefreshSuccess != null) {
        onRefreshSuccess!();
      }
    }

    // 🔥 检测加载更多状态变化（不触发刷新成功消息）
    if (listLogic.isLoadingMore && !_wasLoadingMore) {
      // 开始加载更多 - 可以在这里添加加载更多开始回调
    } else if (!listLogic.isLoadingMore && _wasLoadingMore) {
      // 加载更多结束 - 可以在这里添加加载更多完成回调
    }

    // 🔥 更新状态跟踪
    _wasRefreshing = listLogic.isRefreshing;
    _wasLoadingMore = listLogic.isLoadingMore;

    notifyListeners();
  }

  // Getters - 兼容性接口
  List<JobInfoEntity> get jobs => listLogic.items;
  bool get hasMore => listLogic.hasMore;
  int get totalCount => listLogic.totalCount;
  bool get isRefreshing => listLogic.isRefreshing;
  bool get isLoadingMore => listLogic.isLoadingMore;

  @override
  void loadData() {
    listLogic.loadData();
  }

  @override
  void refreshPaging() {
    listLogic.refreshPaging();
  }

  @override
  void loadMorePaging() {
    listLogic.loadMorePaging();
  }

  /// 兼容性方法
  Future<void> fetchJobs() async {
    loadData();
  }

  @override
  void dispose() {
    listLogic.removeListener(_onListStateChanged);
    listLogic.dispose();
    super.dispose();
  }
}
