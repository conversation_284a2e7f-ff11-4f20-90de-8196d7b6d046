import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/base.dart';
import 'package:flutter_kit/src/base/widgets/view_state_load_widget.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import 'package:flutter_kit/src/features/home/<USER>/job_list_item.dart';
import 'package:flutter_kit/src/shared/extensions/asset_ext.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:flutter_kit/src/shared/utils/safe_area_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../datasource/models/job_info_entity.dart';
import '../../../shared/components/custom_list_view/custom_list_view_export.dart';
import '../logic/home_logic.dart';

@RoutePage()
class HomeScreenNew extends ViewStateWidget<HomeLogic> {
  const HomeScreenNew({super.key});

  @override
  HomeLogic createController() {
    return locator<HomeLogic>();
  }

  @override
  Widget? buildCustomAppBar(BuildContext context, HomeLogic logic) {
    return null;
  }

  @override
  Widget buildBody(BuildContext context, HomeLogic logic) {

    // 🔥 为CustomListLogic提供Provider
    return ChangeNotifierProvider<CustomListLogic<JobInfoEntity>>.value(
      value: logic.listLogic,
      child: _buildContent(context, logic),
    );
  }

  /// 🔥 构建页面内容
  Widget _buildContent(BuildContext context, HomeLogic logic) {
    final ValueNotifier<bool> isRefreshing = ValueNotifier<bool>(false);

    // 设置刷新开始回调
    logic.onRefreshStart ??= () {
      isRefreshing.value = true;
    };

    // 设置刷新结束回调
    logic.onRefreshEnd ??= () {
      isRefreshing.value = false;
    };



    return Stack(
      children: [
        // 背景图片
        Column(
          children: [
            Image.asset(
              'bg_index'.png,
              width: double.infinity,
              height: 240.w,
              fit: BoxFit.cover,
            ),
            Expanded(child: Container(color: AppColors.color_background))
          ],
        ),

        // 🔥 页面内容 - 使用CustomListView统一管理滚动
        _buildJobListViewWithHeader(logic),
      ],
    );
  }



  @override
  Widget buildTopLoadingWidget() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 3,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue, Colors.blue.shade300],
          ),
        ),
        child: LinearProgressIndicator(
          backgroundColor: Colors.transparent,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white.withValues(alpha: 0.8)),
        ),
      ),
    );
  }

  Widget _buildTitleBar() {
    return Container(
      padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 12.w),
      child: Row(
        children: [
          Row(
            children: [
              Icon(Icons.location_on,
                  size: 14.w, color: AppColors.color_333333),
              SizedBox(width: 2.w),
              Text(
                '汕头',
                style: TextStyle(
                  fontSize: 13.w,
                  fontWeight: FontWeight.bold,
                  color: AppColors.color_333333,
                ),
              ),
              SizedBox(width: 0.w),
              Icon(Icons.arrow_drop_down,
                  size: 20.w, color: AppColors.color_333333),
            ],
          ),
          SizedBox(width: 6.w),
          Expanded(
            child: Card(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.w)),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
                child: Row(
                  children: [
                    Icon(Icons.search,
                        size: 20.w, color: AppColors.color_333333),
                    SizedBox(width: 6),
                    Expanded(
                        child: Text(
                      '搜索职位名称',
                      style: TextStyle(
                          fontSize: 15.w, color: AppColors.color_cccccc),
                    )),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabs() {
    final List<Map<String, String>> tabs = [
      {'text': '全职', 'icon': 'ic_ygz'},
      {'text': '兼职', 'icon': 'ic_ygz'},
      {'text': '普工', 'icon': 'ic_ygz'},
      {'text': '销售', 'icon': 'ic_ygz'},
      {'text': '招聘会', 'icon': 'ic_ygz'}
    ];

    return Container(
      margin: EdgeInsets.fromLTRB(12.w, 0, 12.w, 0),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(12.w),
          child: Container(
            height: 80.h,
            color: Colors.white,
            child: Row(
                children: tabs
                    .map((tab) => Expanded(
                            child: Container(
                          margin: EdgeInsets.symmetric(
                              vertical: 8.w, horizontal: 8.w),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                  height: 30.w,
                                  width: 30.w,
                                  child: Image.asset(
                                    'assets/images/${tab['icon']}.png',
                                    fit: BoxFit.cover,
                                  )),
                              SizedBox(height: 6.w),
                              Text(
                                tab['text']!,
                                style: TextStyle(
                                    fontSize: 13.w,
                                    color: AppColors.color_333333,
                                    fontWeight: FontWeight.w600),
                              )
                            ],
                          ),
                        )))
                    .toList()),
          )),
    );
  }

  Widget _buildBanner() {
    return SizedBox(
      height: 160.w,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.w),
        child: PageView(
          children: [
            Image.asset(
              'assets/images/bg_gongsi.jpg',
              fit: BoxFit.cover,
            ),
            Image.asset(
              'assets/images/bg_gongsi.jpg',
              fit: BoxFit.cover,
            )
          ],
        ),
      ),
    );
  }

  Widget _buildJobExpection() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10.w),
      child: Container(
        color: Colors.white,
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 12.w),
          child: Row(
            children: [
              Text(
                '行政专员',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.w),
              ),
              Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                      child: ClipRRect(
                          borderRadius: BorderRadius.circular(14.w),
                          child: Container(
                            color: AppColors.color_ff0027,
                            child: Row(
                              children: [
                                SizedBox(
                                    width: 22.w,
                                    height: 22.w,
                                    child: Image.asset(
                                      'assets/images/ic_zaizhi_yuenei.png',
                                      fit: BoxFit.cover,
                                    )),
                                SizedBox(width: 8.w),
                                Text(
                                  '暂不考虑',
                                  style: TextStyle(
                                      fontSize: 11.w,
                                      color: AppColors.color_666666),
                                ),
                                SizedBox(width: 8.w)
                              ],
                            ),
                          ))),
                  SizedBox(width: 10.w),
                  SizedBox(
                    width: 5.w,
                    child: Image.asset('assets/images/ic_arrow_see.png',
                        fit: BoxFit.cover),
                  )
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  /// 🔥 构建带头部的职位列表视图
  Widget _buildJobListViewWithHeader(HomeLogic logic) {
    return Consumer<CustomListLogic<JobInfoEntity>>(
      builder: (context, listLogic, child) {
        return _JobListViewWithAppBar(logic: logic);
      },
    );
  }
}

/// 🔥 独立的StatefulWidget来管理AppBar透明度状态
class _JobListViewWithAppBar extends StatefulWidget {
  final HomeLogic logic;

  const _JobListViewWithAppBar({required this.logic});

  @override
  State<_JobListViewWithAppBar> createState() => _JobListViewWithAppBarState();
}

class _JobListViewWithAppBarState extends State<_JobListViewWithAppBar> {
  late ValueNotifier<double> _appBarOpacity;
  double _lastOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    _appBarOpacity = ValueNotifier<double>(0.0);
  }

  @override
  void dispose() {
    _appBarOpacity.dispose();
    super.dispose();
  }

  void _updateAppBarOpacity(double offset) {
    // 🔥 计算透明度，添加防抖机制
    final double newOpacity = (offset / 100.0).clamp(0.0, 1.0);

    // 🔥 只有透明度变化超过阈值时才更新，减少不必要的重绘
    if ((newOpacity - _lastOpacity).abs() > 0.01) {
      _lastOpacity = newOpacity;
      _appBarOpacity.value = newOpacity;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 主要内容区域
        NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification notification) {
            if (notification is ScrollUpdateNotification) {
              // 🔥 只处理垂直滚动，忽略Banner的PageView水平滑动
              if (notification.metrics.axis == Axis.vertical) {
                _updateAppBarOpacity(notification.metrics.pixels);
              }
            }
            return false;
          },
          child: CustomListView<JobInfoEntity>(
            dataSource: widget.logic.dataSource,
            externalLogic: widget.logic.listLogic,
            customHeader: _buildContentHeader(context),
            itemBuilder: (context, item, index) {
              return JobListItem(
                item: item,
                index: index,
              );
            },
            enableRefresh: true,
            enableLoadMore: true,
            enableItemAnimation: false, // 🔥 取消滑动特效以提升性能
            separatorBuilder: (context, index) => SizedBox(height: 0.h),
            emptyWidget: _buildEmptyWidget(),
            errorWidget: _buildErrorWidget(),
          ),
        ),
        // 🔥 固定在顶部的AppBar
        _buildFixedAppBar(),
      ],
    );
  }

  /// 🔥 构建固定在顶部的AppBar
  Widget _buildFixedAppBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: ValueListenableBuilder<double>(
        valueListenable: _appBarOpacity,
        builder: (context, opacity, child) {
          return Container(
            height: kToolbarHeight + MediaQuery.of(context).padding.top,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: opacity),
            ),
            child: SafeArea(
              child: Container(
                height: kToolbarHeight,
                alignment: Alignment.center,
                child: _buildTitleBar(),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 🔥 构建内容头部（Banner、Tabs等，不包含AppBar）
  Widget _buildContentHeader(BuildContext context) {
    final top = kToolbarHeight + MediaQuery.of(context).padding.top + 12.w;
    return Column(
      children: [
        // Banner 部分
        Padding(
          padding: EdgeInsets.fromLTRB(12.w, top, 12.w, 16.w),
          child: _buildBanner(),
        ),
        // Tabs 部分
        _buildTabs(),
        // Job Expectation 部分
        Container(
          margin: EdgeInsets.fromLTRB(12.w, 12.w, 12.w, 6.w),
          child: _buildJobExpection(),
        ),
      ],
    );
  }

  /// 🔥 构建空数据状态组件
  Widget _buildEmptyWidget() {
    return Container(
      padding: EdgeInsets.all(40.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.work_off_outlined,
            size: 64.w,
            color: Colors.grey.shade400,
          ),
          SizedBox(height: 16.h),
          Text(
            '暂无职位信息',
            style: TextStyle(
              fontSize: 16.w,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '请稍后再试或刷新页面',
            style: TextStyle(
              fontSize: 14.w,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  /// 🔥 构建错误状态组件
  Widget _buildErrorWidget() {
    return Container(
      padding: EdgeInsets.all(40.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.w,
            color: Colors.red.shade400,
          ),
          SizedBox(height: 16.h),
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 16.w,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '网络连接异常，请检查网络设置',
            style: TextStyle(
              fontSize: 14.w,
              color: Colors.grey.shade500,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () {
              widget.logic.refreshPaging();
            },
            child: Text('重试'),
          ),
        ],
      ),
    );
  }
  /// 🔥 显示刷新成功消息
  void _showRefreshSuccessMessage(BuildContext context) {
    final overlay = Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 10,
        left: 0,
        right: 0,
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: const Color(0xFFF02E4B),
                borderRadius: BorderRadius.circular(10.w),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.check_circle, color: Colors.white, size: 16.w),
                  SizedBox(width: 8.w),
                  Text(
                    '刷新成功！',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14.w,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);
    Future.delayed(const Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }

  Widget _buildTitleBar() {
    return Container(
      padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 12.w),
      child: Row(
        children: [
          Row(
            children: [
              Icon(Icons.location_on,
                  size: 14.w, color: AppColors.color_333333),
              SizedBox(width: 2.w),
              Text(
                '汕头',
                style: TextStyle(
                  fontSize: 13.w,
                  fontWeight: FontWeight.bold,
                  color: AppColors.color_333333,
                ),
              ),
              SizedBox(width: 0.w),
              Icon(Icons.arrow_drop_down,
                  size: 20.w, color: AppColors.color_333333),
            ],
          ),
          SizedBox(width: 6.w),
          Expanded(
            child: Card(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.w)),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
                child: Row(
                  children: [
                    Icon(Icons.search,
                        size: 20.w, color: AppColors.color_333333),
                    SizedBox(width: 6),
                    Expanded(
                        child: Text(
                          '搜索职位名称',
                          style: TextStyle(
                              fontSize: 15.w, color: AppColors.color_cccccc),
                        )),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBanner() {
    return SizedBox(
      height: 160.w,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.w),
        child: PageView(
          children: [
            Image.asset(
              'assets/images/bg_gongsi.jpg',
              fit: BoxFit.cover,
            ),
            Image.asset(
              'assets/images/bg_gongsi.jpg',
              fit: BoxFit.cover,
            )
          ],
        ),
      ),
    );
  }

  Widget _buildTabs() {
    final tabs = [
      {'icon': 'ic_zhiwei', 'text': '职位'},
      {'icon': 'ic_gongsi', 'text': '公司'},
      {'icon': 'ic_zhaopin', 'text': '招聘会'},
      {'icon': 'ic_gengduo', 'text': '更多'},
    ];

    return Container(
      margin: EdgeInsets.fromLTRB(12.w, 0, 12.w, 0),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(12.w),
          child: Container(
            height: 80.h,
            color: Colors.white,
            child: Row(
                children: tabs
                    .map((tab) => Expanded(
                            child: Container(
                          margin: EdgeInsets.symmetric(
                              vertical: 8.w, horizontal: 8.w),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                  height: 30.w,
                                  width: 30.w,
                                  child: Image.asset(
                                    'assets/images/${tab['icon']}.png',
                                    fit: BoxFit.cover,
                                  )),
                              SizedBox(height: 6.w),
                              Text(
                                tab['text']!,
                                style: TextStyle(
                                    fontSize: 13.w,
                                    color: AppColors.color_333333,
                                    fontWeight: FontWeight.w600),
                              )
                            ],
                          ),
                        )))
                    .toList()),
          )),
    );
  }

  Widget _buildJobExpection() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10.w),
      child: Container(
        color: Colors.white,
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 12.w),
          child: Row(
            children: [
              Text(
                '行政专员',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.w),
              ),
              SizedBox(width: 8.w),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.w),
                decoration: BoxDecoration(
                  color: AppColors.color_f02e4b.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4.w),
                ),
                child: Text(
                  '期望职位',
                  style: TextStyle(
                    color: AppColors.color_f02e4b,
                    fontSize: 12.w,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                '修改',
                style: TextStyle(
                  color: AppColors.color_999999,
                  fontSize: 14.w,
                ),
              ),
              SizedBox(width: 4.w),
              Icon(
                Icons.arrow_forward_ios,
                size: 12.w,
                color: AppColors.color_999999,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
