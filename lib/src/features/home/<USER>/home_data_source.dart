import 'package:flutter_kit/src/base/core/base_result.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';
import 'package:flutter_kit/src/shared/components/custom_list_view/custom_list_view_export.dart';
import '../../../datasource/repositories/home_repository.dart';

/// 🎯 首页职位列表数据源
/// 
/// 功能特性：
/// - ✅ 继承ApiListDataSource，符合项目架构
/// - ✅ 封装首页职位数据获取逻辑
/// - ✅ 支持分页加载和数据验证
/// - ✅ 统一的错误处理和重试机制
/// - ✅ 数据转换和缓存支持
class HomeDataSource extends ApiListDataSource<JobInfoEntity> {
  final HomeReportsitory repository;

  HomeDataSource({
    required this.repository,
    super.pageSize = 20,
  }) : super(
          apiCall: (page, pageSize) => _fetchJobList(repository, page, pageSize),
          name: 'HomeJobListDataSource',
        );

  /// 获取职位列表数据
  static Future<BaseResult<List<JobInfoEntity>>> _fetchJobList(
    HomeReportsitory repository,
    int page,
    int pageSize,
  ) async {
    try {
      final response = await repository.getHomeList(page, pageSize);
      
      return response.when(
        success: (data, message) {
          return BaseResult.success(data ?? []);
        },
        failure: (message, code, error) {
          return BaseResult.failure(message ?? '获取职位列表失败');
        },
        empty: (message) {
          return BaseResult.empty();
        },
      );
    } catch (e) {
      return BaseResult.failure('网络请求异常: $e');
    }
  }

  @override
  List<JobInfoEntity> transformData(dynamic rawData) {
    if (rawData is List<JobInfoEntity>) {
      return rawData;
    }
    
    if (rawData is List) {
      try {
        return rawData
            .map((item) => item is JobInfoEntity 
                ? item 
                : JobInfoEntity.fromJson(item as Map<String, dynamic>))
            .toList();
      } catch (e) {
        throw Exception('职位数据格式错误: $e');
      }
    }
    throw UnsupportedError('不支持的数据类型: ${rawData.runtimeType}');
  }

  @override
  bool validateData(List<JobInfoEntity> data) {
    if (data.isEmpty) {
      return true; // 空数据也是有效的
    }
    
    // 验证数据完整性
    for (final job in data) {
      if (job.id.isEmpty || job.name.isEmpty) {
        return false;
      }
    }
    return true;
  }

  /// 获取热门职位（可扩展功能）
  Future<BaseResult<List<JobInfoEntity>>> getHotJobs() async {
    try {
      // TODO: 实现热门职位获取逻辑
      return BaseResult.success([]);
    } catch (e) {
      return BaseResult.failure('获取热门职位失败: $e');
    }
  }

  /// 搜索职位（可扩展功能）
  Future<BaseResult<List<JobInfoEntity>>> searchJobs({
    required String keyword,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      // TODO: 实现职位搜索逻辑
      return BaseResult.success([]);
    } catch (e) {
      return BaseResult.failure('搜索职位失败: $e');
    }
  }

  /// 按分类获取职位（可扩展功能）
  Future<BaseResult<List<JobInfoEntity>>> getJobsByCategory({
    required String categoryId,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      // TODO: 实现分类职位获取逻辑
      return BaseResult.success([]);
    } catch (e) {
      return BaseResult.failure('获取分类职位失败: $e');
    }
  }
}
