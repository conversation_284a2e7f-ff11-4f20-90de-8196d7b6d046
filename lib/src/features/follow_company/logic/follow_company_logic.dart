import 'package:flutter/foundation.dart';
import 'package:flutter_kit/src/base/logic/view_state_paging_logic.dart';
import 'package:flutter_kit/src/shared/components/custom_list_view/custom_list_view_export.dart';
import '../models/follow_company_entity.dart';
import '../../../datasource/repositories/follow_company_repository.dart';

/// 🔥 关注企业Logic - 遵循项目架构规范
class FollowCompanyLogic extends ViewStatePagingLogic {
  final FollowCompanyRepository repository;
  late CustomListLogic<FollowCompanyEntity> listLogic;
  late CustomListDataSource<FollowCompanyEntity> dataSource;

  FollowCompanyLogic({required this.repository}) {

  }



  List<FollowCompanyEntity> _generateMockData(int count) {
    return List.generate(count, (index) {
      final companyId = DateTime.now().millisecondsSinceEpoch + index;
      return FollowCompanyEntity(
        id: companyId.toString(),
        companyName: '${_companyNames[index % _companyNames.length]}',
        industry: _industries[index % _industries.length],
        companySize: _companySizes[index % _companySizes.length],
        location: _locations[index % _locations.length],
        companyLogo: 'https://via.placeholder.com/60x60?text=${index + 1}',
        description: '${_descriptions[index % _descriptions.length]}',
        followTime: DateTime.now().subtract(Duration(days: index)),
        jobCount: (index % 20) + 1,
        isVerified: index % 3 == 0,
        tags: _generateTags(index),
      );
    });
  }

  List<String> _generateTags(int index) {
    final allTags = ['上市公司', '外资企业', '国企', '创业公司', '独角兽', '福利好', '发展快', '技术驱动'];
    final tagCount = (index % 3) + 1;
    return allTags.take(tagCount).toList();
  }

  static const List<String> _companyNames = [
    '阿里巴巴集团', '腾讯科技', '字节跳动', '美团', '滴滴出行', '京东集团', '百度', '网易', '小米科技', '华为技术'
  ];

  static const List<String> _industries = [
    '互联网/电子商务', '计算机软件', '移动互联网', '人工智能', '金融科技', '电子商务', '游戏', '教育培训'
  ];

  static const List<String> _companySizes = [
    '10000人以上', '1000-9999人', '500-999人', '100-499人', '50-99人'
  ];

  static const List<String> _locations = [
    '北京', '上海', '深圳', '杭州', '广州', '成都'
  ];

  static const List<String> _descriptions = [
    '致力于让天下没有难做的生意', '科技向善，连接一切', '激发创造，丰富生活', '帮大家吃得更好，生活更好'
  ];

  @override
  void loadData() {
    debugPrint('🔍 FollowCompanyLogic - loadData() called');
    listLogic.refresh();
  }

  @override
  void refreshPaging() {
    debugPrint('🔍 FollowCompanyLogic - refreshPaging() called');
    listLogic.refresh();
  }

  @override
  void loadMorePaging() {
    debugPrint('🔍 FollowCompanyLogic - loadMorePaging() called');
    listLogic.loadMore();
  }

  Future<void> unfollowCompany(String companyId) async {
    try {
      debugPrint('🔍 FollowCompanyLogic - 取消关注: $companyId');
      await Future.delayed(const Duration(milliseconds: 500));
      
      final index = listLogic.findItemIndex((item) => item.id == companyId);
      if (index != -1) {
        listLogic.removeItemAt(index);
        debugPrint('🔍 FollowCompanyLogic - 已从列表移除关注: $companyId');
      }
    } catch (e) {
      debugPrint('🔍 FollowCompanyLogic - 取消关注失败: $e');
    }
  }

  List<FollowCompanyEntity> get items => listLogic.items;
  bool get hasMore => listLogic.hasMore;
  bool get isRefreshing => listLogic.isRefreshing;
  bool get isLoadingMore => listLogic.isLoadingMore;
  bool get isEmpty => listLogic.isEmpty;
  int get itemCount => listLogic.itemCount;

  @override
  void dispose() {
    listLogic.dispose();
    super.dispose();
  }
}
