


import 'package:auto_route/auto_route.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_kit/src/features/follow_company/logic/follow_company_logic.dart';

import '../../../base/base.dart';
import '../../../shared/locator.dart';

@RoutePage()
class FollowCompanyScreen extends ViewStateWidget<FollowCompanyLogic>{
  const FollowCompanyScreen({super.key});

  @override
  Widget? buildCustomAppBar(BuildContext context, FollowCompanyLogic logic) {
    return customTitleBar(context,'关注企业',leftCallback: (){
      context.router.pop();
    });
  }

  @override
  Widget buildBody(BuildContext context, FollowCompanyLogic logic) {
    return
  }

  @override
  FollowCompanyLogic createController() {
    return locator<FollowCompanyLogic>();
  }

}