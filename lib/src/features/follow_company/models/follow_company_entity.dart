/// 🔥 关注企业实体类
class FollowCompanyEntity {
  final String id;
  final String companyName;
  final String industry;
  final String companySize;
  final String location;
  final String? companyLogo;
  final String description;
  final DateTime followTime;
  final int jobCount;
  final bool isVerified;
  final List<String> tags;

  const FollowCompanyEntity({
    required this.id,
    required this.companyName,
    required this.industry,
    required this.companySize,
    required this.location,
    this.companyLogo,
    required this.description,
    required this.followTime,
    required this.jobCount,
    this.isVerified = false,
    this.tags = const [],
  });

  factory FollowCompanyEntity.fromJson(Map<String, dynamic> json) {
    return FollowCompanyEntity(
      id: json['id']?.toString() ?? '',
      companyName: json['companyName']?.toString() ?? '',
      industry: json['industry']?.toString() ?? '',
      companySize: json['companySize']?.toString() ?? '',
      location: json['location']?.toString() ?? '',
      companyLogo: json['companyLogo']?.toString(),
      description: json['description']?.toString() ?? '',
      followTime: _parseDateTime(json['followTime']),
      jobCount: json['jobCount'] ?? 0,
      isVerified: json['isVerified'] == true,
      tags: _parseStringList(json['tags']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'companyName': companyName,
      'industry': industry,
      'companySize': companySize,
      'location': location,
      'companyLogo': companyLogo,
      'description': description,
      'followTime': followTime.toIso8601String(),
      'jobCount': jobCount,
      'isVerified': isVerified,
      'tags': tags,
    };
  }

  String get followTimeDisplay {
    final now = DateTime.now();
    final difference = now.difference(followTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前关注';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前关注';
    } else {
      return '刚刚关注';
    }
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is DateTime) return value;
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        return DateTime.now();
      }
    }
    return DateTime.now();
  }

  static List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) => e?.toString() ?? '').where((s) => s.isNotEmpty).toList();
    }
    return [];
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FollowCompanyEntity && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FollowCompanyEntity(id: $id, companyName: $companyName, industry: $industry)';
  }
}
