import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../shared/widgets/smart_date_picker.dart';
import '../../../location/location.dart';
import 'selectable_item.dart';
import 'common_picker.dart';
import 'multi_select_picker.dart';
import '../../../../shared/config/managers/config_manager.dart';
import '../../../../shared/config/models/config_models.dart';

/// 简历选择器组件集合
class ResumePickers {
  /// 出生年份选择器
  static Widget birthYearPicker({
    required BuildContext context,
    required String? currentValue,
    required Function(String) onChanged,
  }) {
    return SelectableItem(
      label: '出生年份',
      value: currentValue,
      hint: '请选择',
      hintColor: const Color(0xFFCCCCCC),
      isRequired: true,
      onTap: () async {
        final years = List.generate(50, (index) => '${2024 - index}年');
        final result = await CommonPicker.show(
          context: context,
          title: '选择出生年份',
          options: years,
          currentValue: currentValue,
        );
        if (result != null) {
          onChanged(result);
        }
      },
    );
  }

  /// 求职身份选择器
  static Widget jobStatusPicker({
    required BuildContext context,
    required String? currentValue,
    required Function(String) onChanged,
  }) {
    return SelectableItem(
      label: '求职身份',
      value: currentValue,
      hint: '请选择',
      hintColor: const Color(0xFFCCCCCC),
      isRequired: true,
      onTap: () async {
        const options = [
          '在校-找暑期实习',
          '在校-找全职',
          '离职-随时到岗',
          '在职-月内到岗',
          '在职-考虑机会',
        ];
        final result = await CommonPicker.show(
          context: context,
          title: '选择求职身份',
          options: options,
          currentValue: currentValue,
        );
        if (result != null) {
          onChanged(result);
        }
      },
    );
  }

  /// xml选择器
  static Widget xmlPicker({
    required BuildContext context,
    required String? currentValue,
    required Function(String) onChanged,
    required title,
    required label,
    required isRequired,
    required groundName,
    String? errorMessage,                           // 🔥 错误信息
    Function(String?, String)? onValidate,         // 🔥 验证回调
    String? fieldName,                             // 🔥 字段名
  }) {
    return FutureBuilder<String?>(
      future: ConfigManager().getValueByKey(groundName, currentValue ?? ''),
      builder: (context, snapshot) {
        final displayValue = snapshot.data;

        return SelectableItem(
          label: label,
          value: displayValue,
          hint: '请选择',
          hintColor: const Color(0xFFCCCCCC),
          isRequired: isRequired,
          errorMessage: errorMessage,  // 🔥 传递错误信息
          onTap: () async {
            String? currentDisplayValue = currentValue;
            final convertedValue = await ConfigManager()
                .getValueByKey(groundName, currentValue ?? '');
            if (convertedValue != null) {
              currentDisplayValue = convertedValue;
            }

            final result = await CommonPicker.show(
              context: context,
              title: title,
              options:
                  await ConfigManager().getGroupOptionsList(groundName) ?? [],
              currentValue: currentDisplayValue,
            );

            if (result != null) {
              // 将选择的显示值转换为key
              final selectedKey =
                  await ConfigManager().getKeyByValue(groundName, result);
              if (selectedKey != null) {
                onChanged(selectedKey);
                // 🔥 选择后自动验证
                if (onValidate != null && fieldName != null) {
                  if (isRequired && selectedKey.isEmpty) {
                    onValidate!('请选择$label', fieldName!);
                  } else {
                    onValidate!(null, fieldName!);
                  }
                }
              }
            }
          },
        );
      },
    );
  }



  /// 居住地选择器
  static Widget liveAreaPicker({
    required BuildContext context,
    required String? currentValue,
    required Function(AreaSelectionResult) onChanged,
    required title,
    required label,
    required AreaLevel level,
    required isRequired,
  }) {
    return SelectableItem(
      label: label,
      value: currentValue,
      hint: title,
      hintColor: const Color(0xFFCCCCCC),
      isRequired: isRequired,
      onTap: () async {
        final result = await Navigator.of(context).push<AreaSelectionResult>(
          MaterialPageRoute(
            builder: (context) => LocationScreen(
              level: level,
              title: title,
            ),
          ),
        );
        onChanged(result!);
      },
    );
  }

  /// 姓名输入项
  static Widget nameInput({
    required String? currentValue,
    required Function(String) onChanged,
    required VoidCallback onTap,
  }) {
    return SelectableItem(
      label: '姓名',
      value: currentValue,
      hint: '请输入姓名',
      hintColor: const Color(0xFFCCCCCC),
      isRequired: true,
      onTap: onTap,
    );
  }

  /// 🔥 多选XML选择器
  static Widget multiXmlPicker({
    required BuildContext context,
    required List<String>? currentValues,
    required Function(List<String>) onChanged,
    required String title,
    required String label,
    required bool isRequired,
    required String groundName,
    int? maxSelection,
  }) {
    return FutureBuilder<List<String>?>(
      future: _getDisplayValues(groundName, currentValues ?? []),
      builder: (context, snapshot) {
        final displayValues = snapshot.data ?? [];
        final displayText = displayValues.isEmpty
            ? '请选择'
            : displayValues.length == 1
                ? displayValues.first
                : '已选择${displayValues.length}项';

        return SelectableItem(
          label: label,
          value: displayValues.isEmpty ? null : displayText,
          hint: '请选择',
          hintColor: const Color(0xFFCCCCCC),
          isRequired: isRequired,
          onTap: () async {
            // 获取所有选项
            final allOptions = await ConfigManager().getGroupOptionsList(groundName) ?? [];

            // 获取当前选中的显示值
            List<String> currentDisplayValues = [];
            if (currentValues != null && currentValues!.isNotEmpty) {
              for (String key in currentValues!) {
                final displayValue = await ConfigManager().getValueByKey(groundName, key);
                if (displayValue != null) {
                  currentDisplayValues.add(displayValue);
                }
              }
            }

            // 显示多选选择器
            final result = await MultiSelectPicker.show(
              context: context,
              title: title,
              options: allOptions,
              selectedValues: currentDisplayValues,
              maxSelection: maxSelection,
            );

            if (result != null) {
              // 将选择的显示值转换为key
              List<String> selectedKeys = [];
              for (String displayValue in result) {
                final key = await ConfigManager().getKeyByValue(groundName, displayValue);
                if (key != null) {
                  selectedKeys.add(key);
                }
              }
              onChanged(selectedKeys);
            }
          },
        );
      },
    );
  }

  /// 🔥 获取显示值列表
  static Future<List<String>> _getDisplayValues(String groundName, List<String> keys) async {
    List<String> displayValues = [];
    for (String key in keys) {
      final displayValue = await ConfigManager().getValueByKey(groundName, key);
      if (displayValue != null) {
        displayValues.add(displayValue);
      }
    }
    return displayValues;
  }
}

/// 简历选择器工具类
class ResumePickerUtils {
  /// 生成出生年份选项
  static List<String> generateBirthYears({int startYear = 1950, int? endYear}) {
    final currentYear = endYear ?? DateTime.now().year;
    return List.generate(
      currentYear - startYear + 1,
      (index) => '${currentYear - index}年',
    );
  }

  /// 求职身份选项
  static const List<String> jobStatusOptions = [
    '在校-找暑期实习',
    '在校-找全职',
    '离职-随时到岗',
    '在职-月内到岗',
    '在职-考虑机会',
  ];
}
