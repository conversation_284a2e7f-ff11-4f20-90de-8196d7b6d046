import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../shared/widgets/smart_date_picker.dart';
import '../../theme/resume_theme.dart';

/// 可选择项组件
class SelectableItem extends StatelessWidget {
  final String label;
  final String? value;
  final String? hint;
  final Color? hintColor;
  final bool isRequired;
  final VoidCallback onTap;
  final String? errorMessage;  // 🔥 新增：错误信息

  const SelectableItem({
    super.key,
    required this.label,
    this.value,
    this.hint,
    this.hintColor,
    this.isRequired = false,
    required this.onTap,
    this.errorMessage,  // 🔥 新增：错误信息
  });

  @override
  Widget build(BuildContext context) {
    final bool hasValue = value != null && value!.isNotEmpty;
    final bool hasError = errorMessage != null && errorMessage!.isNotEmpty;

    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque, // 确保整个区域都可以点击
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标签
                  _buildLabel(),
                  SizedBox(height: 7.h),
                  // 值或提示
                  _buildValue(hasValue, hasError),
                  // 🔥 错误提示
                  if (hasError) ...[
                    SizedBox(height: 4.h),
                    _buildErrorMessage(),
                  ],
                ],
              ),
            ),
            SizedBox(width: 8.w), // 添加一些间距
            // 箭头图标
            Icon(
              Icons.arrow_forward_ios,
              color: ResumeTheme.textSecondary,
              size: 16.w,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建标签
  Widget _buildLabel() {
    return RichText(
      text: TextSpan(
        style: TextStyle(
          fontSize: 12.sp,
          color: ResumeTheme.textSecondary,
          fontFamily: 'Inter',
        ),
        children: [
          if (isRequired)
            TextSpan(
              text: '* ',
              style: TextStyle(color: ResumeTheme.requiredColor),
            ),
          TextSpan(text: label),
        ],
      ),
    );
  }

  /// 构建值或提示
  Widget _buildValue(bool hasValue, bool hasError) {
    return Text(
      hasValue ? value! : hint ?? '',
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
        color: hasError
            ? ResumeTheme.primaryColor  // 🔥 错误时显示主色调
            : hasValue
                ? ResumeTheme.textPrimary
                : (hintColor ?? ResumeTheme.textPlaceholder),
      ),
    );
  }

  /// 🔥 构建错误提示
  Widget _buildErrorMessage() {
    return Text(
      errorMessage!,
      style: TextStyle(
        fontSize: 12.sp,
        color: ResumeTheme.primaryColor,  // 🔥 使用主色调显示错误
        fontWeight: FontWeight.w400,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }
}


/// 可选择项组件
class SelectableDataItem extends StatelessWidget {
  final String label;
  final String? value;
  final String? hint;
  final Color? hintColor;
  final bool isRequired;
  final VoidCallback onTap;
  final String? errorMessage;  // 🔥 新增：错误信息
  final DateTime? currDate;
  final DatePickerConfig? config;

  const SelectableDataItem({
    super.key,
    required this.label,
    this.value,
    this.hint,
    this.hintColor,
    this.currDate,
    this.config,
    this.isRequired = false,
    required this.onTap,
    this.errorMessage,  // 🔥 新增：错误信息
  });

  @override
  Widget build(BuildContext context) {
    final bool hasValue = value != null && value!.isNotEmpty;
    final bool hasError = errorMessage != null && errorMessage!.isNotEmpty;

    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque, // 确保整个区域都可以点击
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标签
                  _buildLabel(),
                  SizedBox(height: 7.h),
                  // 值或提示
                  _buildValue(hasValue, hasError),
                  // 🔥 错误提示
                  if (hasError) ...[
                    SizedBox(height: 4.h),
                    _buildErrorMessage(),
                  ],
                ],
              ),
            ),
            SizedBox(width: 8.w), // 添加一些间距
            // 箭头图标
            Icon(
              Icons.arrow_forward_ios,
              color: ResumeTheme.textSecondary,
              size: 16.w,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建标签
  Widget _buildLabel() {
    return RichText(
      text: TextSpan(
        style: TextStyle(
          fontSize: 12.sp,
          color: ResumeTheme.textSecondary,
          fontFamily: 'Inter',
        ),
        children: [
          if (isRequired)
            TextSpan(
              text: '* ',
              style: TextStyle(color: ResumeTheme.requiredColor),
            ),
          TextSpan(text: label),
        ],
      ),
    );
  }

  /// 构建值或提示
  Widget _buildValue(bool hasValue, bool hasError) {
    return Text(
      hasValue ? value! : hint ?? '',
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
        color: hasError
            ? ResumeTheme.primaryColor  // 🔥 错误时显示主色调
            : hasValue
            ? ResumeTheme.textPrimary
            : (hintColor ?? ResumeTheme.textPlaceholder),
      ),
    );
  }

  /// 🔥 构建错误提示
  Widget _buildErrorMessage() {
    return Text(
      errorMessage!,
      style: TextStyle(
        fontSize: 12.sp,
        color: ResumeTheme.primaryColor,  // 🔥 使用主色调显示错误
        fontWeight: FontWeight.w400,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }
}