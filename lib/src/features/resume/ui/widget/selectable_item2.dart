import 'package:flutter/material.dart';
import 'package:flutter_kit/src/shared/utils/safe_area_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../theme/resume_theme.dart';
import 'package:flutter_kit/src/shared/utils/validation_utils.dart';

/// 输入类型枚举
enum InputType {
  textField,  // 文本框 - 适用于小文本
  textArea,   // 文本域 - 适用于大量文案
}

/// 可选择项组件2 - 支持弹窗输入和正则验证
class SelectableItem2 extends StatelessWidget {
  final String label;
  final String? value;
  final String? hint;
  final Color? hintColor;
  final bool isRequired;
  final InputType inputType;
  final int maxLength;
  final String title;
  final Function(String) onSave;
  final Function(String?, String)? onValidate;  // 🔥 简化：验证回调 (error, fieldName)
  final ValidationType? validationType;         // 🔥 验证类型
  final RegExp? customRegex;                    // 🔥 自定义正则
  final String? customErrorMessage;             // 🔥 自定义错误信息
  final String? errorMessage;                   // 🔥 当前错误信息
  final String? fieldName;                      // 🔥 字段名，用于错误回调

  const SelectableItem2({
    super.key,
    required this.label,
    this.value,
    this.hint,
    this.hintColor,
    this.isRequired = false,
    this.inputType = InputType.textField,
    this.maxLength = 100,
    required this.title,
    required this.onSave,
    this.onValidate,            // 🔥 验证回调
    this.validationType,        // 🔥 验证类型
    this.customRegex,           // 🔥 自定义正则
    this.customErrorMessage,    // 🔥 自定义错误信息
    this.errorMessage,          // 🔥 当前错误信息
    this.fieldName,             // 🔥 字段名
  });

  @override
  Widget build(BuildContext context) {
    final bool hasValue = value != null && value!.isNotEmpty;
    final bool hasError = errorMessage != null && errorMessage!.isNotEmpty;

    return GestureDetector(
      onTap: () => _showInputDialog(context),
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标签
                  _buildLabel(),
                  SizedBox(height: 7.h),
                  // 值或提示
                  _buildValue(hasValue, hasError),
                  // 🔥 错误提示
                  if (hasError) ...[
                    SizedBox(height: 4.h),
                    _buildErrorMessage(),
                  ],
                ],
              ),
            ),
            SizedBox(width: 8.w),
            // 箭头图标
            Icon(
              Icons.arrow_forward_ios,
              color: hasError ? ResumeTheme.primaryColor : ResumeTheme.textSecondary,
              size: 16.w,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建标签
  Widget _buildLabel() {
    return RichText(
      text: TextSpan(
        style: TextStyle(
          fontSize: 12.sp,
          color: ResumeTheme.textSecondary,
          fontFamily: 'Inter',
        ),
        children: [
          if (isRequired)
            TextSpan(
              text: '* ',
              style: TextStyle(color: ResumeTheme.requiredColor),
            ),
          TextSpan(text: label),
        ],
      ),
    );
  }

  /// 构建值或提示
  Widget _buildValue(bool hasValue, bool hasError) {
    return Text(
      hasValue ? value! : hint ?? '',
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
        color: hasError
            ? ResumeTheme.primaryColor  // 🔥 错误时显示主色调
            : hasValue
                ? ResumeTheme.textPrimary
                : (hintColor ?? ResumeTheme.textPlaceholder),
      ),
      maxLines: inputType == InputType.textField ? 1 : 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 🔥 构建错误提示
  Widget _buildErrorMessage() {
    return Text(
      errorMessage!,
      style: TextStyle(
        fontSize: 12.sp,
        color: ResumeTheme.primaryColor,  // 🔥 使用主色调显示错误
        fontWeight: FontWeight.w400,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 🔥 验证输入内容
  ValidationResult _validateInput(String input) {
    // 如果没有设置验证，直接返回成功
    if (validationType == null && customRegex == null) {
      return ValidationResult(true, null);
    }

    // 自定义正则验证
    if (customRegex != null) {
      return ValidationUtils.validateCustom(
        input,
        customRegex!,
        customErrorMessage ?? '输入格式不正确'
      );
    }

    // 预定义验证类型
    switch (validationType) {
      case ValidationType.email:
        return ValidationUtils.validateEmail(input);
      case ValidationType.phone:
        return ValidationUtils.validatePhone(input);
      case ValidationType.idCard:
        return ValidationUtils.validateIdCard(input);
      case ValidationType.chineseName:
        return ValidationUtils.validateChineseName(input);
      case ValidationType.password:
        return ValidationUtils.validatePassword(input);
      case ValidationType.strongPassword:
        return ValidationUtils.validateStrongPassword(input);
      case ValidationType.number:
        return ValidationUtils.validateNumber(input);
      case ValidationType.decimal:
        return ValidationUtils.validateDecimal(input);
      case ValidationType.url:
        return ValidationUtils.validateUrl(input);
      case ValidationType.qq:
        return ValidationUtils.validateQQ(input);
      case ValidationType.wechat:
        return ValidationUtils.validateWechat(input);
      case ValidationType.bankCard:
        return ValidationUtils.validateBankCard(input);
      default:
        return ValidationResult(true, null);
    }
  }

  /// 显示输入弹窗
  void _showInputDialog(BuildContext context) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => _InputDialog(
          title: title,
          initialText: value ?? '',
          hint: hint ?? '',
          inputType: inputType,
          maxLength: maxLength,
          onSave: onSave,
          validator: _validateInput,  // 🔥 传递验证函数
          onValidate: onValidate,     // 🔥 传递验证回调
          fieldName: fieldName,       // 🔥 传递字段名
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        fullscreenDialog: true,
      ),
    );
  }
}

/// 字数统计组件
class _CharacterCounter extends StatelessWidget {
  final ValueNotifier<String> textNotifier;
  final int maxLength;
  final bool isPositioned;

  const _CharacterCounter({
    required this.textNotifier,
    required this.maxLength,
    this.isPositioned = false,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<String>(
      valueListenable: textNotifier,
      builder: (context, text, child) {
        final currentLength = text.length;
        final counterWidget = Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$currentLength',
              style: TextStyle(
                fontSize: 14.sp,
                color: currentLength >= maxLength
                    ? ResumeTheme.primaryColor
                    : ResumeTheme.textSecondary,
              ),
            ),
            Text(
              '/',
              style: TextStyle(
                fontSize: 14.sp,
                color: ResumeTheme.textSecondary,
              ),
            ),
            Text(
              '$maxLength',
              style: TextStyle(
                fontSize: 14.sp,
                color: ResumeTheme.primaryColor,
              ),
            ),
          ],
        );

        if (isPositioned) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 0.h),
            decoration: BoxDecoration(
              color: ResumeTheme.surfaceColor.withOpacity(0.9),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: counterWidget,
          );
        }

        return counterWidget;
      },
    );
  }
}

/// 输入弹窗
class _InputDialog extends StatefulWidget {
  final String title;
  final String initialText;
  final String hint;
  final InputType inputType;
  final int maxLength;
  final Function(String) onSave;
  final ValidationResult Function(String)? validator;  // 🔥 验证函数
  final Function(String?, String)? onValidate;         // 🔥 验证回调
  final String? fieldName;                             // 🔥 字段名

  const _InputDialog({
    required this.title,
    required this.initialText,
    required this.hint,
    required this.inputType,
    required this.maxLength,
    required this.onSave,
    this.validator,   // 🔥 验证函数
    this.onValidate,  // 🔥 验证回调
    this.fieldName,   // 🔥 字段名
  });

  @override
  State<_InputDialog> createState() => _InputDialogState();
}

class _InputDialogState extends State<_InputDialog> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late ValueNotifier<String> _textNotifier;
  late double _cachedScreenHeight;
  String? _errorMessage;  // 🔥 新增：错误信息

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText);
    _focusNode = FocusNode();
    _textNotifier = ValueNotifier<String>(widget.initialText);

    // 优化监听器 - 只更新必要的状态
    _controller.addListener(() {
      _textNotifier.value = _controller.text;
    });

  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _textNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 缓存屏幕高度，避免重复计算
    _cachedScreenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: ResumeTheme.backgroundColor,
      resizeToAvoidBottomInset: true, // 优化键盘弹出
      appBar: _buildAppBar(),
      body: GestureDetector(
        onTap: () {
          // 点击空白区域收起键盘
          FocusScope.of(context).unfocus();
        },
        child: _buildBody(),
      ),
    );
  }

  /// 构建标题栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: ResumeTheme.surfaceColor,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.close,
          color: ResumeTheme.textSecondary,
          size: 24.w,
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        widget.title,
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: ResumeTheme.textPrimary,
        ),
      ),
      centerTitle: true,
      actions: [
        ValueListenableBuilder<String>(
          valueListenable: _textNotifier,
          builder: (context, text, child) {
            final hasText = text.trim().isNotEmpty;
            return TextButton(
              onPressed: hasText ? _handleSave : null,
              child: Text(
                '保存',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: hasText
                      ? ResumeTheme.primaryColor
                      : ResumeTheme.textPlaceholder,
                ),
              ),
            );
          },
        ),
      ],
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(1.h),
        child: Container(
          height: 1.h,
          color: ResumeTheme.borderColor,
        ),
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.inputType == InputType.textField)
            _buildTextField()
          else
            _buildTextArea(),
        ],
      ),
    );
  }

  /// 构建文本框
  Widget _buildTextField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: _controller,
          focusNode: _focusNode,
          maxLength: widget.maxLength,
          textInputAction: TextInputAction.done,
          keyboardType: TextInputType.text,
          style: TextStyle(
            fontSize: 16.sp,
            color: ResumeTheme.textPrimary,
          ),
          decoration: InputDecoration(
            hintText: widget.hint,
            hintStyle: TextStyle(
              fontSize: 16.sp,
              color: ResumeTheme.textPlaceholder,
            ),
            border: UnderlineInputBorder(
              borderSide: BorderSide(color: ResumeTheme.borderColor),
            ),
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: ResumeTheme.borderColor),
            ),
            focusedBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: ResumeTheme.primaryColor, width: 2),
            ),
            counterText: '', // 隐藏默认计数器
          ),
        ),
        SizedBox(height: 8.h),
        // 🔥 错误提示和字数统计
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 错误提示
            if (_errorMessage != null)
              Expanded(
                child: Text(
                  _errorMessage!,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ResumeTheme.primaryColor,
                    fontWeight: FontWeight.w400,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              )
            else
              const Spacer(),
            // 字数统计
            _CharacterCounter(
              textNotifier: _textNotifier,
              maxLength: widget.maxLength,
            ),
          ],
        ),
      ],
    );
  }

  /// 构建文本域
  Widget _buildTextArea() {
    // 使用缓存的屏幕高度，避免重复计算
    final textAreaHeight = _cachedScreenHeight * 0.4;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: textAreaHeight,
          child: Stack(
            children: [
              TextField(
                controller: _controller,
                focusNode: _focusNode,
                maxLength: widget.maxLength,
                maxLines: null,
                expands: true,
                textAlignVertical: TextAlignVertical.top,
                textInputAction: TextInputAction.newline,
                keyboardType: TextInputType.multiline,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: ResumeTheme.textPrimary,
                ),
                decoration: InputDecoration(
                  hintText: widget.hint,
                  hintStyle: TextStyle(
                    fontSize: 16.sp,
                    color: ResumeTheme.textPlaceholder,
                  ),
                  border: InputBorder.none,
                  counterText: '', // 隐藏默认计数器
                ),
              ),
              // 右下角字数统计
              Positioned(
                bottom: 8.h,
                right: 0.w,
                child: _CharacterCounter(
                  textNotifier: _textNotifier,
                  maxLength: widget.maxLength,
                  isPositioned: true,
                ),
              ),
            ],
          ),
        ),
        // 🔥 文本域的错误提示
        if (_errorMessage != null) ...[
          SizedBox(height: 8.h),
          Text(
            _errorMessage!,
            style: TextStyle(
              fontSize: 12.sp,
              color: ResumeTheme.primaryColor,
              fontWeight: FontWeight.w400,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  /// 🔥 处理保存 - 简化验证逻辑
  void _handleSave() {
    final text = _controller.text.trim();

    // 如果有验证器，先进行验证
    if (widget.validator != null) {
      final result = widget.validator!(text);
      if (!result.isValid) {
        setState(() {
          _errorMessage = result.errorMessage;
        });
        // 🔥 通知外部验证失败
        if (widget.onValidate != null && widget.fieldName != null) {
          widget.onValidate!(result.errorMessage, widget.fieldName!);
        }
        return; // 🔥 验证失败，不保存，不关闭弹窗
      }
    }

    // 验证通过，保存并关闭
    setState(() {
      _errorMessage = null;
    });
    // 🔥 通知外部验证成功
    if (widget.onValidate != null && widget.fieldName != null) {
      widget.onValidate!(null, widget.fieldName!);
    }
    widget.onSave(text);
    Navigator.of(context).pop();
  }
}

/// 使用示例：
///
/// // 文本框示例
/// SelectableItem2(
///   label: '姓名',
///   value: _name,
///   hint: '请输入姓名',
///   title: '编辑姓名',
///   inputType: InputType.textField,
///   maxLength: 20,
///   isRequired: true,
///   onSave: (value) {
///     setState(() {
///       _name = value;
///     });
///   },
/// ),
///
/// // 文本域示例
/// SelectableItem2(
///   label: '个人简介',
///   value: _introduction,
///   hint: '请输入个人简介',
///   title: '编辑个人简介',
///   inputType: InputType.textArea,
///   maxLength: 500,
///   onSave: (value) {
///     setState(() {
///       _introduction = value;
///     });
///   },
/// ),
