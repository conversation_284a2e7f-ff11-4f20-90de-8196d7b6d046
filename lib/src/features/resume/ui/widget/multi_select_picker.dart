import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../theme/resume_theme.dart';

/// 🔥 多选选择器组件
class MultiSelectPicker {
  /// 显示多选底部选择器
  static Future<List<String>?> show({
    required BuildContext context,
    required String title,
    required List<String> options,
    List<String>? selectedValues,
    int? maxSelection,
  }) async {
    if (options.isEmpty) return null;

    return await showModalBottomSheet<List<String>>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return _MultiSelectBottomSheet(
          title: title,
          options: options,
          selectedValues: selectedValues ?? [],
          maxSelection: maxSelection,
        );
      },
    );
  }
}

/// 多选选择器底部弹窗
class _MultiSelectBottomSheet extends StatefulWidget {
  final String title;
  final List<String> options;
  final List<String> selectedValues;
  final int? maxSelection;

  const _MultiSelectBottomSheet({
    required this.title,
    required this.options,
    required this.selectedValues,
    this.maxSelection,
  });

  @override
  State<_MultiSelectBottomSheet> createState() => _MultiSelectBottomSheetState();
}

class _MultiSelectBottomSheetState extends State<_MultiSelectBottomSheet> {
  late List<String> _selectedValues;

  @override
  void initState() {
    super.initState();
    _selectedValues = List.from(widget.selectedValues);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 头部
          _buildHeader(),
          // 分割线
          Container(height: 1.h, color: ResumeTheme.borderColor),
          // 选择列表
          Flexible(
            child: _buildOptionsList(),
          ),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 取消按钮
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              '取消',
              style: TextStyle(color: ResumeTheme.textSecondary),
            ),
          ),
          // 标题和选择数量
          Column(
            children: [
              Text(
                widget.title,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: ResumeTheme.textPrimary,
                ),
              ),
              if (widget.maxSelection != null)
                Text(
                  '已选择 ${_selectedValues.length}/${widget.maxSelection}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ResumeTheme.textSecondary,
                  ),
                ),
            ],
          ),
          // 确定按钮
          TextButton(
            onPressed: () => Navigator.pop(context, _selectedValues),
            child: Text(
              '确定',
              style: TextStyle(color: ResumeTheme.primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建选项列表
  Widget _buildOptionsList() {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: widget.options.length,
      itemBuilder: (context, index) {
        final option = widget.options[index];
        final isSelected = _selectedValues.contains(option);
        final canSelect = widget.maxSelection == null || 
                         _selectedValues.length < widget.maxSelection! ||
                         isSelected;

        return _buildOptionItem(option, isSelected, canSelect);
      },
    );
  }

  /// 构建选项项
  Widget _buildOptionItem(String option, bool isSelected, bool canSelect) {
    return InkWell(
      onTap: canSelect ? () => _toggleSelection(option) : null,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: isSelected ? ResumeTheme.primaryColor.withOpacity(0.1) : null,
        ),
        child: Row(
          children: [
            // 复选框
            Container(
              width: 20.w,
              height: 20.w,
              decoration: BoxDecoration(
                border: Border.all(
                  color: isSelected 
                      ? ResumeTheme.primaryColor 
                      : canSelect 
                          ? ResumeTheme.borderColor
                          : ResumeTheme.textSecondary.withOpacity(0.3),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(4.r),
                color: isSelected ? ResumeTheme.primaryColor : null,
              ),
              child: isSelected
                  ? Icon(
                      Icons.check,
                      size: 14.w,
                      color: Colors.white,
                    )
                  : null,
            ),
            SizedBox(width: 12.w),
            // 选项文本
            Expanded(
              child: Text(
                option,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: canSelect 
                      ? ResumeTheme.textPrimary 
                      : ResumeTheme.textSecondary.withOpacity(0.5),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 切换选择状态
  void _toggleSelection(String option) {
    setState(() {
      if (_selectedValues.contains(option)) {
        _selectedValues.remove(option);
      } else {
        // 检查是否超过最大选择数量
        if (widget.maxSelection == null || 
            _selectedValues.length < widget.maxSelection!) {
          _selectedValues.add(option);
        } else {
          // 显示提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('最多只能选择 ${widget.maxSelection} 项'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    });
  }
}

/// 🔥 多选结果类
class MultiSelectResult {
  final List<String> selectedValues;
  final List<String> selectedKeys;

  const MultiSelectResult({
    required this.selectedValues,
    required this.selectedKeys,
  });

  @override
  String toString() {
    return 'MultiSelectResult(selectedValues: $selectedValues, selectedKeys: $selectedKeys)';
  }
}
