import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kit/src/core/routing/app_router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:math' as math;
import '../../../../core/theme/app_color.dart';
import '../../../../shared/components/app_bars/unified_app_bar.dart';
import '../../../../shared/utils/safe_area_manager.dart';
import '../../logic/resume_library_logic.dart';

class ResumeLibraryView extends StatefulWidget {
  final ResumeLibraryLogic logic;

  const ResumeLibraryView({
    super.key,
    required this.logic,
  });

  @override
  State<ResumeLibraryView> createState() => _ResumeLibraryViewState();
}

class _ResumeLibraryViewState extends State<ResumeLibraryView> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    widget.logic.addListener(_onLogicChanged);
    widget.logic.initData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    widget.logic.removeListener(_onLogicChanged);
    super.dispose();
  }

  void _onLogicChanged() {
    if (mounted) {
      setState(() {});
    }
  }





  @override
  Widget build(BuildContext context) {
    final logic = widget.logic;

    // 设置状态栏样式为透明
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ));

    return Scaffold(
      backgroundColor: AppColors.pageBackground,
      // 移除AppBar，让渐变效果顶到状态栏
      extendBodyBehindAppBar: true,
      // 主体内容
      body: logic.isLoading
          ? const Center(child: CircularProgressIndicator())
          : Stack(
              children: [
                // 背景渐变 - 顶到全屏
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: SafeAreaManager.instance.screenSize.height,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          AppColors.primary.withValues(alpha: 0.08),
                          AppColors.primary.withValues(alpha: 0.04),
                          AppColors.primary.withValues(alpha: 0.02),
                          Colors.transparent,
                        ],
                        stops: const [0.0, 0.3, 0.6, 1.0],
                      ),
                    ),
                  ),
                ),
                // 主要内容 - 可滚动
                RefreshIndicator(
                  onRefresh: logic.refreshData,
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.fromLTRB(
                      16.w,
                      MediaQuery.of(context).padding.top + 60.w,
                      16.w,
                      100.w,
                    ),
                    itemCount: logic.resumes.length + 1, // +1 for header
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        // 页面头部标题 - 现在可以滚动
                        return _buildPageHeader(logic);
                      }
                      return _buildResumeCard(logic.resumes[index - 1], index - 1);
                    },
                  ),
                ),
                // 滚动时显示的透明AppBar
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: UnifiedAppBar.scrollableIconTransparent(
                    scrollController: _scrollController,
                    title: '简历库',
                    onBackPressed: () => Navigator.of(context).pop(),
                    backgroundColor: AppColors.white,
                    contentColor: AppColors.textPrimary,
                    iconColor: AppColors.color_666666
                  ),
                ),
              ],
            ),
      // 悬浮按钮
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          logic.addNewResume();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('添加新简历')),
          );
        },
        backgroundColor: AppColors.primary,
        child: Icon(Icons.add, color: Colors.white, size: 24.w),
      ),
    );
  }

  /// 构建页面头部
  Widget _buildPageHeader(ResumeLibraryLogic logic) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(4.w, 4.h, 4.w, 24.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '简历库',
            style: TextStyle(
              fontSize: 28.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.textPrimary,
              height: 1.2,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '管理您的简历 (${logic.totalCount}/4)',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建简历卡片
  Widget _buildResumeCard(ResumeItem resume, int cardIndex) {
    return GestureDetector(onTap: ()=>{
      context.router.push(ResumeRoute())
    },child: Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: Card(
        color: Colors.white,
        elevation: 2.0,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.w),
        ),
        clipBehavior: Clip.antiAlias,
        child: Stack(
          children: [
            Column(
              children: [
                _buildTopSection(resume, cardIndex),
                _buildBottomSection(resume),
              ],
            ),
            // 角标显示默认状态
            _CornerRibbon(isDefault: resume.isDefault),
          ],
        ),
      ),
    ));
  }

  /// 构建卡片顶部区域
  Widget _buildTopSection(ResumeItem resume, int cardIndex) {
    return Padding(
      padding: EdgeInsets.all(20.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 圆形进度指示器
          _ProgressCircle(
            completion: (resume.completeness * 100).toInt(),
            index: cardIndex,
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  resume.title,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 8.h),
                // 意向信息移到这里
                _buildIntentionRow(
                  icon: Icons.work_outline,
                  text: resume.jobTitle,
                ),
                SizedBox(height: 6.h),
                _buildIntentionRow(
                  icon: Icons.attach_money,
                  text: '${resume.salary} | ${resume.city}',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建卡片底部区域
  Widget _buildBottomSection(ResumeItem resume) {
    return Padding(
      padding: EdgeInsets.fromLTRB(0, 0, 10.w, 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
            '更新于: 2025-${resume.updatedDate}',
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建意向信息行
  Widget _buildIntentionRow({required IconData icon, required String text}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: AppColors.primary, size: 16.w),
        SizedBox(width: 8.w),
        Flexible(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }


}

/// 圆形进度指示器组件
class _ProgressCircle extends StatefulWidget {
  final int completion;
  final int index; // 添加索引用于延迟

  const _ProgressCircle({
    required this.completion,
    this.index = 0,
  });

  @override
  State<_ProgressCircle> createState() => _ProgressCircleState();
}

class _ProgressCircleState extends State<_ProgressCircle>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _layoutController;
  late Animation<double> _progressAnimation;
  late Animation<double> _layoutAnimation;

  @override
  void initState() {
    super.initState();

    // 进度动画控制器
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500), // 1.5秒动画
      vsync: this,
    );

    // 布局变化动画控制器
    _layoutController = AnimationController(
      duration: const Duration(milliseconds: 600), // 1秒布局动画
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.completion.toDouble(),
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutCubic,
    ));

    // 合并所有布局动画到单一动画
    _layoutAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _layoutController,
      curve: Curves.easeOutBack,
    ));

    // 监听进度动画完成
    _progressController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // 进度动画完成后，开始布局变化动画（所有完善度都显示文字）
        _layoutController.forward();
      }
    });

    // 基于索引添加延迟，实现逐步播放动画
    final delay = 300 + (widget.index * 400); // 每个卡片延迟400ms，更明显的错开效果
    Future.delayed(Duration(milliseconds: delay), () {
      if (mounted) {
        _progressController.forward();
      }
    });
  }

  @override
  void dispose() {
    _progressController.dispose();
    _layoutController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: SizedBox(
        width: 64.w,
        height: 64.w,
        child: AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            return AnimatedBuilder(
              animation: _layoutAnimation,
              builder: (context, child) {
                return CustomPaint(
                  painter: _ProgressCirclePainter(
                    completion: _progressAnimation.value.toInt(),
                    isDeliverable: widget.completion > 60,
                    colorProgress: _layoutAnimation.value, // 使用布局动画的值
                  ),
                  child: Builder(
                    builder: (context) {
                      final bool isDeliverable = widget.completion > 60;
                      final bool shouldShowText = _layoutAnimation.value > 0;
                      final String statusText = isDeliverable ? '可投递' : '待优化';
                      final Color statusColor = isDeliverable
                          ? AppColors.abcSearchUrlTextNormal
                          : AppColors.primary;

                      // 计算动画值，确保在有效范围内
                      final double numberOffset = _layoutAnimation.value * -1.0; // 向上移动1个单位
                      final double fontSize = (14.0 - (_layoutAnimation.value * 2.0)).clamp(12.0, 14.0); // 从14sp到12sp
                      final double textOffset = 20.0 * (1.0 - _layoutAnimation.value); // 从下方滑入
                      final double textOpacity = _layoutAnimation.value.clamp(0.0, 1.0); // 确保透明度在有效范围内

                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // 数字部分 - 使用单一Transform
                            Transform.translate(
                              offset: Offset(0, numberOffset.h),
                              child: Text(
                                '${_progressAnimation.value.toInt()}%',
                                style: TextStyle(
                                  fontSize: fontSize.sp,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ),
                            // 状态文字 - 使用单一Transform
                            if (shouldShowText)
                              Transform.translate(
                                offset: Offset(0, textOffset.h),
                                child: Opacity(
                                  opacity: textOpacity,
                                  child: Text(
                                    statusText,
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w600,
                                      color: statusColor,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      );
                    },
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}

/// 圆形进度指示器绘制器
class _ProgressCirclePainter extends CustomPainter {
  final int completion;
  final bool isDeliverable;
  final double colorProgress;

  _ProgressCirclePainter({
    required this.completion,
    this.isDeliverable = false,
    this.colorProgress = 0.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final rect = Rect.fromCircle(center: center, radius: size.width / 2);

    final backgroundPaint = Paint()
      ..color = AppColors.borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 6;

    // 根据动画进度和是否可投递来插值颜色
    Color progressColor;
    if (isDeliverable && colorProgress > 0) {
      // 可投递：从 primary 渐变到 abcSearchUrlTextNormal
      progressColor = Color.lerp(
        AppColors.primary,
        AppColors.abcSearchUrlTextNormal,
        colorProgress,
      ) ?? AppColors.primary;
    } else {
      // 待优化或动画未开始：保持 primary 颜色
      progressColor = AppColors.primary;
    }

    final progressPaint = Paint()
      ..color = progressColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 6
      ..strokeCap = StrokeCap.round;

    // 绘制背景圆圈
    canvas.drawArc(rect, -math.pi / 2, 2 * math.pi, false, backgroundPaint);

    // 绘制进度弧线
    final progressAngle = 2 * math.pi * (completion / 100);
    if (completion > 0) {
      canvas.drawArc(rect, -math.pi / 2, progressAngle, false, progressPaint);
    }
  }

  @override
  bool shouldRepaint(covariant _ProgressCirclePainter oldDelegate) {
    return completion != oldDelegate.completion ||
           isDeliverable != oldDelegate.isDeliverable ||
           colorProgress != oldDelegate.colorProgress;
  }
}

/// 角标组件
class _CornerRibbon extends StatelessWidget {
  final bool isDefault;

  const _CornerRibbon({required this.isDefault});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      right: 0,
      child: GestureDetector(
        onTap: () {
          // 处理设置为默认的操作
        },
        child: SizedBox(
          width: 60.w,
          height: 60.w,
          child: Stack(
            children: [
              CustomPaint(
                size: Size(60.w, 60.w), // 增大角标尺寸
                painter: _CornerRibbonPainter(
                  color: isDefault ? AppColors.primary : AppColors.secondary,
                ),
              ),
              Positioned(
                top: 12.h, // 调整图标位置
                right: 12.w,
                child: Icon(
                  Icons.check,
                  weight: 12,
                  color: Colors.white,
                  size: 18.w, // 增大图标尺寸
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 角标绘制器
class _CornerRibbonPainter extends CustomPainter {
  final Color color;

  _CornerRibbonPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = color;
    final path = Path()
      ..moveTo(size.width, 0)
      ..lineTo(0, 0)
      ..lineTo(size.width, size.height)
      ..close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
