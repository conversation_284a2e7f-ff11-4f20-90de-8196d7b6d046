import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_kit/src/features/resume/theme/resume_theme.dart';

/// 🔥 关于我们页面 - 遵循项目架构规范
/// 
/// 功能特性：
/// - ✅ 包含5个功能项：联系我们、留言反馈、关于我们、用户协议、隐私政策
/// - ✅ 列表式布局，每个item右侧带箭头图标，支持点击跳转
/// - ✅ 使用ResumeTheme配色方案
/// - ✅ 配置对应的路由跳转
class AboutUsScreen extends StatelessWidget {
  const AboutUsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ResumeTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(context),
    );
  }

  /// 🔥 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: ResumeTheme.surfaceColor,
      elevation: 0,
      title: Text(
        '关于我们',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: ResumeTheme.textPrimary,
        ),
      ),
      leading: Builder(
        builder: (context) => IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(
            Icons.arrow_back_ios,
            color: ResumeTheme.textPrimary,
            size: 20.w,
          ),
        ),
      ),
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(1.h),
        child: Container(
          height: 1.h,
          color: ResumeTheme.borderColor,
        ),
      ),
    );
  }

  /// 🔥 构建页面主体
  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          SizedBox(height: 8.h),
          // 应用信息卡片
          _buildAppInfoCard(),
          SizedBox(height: 16.h),
          // 功能列表卡片
          _buildFunctionListCard(context),
          SizedBox(height: 16.h),
          // 版本信息
          _buildVersionInfo(),
        ],
      ),
    );
  }

  /// 🔥 构建应用信息卡片
  Widget _buildAppInfoCard() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        children: [
          // 应用图标
          Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              color: ResumeTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Icon(
              Icons.work,
              color: ResumeTheme.primaryColor,
              size: 40.w,
            ),
          ),
          SizedBox(height: 16.h),
          // 应用名称
          Text(
            '智达招聘',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.w700,
              color: ResumeTheme.textPrimary,
            ),
          ),
          SizedBox(height: 8.h),
          // 应用描述
          Text(
            '专业的求职招聘平台\n连接优秀人才与理想职位',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14.sp,
              color: ResumeTheme.textSecondary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  /// 🔥 构建功能列表卡片
  Widget _buildFunctionListCard(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildFunctionItem(
            context: context,
            icon: Icons.contact_support,
            title: '联系我们',
            subtitle: '客服热线、邮箱联系方式',
            onTap: () => _handleContactUs(context),
          ),
          _buildDivider(),
          _buildFunctionItem(
            context: context,
            icon: Icons.feedback,
            title: '留言反馈',
            subtitle: '意见建议、问题反馈',
            onTap: () => _handleFeedback(context),
          ),
          _buildDivider(),
          _buildFunctionItem(
            context: context,
            icon: Icons.info,
            title: '关于我们',
            subtitle: '公司介绍、发展历程',
            onTap: () => _handleAboutCompany(context),
          ),
          _buildDivider(),
          _buildFunctionItem(
            context: context,
            icon: Icons.description,
            title: '用户协议',
            subtitle: '服务条款、使用规范',
            onTap: () => _handleUserAgreement(context),
          ),
          _buildDivider(),
          _buildFunctionItem(
            context: context,
            icon: Icons.privacy_tip,
            title: '隐私政策',
            subtitle: '隐私保护、数据安全',
            onTap: () => _handlePrivacyPolicy(context),
            isLast: true,
          ),
        ],
      ),
    );
  }

  /// 🔥 构建功能项
  Widget _buildFunctionItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isLast = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(isLast ? 0 : 16.r),
        bottom: Radius.circular(isLast ? 16.r : 0),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            // 图标
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: ResumeTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                color: ResumeTheme.primaryColor,
                size: 20.w,
              ),
            ),
            SizedBox(width: 12.w),
            // 文本信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: ResumeTheme.textPrimary,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: ResumeTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            // 箭头
            Icon(
              Icons.arrow_forward_ios,
              color: ResumeTheme.textSecondary,
              size: 16.w,
            ),
          ],
        ),
      ),
    );
  }

  /// 🔥 构建分割线
  Widget _buildDivider() {
    return Container(
      height: 1.h,
      margin: EdgeInsets.only(left: 68.w),
      color: ResumeTheme.borderColor,
    );
  }

  /// 🔥 构建版本信息
  Widget _buildVersionInfo() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          Text(
            '版本信息',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: ResumeTheme.textPrimary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'v1.0.0 (Build 1)',
            style: TextStyle(
              fontSize: 12.sp,
              color: ResumeTheme.textSecondary,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            '© 2024 智达招聘. All rights reserved.',
            style: TextStyle(
              fontSize: 10.sp,
              color: ResumeTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// 🔥 处理联系我们
  void _handleContactUs(BuildContext context) {
    // TODO: 跳转到联系我们页面
    // Navigator.pushNamed(context, '/contact_us');
    
    // 暂时显示联系方式对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('联系我们'),
        content: const Text(
          '客服热线：400-123-4567\n'
          '工作时间：周一至周五 9:00-18:00\n'
          '邮箱：<EMAIL>\n'
          '地址：北京市朝阳区xxx大厦',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 🔥 处理留言反馈
  void _handleFeedback(BuildContext context) {
    // TODO: 跳转到反馈页面
    // Navigator.pushNamed(context, '/help_feedback');
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('跳转到留言反馈页面')),
    );
  }

  /// 🔥 处理关于公司
  void _handleAboutCompany(BuildContext context) {
    // TODO: 跳转到公司介绍页面
    // Navigator.pushNamed(context, '/company_intro');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('关于智达招聘'),
        content: const Text(
          '智达招聘成立于2020年，是一家专注于互联网招聘的科技公司。\n\n'
          '我们致力于通过技术创新，为求职者和企业提供更高效、更精准的招聘服务。\n\n'
          '目前已服务超过10万家企业，帮助100万+求职者找到理想工作。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 🔥 处理用户协议
  void _handleUserAgreement(BuildContext context) {
    // TODO: 跳转到用户协议页面
    // Navigator.pushNamed(context, '/user_agreement');
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('跳转到用户协议页面')),
    );
  }

  /// 🔥 处理隐私政策
  void _handlePrivacyPolicy(BuildContext context) {
    // TODO: 跳转到隐私政策页面
    // Navigator.pushNamed(context, '/privacy_policy');
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('跳转到隐私政策页面')),
    );
  }
}
