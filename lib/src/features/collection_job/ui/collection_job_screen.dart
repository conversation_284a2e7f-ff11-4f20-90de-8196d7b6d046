import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:flutter_kit/src/shared/components/custom_list_view/custom_list_view_export.dart';
import 'package:flutter_kit/src/features/resume/theme/resume_theme.dart';
import '../logic/collection_job_logic.dart';
import '../../../datasource/repositories/collection_job_repository.dart';
import 'widgets/collection_job_item.dart';

/// 🔥 收藏简历页面 - 遵循项目架构规范
/// 
/// 功能特性：
/// - ✅ 使用CustomListView组件展示列表
/// - ✅ 支持下拉刷新和上拉加载
/// - ✅ 使用Logic + ChangeNotifier架构
/// - ✅ 符合项目设计规范
///
@RoutePage()
class CollectionJobScreen extends StatefulWidget {
  const CollectionJobScreen({super.key});

  @override
  State<CollectionJobScreen> createState() => _CollectionJobScreenState();
}

class _CollectionJobScreenState extends State<CollectionJobScreen> {
  late CollectionJobLogic _logic;
  final TextEditingController _searchController = TextEditingController();
  bool _isSearchMode = false;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  /// 🔥 加载初始数据
  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _logic.loadData();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _logic.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _logic,
      child: Scaffold(
        backgroundColor: ResumeTheme.backgroundColor,
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  /// 🔥 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: ResumeTheme.surfaceColor,
      elevation: 0,
      title: _isSearchMode ? _buildSearchField() : _buildTitle(),
      actions: _buildAppBarActions(),
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(1.h),
        child: Container(
          height: 1.h,
          color: ResumeTheme.borderColor,
        ),
      ),
    );
  }

  /// 🔥 构建标题
  Widget _buildTitle() {
    return Consumer<CollectionJobLogic>(
      builder: (context, logic, child) {
        return Text(
          '收藏职位 (${logic.itemCount})',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: ResumeTheme.textPrimary,
          ),
        );
      },
    );
  }

  /// 🔥 构建搜索框
  Widget _buildSearchField() {
    return TextField(
      controller: _searchController,
      autofocus: true,
      decoration: InputDecoration(
        hintText: '搜索职位或公司',
        hintStyle: TextStyle(
          color: ResumeTheme.textSecondary,
          fontSize: 16.sp,
        ),
        border: InputBorder.none,
        contentPadding: EdgeInsets.zero,
      ),
      style: TextStyle(
        fontSize: 16.sp,
        color: ResumeTheme.textPrimary,
      ),
      onSubmitted: _handleSearch,
    );
  }

  /// 🔥 构建AppBar操作按钮
  List<Widget> _buildAppBarActions() {
    return [
      if (_isSearchMode) ...[
        // 搜索模式下的取消按钮
        TextButton(
          onPressed: _exitSearchMode,
          child: Text(
            '取消',
            style: TextStyle(
              color: ResumeTheme.textSecondary,
              fontSize: 16.sp,
            ),
          ),
        ),
      ] else ...[
        // 正常模式下的搜索按钮
        IconButton(
          onPressed: _enterSearchMode,
          icon: Icon(
            Icons.search,
            color: ResumeTheme.textSecondary,
            size: 24.w,
          ),
        ),
        // 更多操作按钮
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          icon: Icon(
            Icons.more_vert,
            color: ResumeTheme.textSecondary,
            size: 24.w,
          ),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'sort',
              child: Text('排序'),
            ),
            const PopupMenuItem(
              value: 'filter',
              child: Text('筛选'),
            ),
            const PopupMenuItem(
              value: 'batch_delete',
              child: Text('批量删除'),
            ),
          ],
        ),
      ],
    ];
  }

  /// 🔥 构建页面主体
  Widget _buildBody() {
    return Consumer<CollectionJobLogic>(
      builder: (context, logic, child) {
        return CustomListView<JobInfoEntity>(
          dataSource: logic.dataSource,
          itemBuilder: (context, item, index) {
            return CollectionJobItem(item: item, index: index);
          },
          enableRefresh: true,
          enableLoadMore: true,
          enableItemAnimation: true,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          separatorBuilder: (context, index) => SizedBox(height: 12.h),
          emptyWidget: _buildEmptyWidget(),
          errorWidget: _buildErrorWidget(),
        );
      },
    );
  }

  /// 🔥 构建空状态组件
  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 80.w,
            color: ResumeTheme.textSecondary.withOpacity(0.5),
          ),
          SizedBox(height: 16.h),
          Text(
            '暂无收藏职位',
            style: TextStyle(
              fontSize: 16.sp,
              color: ResumeTheme.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '快去收藏心仪的职位吧',
            style: TextStyle(
              fontSize: 14.sp,
              color: ResumeTheme.textSecondary.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// 🔥 构建错误状态组件
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80.w,
            color: ResumeTheme.textSecondary.withOpacity(0.5),
          ),
          SizedBox(height: 16.h),
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 16.sp,
              color: ResumeTheme.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () => _logic.loadData(),
            style: ElevatedButton.styleFrom(
              backgroundColor: ResumeTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
            ),
            child: Text(
              '重试',
              style: TextStyle(fontSize: 14.sp),
            ),
          ),
        ],
      ),
    );
  }

  /// 🔥 进入搜索模式
  void _enterSearchMode() {
    setState(() {
      _isSearchMode = true;
    });
  }

  /// 🔥 退出搜索模式
  void _exitSearchMode() {
    setState(() {
      _isSearchMode = false;
      _searchController.clear();
    });
    // 重新加载全部数据
    _logic.refreshPaging();
  }

  /// 🔥 处理搜索
  void _handleSearch(String keyword) {
    if (keyword.trim().isNotEmpty) {
      _logic.searchCollections(keyword);
    }
  }

  /// 🔥 处理菜单操作
  void _handleMenuAction(String action) {
    switch (action) {
      case 'sort':
        _showSortDialog();
        break;
      case 'filter':
        _showFilterDialog();
        break;
      case 'batch_delete':
        _showBatchDeleteDialog();
        break;
    }
  }

  /// 🔥 显示排序对话框
  void _showSortDialog() {
    // TODO: 实现排序对话框
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('排序功能开发中')),
    );
  }

  /// 🔥 显示筛选对话框
  void _showFilterDialog() {
    // TODO: 实现筛选对话框
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('筛选功能开发中')),
    );
  }

  /// 🔥 显示批量删除对话框
  void _showBatchDeleteDialog() {
    // TODO: 实现批量删除对话框
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('批量删除功能开发中')),
    );
  }

  /// 🔥 处理职位点击
  void _handleJobTap(JobInfoEntity job) {
    // TODO: 跳转到职位详情页
  }

  /// 🔥 处理取消收藏
  void _handleRemoveCollection(JobInfoEntity job) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认取消收藏'),
        content: Text('确定要取消收藏"${job.name}"吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _logic.removeCollection(job.id);
            },
            child: Text(
              '确定',
              style: TextStyle(color: ResumeTheme.primaryColor),
            ),
          ),
        ],
      ),
    );
  }
}
