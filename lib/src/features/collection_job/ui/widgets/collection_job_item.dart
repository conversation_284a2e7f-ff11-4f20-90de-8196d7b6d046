import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_kit/src/features/resume/theme/resume_theme.dart';

import '../../../../core/routing/app_router.dart';
import '../../../../core/theme/app_color.dart';

/// 🔥 收藏职位列表项组件

class CollectionJobItem extends StatelessWidget {
  final JobInfoEntity item;
  final int index;

  const CollectionJobItem({
    required this.item,
    required this.index,
    Key? key,
  }) : super(key: key);

  List<String> get tags {
    // 假设welfare字段用逗号分隔，学历和经验字段直接拼接
    final List<String> tagList = [];
    if (item.demandEducationCode.isNotEmpty)
      tagList.add(item.demandEducationCode);
    if (item.nature.isNotEmpty) tagList.add(item.nature);
    if (item.welfare.isNotEmpty) tagList.addAll(item.welfare.split(','));
    return tagList.where((e) => e.trim().isNotEmpty).toList();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // 跳转到职位详情页面
        context.router.push(JobDetailRoute(jobId: item.id));
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 8.w, horizontal: 16.w),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                  color: AppColors.alpha_10_white,
                  blurRadius: 2.w,
                  offset: Offset(0, 2))
            ]),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(
            children: [
              Expanded(
                  flex: 2,
                  child: Text(
                    item.name,
                    style: TextStyle(fontSize: 18.w, fontWeight: FontWeight.w600),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )),
              SizedBox(
                width: 6.w,
              ),
              Container(
                alignment: Alignment.centerRight,
                child: Text(
                  item.pay + ' ' + item.jobPayUnit,
                  style: TextStyle(
                      fontSize: 17.w,
                      color: AppColors.color_f02e4b,
                      fontWeight: FontWeight.w600),
                ),
              )
            ],
          ),
          SizedBox(height: 10.w),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.w)),
            child: Text(item.jobTypeName,
                style: TextStyle(color: AppColors.color_666666, fontSize: 14.w,fontWeight: FontWeight.w600)),
          ),
          SizedBox(height: 10.w),
          //标签
          Wrap(
            spacing: 8.w,
            children: (['标签栏', '标签栏', '标签栏'] as List<String>).map((tag) {
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.w),
                decoration: BoxDecoration(
                  color: AppColors.color_f6f8fb,
                  borderRadius: BorderRadius.circular(4.w),
                ),
                child: Text(
                  tag,
                  style: TextStyle(color: AppColors.color_686868, fontSize: 14.w),
                ),
              );
            }).toList(),
          ),
          SizedBox(height: 12.w),
          Row(
            children: [
              CircleAvatar(
                  backgroundImage: NetworkImage(item.enterpriseLogoSmall),
                  radius: 15.w),
              SizedBox(width: 6.w),
              Expanded(
                  child: Text(
                    item.enterpriseName,
                    style: TextStyle(fontSize: 12.w, color: AppColors.color_666666),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )),
              SizedBox(
                width: 10.w,
              ),
              Container(
                alignment: Alignment.centerRight,
                child: Text(
                  item.workAreaName,
                  style: TextStyle(fontSize: 12.w, color: AppColors.color_686868),
                ),
              )
            ],
          ),
        ]),
      ),
    );
  }
}
