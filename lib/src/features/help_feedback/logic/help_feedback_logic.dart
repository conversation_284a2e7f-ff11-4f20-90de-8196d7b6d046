import 'package:flutter/foundation.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/shared/mixins/form_validation_mixin.dart';

/// 🔥 帮助与反馈Logic - 遵循项目架构规范
/// 
/// 功能特性：
/// - ✅ 继承ViewStateLogic和FormValidationMixin
/// - ✅ 表单验证和状态管理
/// - ✅ 字数统计和限制
/// - ✅ 提交状态管理
class HelpFeedbackLogic extends ViewStateLogic with FormValidationMixin {
  
  // 表单数据
  String? _title;
  String? _content;
  bool _isSubmitting = false;
  
  // Getters
  String? get title => _title;
  String? get content => _content;
  bool get isSubmitting => _isSubmitting;

  /// 🔥 重写必填字段配置
  @override
  Map<String, String> get requiredFields => {
    'title': '反馈标题',
    'content': '详细描述',
  };

  /// 🔥 检查是否可以提交
  bool get canSubmit {
    if (_isSubmitting) return false;
    
    // 基本验证：标题和内容不能为空
    if (_title == null || _title!.trim().isEmpty) return false;
    if (_content == null || _content!.trim().isEmpty) return false;
    
    // 长度验证
    if (_title!.trim().length > 50) return false;
    if (_content!.trim().length > 200) return false;
    
    // 没有验证错误
    return !hasAnyError;
  }

  /// 🔥 更新标题
  void updateTitle(String value) {
    _title = value;
    
    // 实时验证
    _validateTitle(value);
    
    notifyListeners();
  }

  /// 🔥 更新内容
  void updateContent(String value) {
    _content = value;
    
    // 实时验证
    _validateContent(value);
    
    notifyListeners();
  }

  /// 🔥 验证标题
  void _validateTitle(String value) {
    String? error;
    
    if (value.trim().isEmpty) {
      error = '请填写反馈标题';
    } else if (value.trim().length > 50) {
      error = '标题长度不能超过50个字符';
    } else if (value.trim().length < 2) {
      error = '标题至少需要2个字符';
    }
    
    setFieldError(error, 'title');
  }

  /// 🔥 验证内容
  void _validateContent(String value) {
    String? error;
    
    if (value.trim().isEmpty) {
      error = '请填写详细描述';
    } else if (value.trim().length > 200) {
      error = '内容长度不能超过200个字符';
    } else if (value.trim().length < 10) {
      error = '请详细描述您的问题，至少需要10个字符';
    }
    
    setFieldError(error, 'content');
  }

  /// 🔥 验证整个表单
  @override
  bool validateForm() {
    // 构建验证数据
    final data = {
      'title': _title,
      'content': _content,
    };
    
    // 使用Mixin的验证方法验证必填项
    final isRequiredValid = validateRequiredFields(data);
    
    // 额外的自定义验证
    _validateTitle(_title ?? '');
    _validateContent(_content ?? '');
    
    return isRequiredValid && !hasAnyError;
  }

  /// 🔥 提交反馈
  Future<void> submitFeedback() async {
    try {
      // 设置提交状态
      _isSubmitting = true;
      notifyListeners();
      
      // 最终验证
      if (!validateForm()) {
        throw Exception('表单验证失败，请检查输入内容');
      }
      
      debugPrint('🔍 HelpFeedbackLogic - 开始提交反馈');
      debugPrint('🔍 标题: $_title');
      debugPrint('🔍 内容: $_content');
      
      // TODO: 实现实际的API调用
      // final feedbackData = {
      //   'title': _title!.trim(),
      //   'content': _content!.trim(),
      //   'deviceInfo': await _getDeviceInfo(),
      //   'appVersion': await _getAppVersion(),
      //   'userId': await _getCurrentUserId(),
      //   'submitTime': DateTime.now().toIso8601String(),
      // };
      // 
      // final response = await feedbackRepository.submitFeedback(feedbackData);
      // if (!response.isSuccess) {
      //   throw Exception(response.message);
      // }
      
      // 🔥 暂时模拟提交
      await Future.delayed(const Duration(seconds: 2));
      
      // 模拟随机失败（10%概率）
      if (DateTime.now().millisecondsSinceEpoch % 10 == 0) {
        throw Exception('网络连接失败，请稍后重试');
      }
      
      debugPrint('🔍 HelpFeedbackLogic - 反馈提交成功');
      
      // 清空表单
      _clearForm();
      
    } catch (e) {
      debugPrint('🔍 HelpFeedbackLogic - 反馈提交失败: $e');
      rethrow;
    } finally {
      _isSubmitting = false;
      notifyListeners();
    }
  }

  /// 🔥 清空表单
  void _clearForm() {
    _title = null;
    _content = null;
    clearAllErrors();
    notifyListeners();
  }

  /// 🔥 重置表单
  void resetForm() {
    _clearForm();
    debugPrint('🔍 HelpFeedbackLogic - 表单已重置');
  }

  /// 🔥 获取设备信息（用于反馈）
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    // TODO: 实现获取设备信息
    // final deviceInfo = await DeviceInfoService.getDeviceInfo();
    // return deviceInfo;
    
    // 🔥 暂时返回模拟数据
    return {
      'platform': 'iOS',
      'version': '16.0',
      'model': 'iPhone 14',
      'brand': 'Apple',
    };
  }

  /// 🔥 获取应用版本
  Future<String> _getAppVersion() async {
    // TODO: 实现获取应用版本
    // final packageInfo = await PackageInfo.fromPlatform();
    // return '${packageInfo.version}+${packageInfo.buildNumber}';
    
    // 🔥 暂时返回模拟版本
    return '1.0.0+1';
  }

  /// 🔥 获取当前用户ID
  Future<String?> _getCurrentUserId() async {
    // TODO: 实现获取当前用户ID
    // final userInfo = await UserService.getCurrentUser();
    // return userInfo?.id;
    
    // 🔥 暂时返回模拟用户ID
    return 'user_123456';
  }

  /// 🔥 获取字符统计信息
  Map<String, int> get characterStats {
    return {
      'titleLength': _title?.length ?? 0,
      'contentLength': _content?.length ?? 0,
      'titleMaxLength': 50,
      'contentMaxLength': 200,
    };
  }

  /// 🔥 检查标题是否超出限制
  bool get isTitleOverLimit => (_title?.length ?? 0) > 50;

  /// 🔥 检查内容是否超出限制
  bool get isContentOverLimit => (_content?.length ?? 0) > 200;

  /// 🔥 获取表单完成度（百分比）
  double get formCompleteness {
    double score = 0.0;
    
    // 标题完成度 (40%)
    if (_title != null && _title!.trim().isNotEmpty) {
      if (_title!.trim().length >= 2 && _title!.trim().length <= 50) {
        score += 0.4;
      } else {
        score += 0.2; // 部分完成
      }
    }
    
    // 内容完成度 (60%)
    if (_content != null && _content!.trim().isNotEmpty) {
      if (_content!.trim().length >= 10 && _content!.trim().length <= 200) {
        score += 0.6;
      } else {
        score += 0.3; // 部分完成
      }
    }
    
    return score;
  }

  @override
  String toString() {
    return 'HelpFeedbackLogic('
           'title: ${_title?.length ?? 0} chars, '
           'content: ${_content?.length ?? 0} chars, '
           'canSubmit: $canSubmit, '
           'isSubmitting: $_isSubmitting)';
  }
}
