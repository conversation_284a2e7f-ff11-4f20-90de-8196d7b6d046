import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:flutter_kit/src/features/resume/theme/resume_theme.dart';
import 'package:flutter_kit/src/shared/widgets/smart_form_field.dart';
import 'package:flutter_kit/src/shared/mixins/form_validation_mixin.dart';
import '../../resume/ui/widget/selectable_item2.dart';
import '../logic/help_feedback_logic.dart';

/// 🔥 帮助与反馈页面 - 遵循项目架构规范
/// 
/// 功能特性：
/// - ✅ 标题输入框 + 反馈内容多行输入框 + 底部提交按钮
/// - ✅ 反馈内容不超过200字，实时显示字数统计
/// - ✅ 集成SmartFormField系统，使用统一表单验证
/// - ✅ 使用Logic + ChangeNotifier架构
class HelpFeedbackScreen extends StatefulWidget {
  const HelpFeedbackScreen({super.key});

  @override
  State<HelpFeedbackScreen> createState() => _HelpFeedbackScreenState();
}

class _HelpFeedbackScreenState extends State<HelpFeedbackScreen> {
  late HelpFeedbackLogic _logic;

  @override
  void initState() {
    super.initState();
    _logic = HelpFeedbackLogic();
  }

  @override
  void dispose() {
    _logic.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _logic,
      child: Scaffold(
        backgroundColor: ResumeTheme.backgroundColor,
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  /// 🔥 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: ResumeTheme.surfaceColor,
      elevation: 0,
      title: Text(
        '帮助与反馈',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: ResumeTheme.textPrimary,
        ),
      ),
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: Icon(
          Icons.arrow_back_ios,
          color: ResumeTheme.textPrimary,
          size: 20.w,
        ),
      ),
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(1.h),
        child: Container(
          height: 1.h,
          color: ResumeTheme.borderColor,
        ),
      ),
    );
  }

  /// 🔥 构建页面主体
  Widget _buildBody() {
    return Consumer<HelpFeedbackLogic>(
      builder: (context, logic, child) {
        return Column(
          children: [
            // 表单区域
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  children: [
                    SizedBox(height: 8.h),
                    // 反馈表单卡片
                    _buildFeedbackForm(logic),
                    SizedBox(height: 16.h),
                    // 帮助说明卡片
                    _buildHelpCard(),
                  ],
                ),
              ),
            ),
            // 底部提交按钮
            _buildSubmitButton(logic),
          ],
        );
      },
    );
  }

  /// 🔥 构建反馈表单卡片
  Widget _buildFeedbackForm(HelpFeedbackLogic logic) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 表单标题
          Text(
            '反馈内容',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: ResumeTheme.textPrimary,
            ),
          ),
          SizedBox(height: 16.h),
          // 分割线
          Container(height: 1.h, color: ResumeTheme.borderColor),
          SizedBox(height: 16.h),
          // 标题输入框
          SmartFormField(
            fieldName: 'title',
            label: '反馈标题',
            value: logic.title,
            hint: '请简要描述您的问题或建议',
            isRequired: true,
            fieldType: SmartFieldType.text,
            fieldConfig: const {'maxLength': 50},
            logic: logic,
            onChanged: (value) => logic.updateTitle(value),
          ),
          SizedBox(height: 16.h),
          // 反馈内容输入框
          SmartFormField(
            fieldName: 'content',
            label: '详细描述',
            value: logic.content,
            hint: '请详细描述您遇到的问题或您的建议，我们会认真处理每一条反馈',
            isRequired: true,
            fieldType: SmartFieldType.text,
            fieldConfig: const {
              'maxLength': 200,
              'inputType': InputType.textArea,
            },
            logic: logic,
            onChanged: (value) => logic.updateContent(value),
          ),
          SizedBox(height: 8.h),
          // 字数统计
          _buildCharacterCount(logic),
        ],
      ),
    );
  }

  /// 🔥 构建字数统计
  Widget _buildCharacterCount(HelpFeedbackLogic logic) {
    final currentLength = logic.content?.length ?? 0;
    const maxLength = 200;
    final isOverLimit = currentLength > maxLength;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          '$currentLength/$maxLength',
          style: TextStyle(
            fontSize: 12.sp,
            color: isOverLimit ? Colors.red : ResumeTheme.textSecondary,
            fontWeight: isOverLimit ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  /// 🔥 构建帮助说明卡片
  Widget _buildHelpCard() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ResumeTheme.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: ResumeTheme.primaryColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.help_outline,
                color: ResumeTheme.primaryColor,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '反馈指南',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: ResumeTheme.primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            '为了更好地帮助您解决问题，请您：\n'
            '• 详细描述遇到的问题或您的建议\n'
            '• 如果是功能问题，请说明具体的操作步骤\n'
            '• 如果是界面问题，请描述具体的页面位置\n'
            '• 我们会在1-3个工作日内回复您的反馈',
            style: TextStyle(
              fontSize: 12.sp,
              color: ResumeTheme.textSecondary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  /// 🔥 构建提交按钮
  Widget _buildSubmitButton(HelpFeedbackLogic logic) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        border: Border(
          top: BorderSide(
            color: ResumeTheme.borderColor,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton(
            onPressed: logic.canSubmit ? () => _handleSubmit(logic) : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: logic.canSubmit 
                  ? ResumeTheme.primaryColor 
                  : ResumeTheme.textSecondary.withOpacity(0.3),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: logic.isSubmitting
                ? SizedBox(
                    width: 20.w,
                    height: 20.w,
                    child: const CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    '提交反馈',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  /// 🔥 处理提交反馈
  void _handleSubmit(HelpFeedbackLogic logic) async {
    // 先进行表单验证
    if (!logic.validateForm()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(logic.errorSummary),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      await logic.submitFeedback();
      
      // 提交成功
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('反馈提交成功，感谢您的宝贵意见！'),
            backgroundColor: Colors.green,
          ),
        );
        
        // 延迟关闭页面
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            Navigator.pop(context);
          }
        });
      }
      
    } catch (e) {
      // 提交失败
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('提交失败：${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
