import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:flutter_kit/src/features/resume/theme/resume_theme.dart';
import '../logic/account_management_logic.dart';

/// 🔥 账号管理页面 - 遵循项目架构规范
/// 
/// 功能特性：
/// - ✅ 账号信息展示（头像、账号名、手机号）
/// - ✅ 底部注销按钮
/// - ✅ 符合项目设计规范
/// - ✅ 使用Logic + ChangeNotifier架构
class AccountManagementScreen extends StatefulWidget {
  const AccountManagementScreen({super.key});

  @override
  State<AccountManagementScreen> createState() => _AccountManagementScreenState();
}

class _AccountManagementScreenState extends State<AccountManagementScreen> {
  late AccountManagementLogic _logic;

  @override
  void initState() {
    super.initState();
    _logic = AccountManagementLogic();
    _loadAccountInfo();
  }

  void _loadAccountInfo() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _logic.loadAccountInfo();
    });
  }

  @override
  void dispose() {
    _logic.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _logic,
      child: Scaffold(
        backgroundColor: ResumeTheme.backgroundColor,
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  /// 🔥 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: ResumeTheme.surfaceColor,
      elevation: 0,
      title: Text(
        '账号管理',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: ResumeTheme.textPrimary,
        ),
      ),
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: Icon(
          Icons.arrow_back_ios,
          color: ResumeTheme.textPrimary,
          size: 20.w,
        ),
      ),
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(1.h),
        child: Container(
          height: 1.h,
          color: ResumeTheme.borderColor,
        ),
      ),
    );
  }

  /// 🔥 构建页面主体
  Widget _buildBody() {
    return Consumer<AccountManagementLogic>(
      builder: (context, logic, child) {
        return Column(
          children: [
            // 账号信息区域
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  children: [
                    SizedBox(height: 20.h),
                    // 账号信息卡片
                    _buildAccountInfoCard(logic),
                    SizedBox(height: 24.h),
                    // 其他管理选项
                    _buildManagementOptions(logic),
                  ],
                ),
              ),
            ),
            // 底部注销按钮
            _buildLogoutButton(logic),
          ],
        );
      },
    );
  }

  /// 🔥 构建账号信息卡片
  Widget _buildAccountInfoCard(AccountManagementLogic logic) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Row(
        children: [
          // 头像
          _buildAvatar(logic),
          SizedBox(width: 16.w),
          // 账号信息
          Expanded(
            child: _buildAccountInfo(logic),
          ),
          // 编辑按钮
          _buildEditButton(),
        ],
      ),
    );
  }

  /// 🔥 构建头像
  Widget _buildAvatar(AccountManagementLogic logic) {
    return Container(
      width: 60.w,
      height: 60.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: ResumeTheme.borderColor,
          width: 2,
        ),
      ),
      child: ClipOval(
        child: logic.avatarUrl != null && logic.avatarUrl!.isNotEmpty
            ? Image.network(
                logic.avatarUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(),
              )
            : _buildDefaultAvatar(),
      ),
    );
  }

  /// 🔥 构建默认头像
  Widget _buildDefaultAvatar() {
    return Container(
      width: 60.w,
      height: 60.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: ResumeTheme.primaryColor.withOpacity(0.1),
      ),
      child: Icon(
        Icons.person,
        color: ResumeTheme.primaryColor,
        size: 30.w,
      ),
    );
  }

  /// 🔥 构建账号信息
  Widget _buildAccountInfo(AccountManagementLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 账号名
        Text(
          logic.accountName ?? '未设置昵称',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: ResumeTheme.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: 6.h),
        // 手机号
        Text(
          logic.phoneNumber ?? '未绑定手机号',
          style: TextStyle(
            fontSize: 14.sp,
            color: ResumeTheme.textSecondary,
          ),
        ),
        SizedBox(height: 4.h),
        // 注册时间
        if (logic.registerTime != null)
          Text(
            '注册时间：${logic.registerTimeDisplay}',
            style: TextStyle(
              fontSize: 12.sp,
              color: ResumeTheme.textSecondary,
            ),
          ),
      ],
    );
  }

  /// 🔥 构建编辑按钮
  Widget _buildEditButton() {
    return GestureDetector(
      onTap: _handleEditProfile,
      child: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: ResumeTheme.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          Icons.edit,
          color: ResumeTheme.primaryColor,
          size: 20.w,
        ),
      ),
    );
  }

  /// 🔥 构建管理选项
  Widget _buildManagementOptions(AccountManagementLogic logic) {
    return Container(
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildOptionItem(
            icon: Icons.phone,
            title: '更换手机号',
            subtitle: '修改绑定的手机号码',
            onTap: _handleChangePhone,
          ),
          _buildDivider(),
          _buildOptionItem(
            icon: Icons.lock,
            title: '修改密码',
            subtitle: '更改登录密码',
            onTap: _handleChangePassword,
          ),
          _buildDivider(),
          _buildOptionItem(
            icon: Icons.security,
            title: '账号安全',
            subtitle: '安全设置和验证',
            onTap: _handleAccountSecurity,
          ),
          _buildDivider(),
          _buildOptionItem(
            icon: Icons.delete_outline,
            title: '注销账号',
            subtitle: '永久删除账号和所有数据',
            onTap: _handleDeleteAccount,
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  /// 🔥 构建选项项
  Widget _buildOptionItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            // 图标
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: (isDestructive ? Colors.red : ResumeTheme.primaryColor).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                color: isDestructive ? Colors.red : ResumeTheme.primaryColor,
                size: 20.w,
              ),
            ),
            SizedBox(width: 12.w),
            // 文本信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: isDestructive ? Colors.red : ResumeTheme.textPrimary,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: ResumeTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            // 箭头
            Icon(
              Icons.arrow_forward_ios,
              color: ResumeTheme.textSecondary,
              size: 16.w,
            ),
          ],
        ),
      ),
    );
  }

  /// 🔥 构建分割线
  Widget _buildDivider() {
    return Container(
      height: 1.h,
      margin: EdgeInsets.only(left: 68.w),
      color: ResumeTheme.borderColor,
    );
  }

  /// 🔥 构建注销按钮
  Widget _buildLogoutButton(AccountManagementLogic logic) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        border: Border(
          top: BorderSide(
            color: ResumeTheme.borderColor,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton(
            onPressed: () => _handleLogout(logic),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: Text(
              '退出登录',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 🔥 处理编辑资料
  void _handleEditProfile() {
    // TODO: 跳转到编辑资料页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('编辑资料功能开发中')),
    );
  }

  /// 🔥 处理更换手机号
  void _handleChangePhone() {
    // TODO: 跳转到更换手机号页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('更换手机号功能开发中')),
    );
  }

  /// 🔥 处理修改密码
  void _handleChangePassword() {
    // TODO: 跳转到修改密码页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('修改密码功能开发中')),
    );
  }

  /// 🔥 处理账号安全
  void _handleAccountSecurity() {
    // TODO: 跳转到账号安全页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('账号安全功能开发中')),
    );
  }

  /// 🔥 处理注销账号
  void _handleDeleteAccount() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('注销账号'),
        content: const Text('注销后将无法恢复账号和数据，确定要继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: 实现注销账号逻辑
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('注销账号功能开发中')),
              );
            },
            child: const Text('确定', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// 🔥 处理退出登录
  void _handleLogout(AccountManagementLogic logic) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('退出登录'),
        content: const Text('确定要退出当前账号吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              logic.logout();
              // TODO: 跳转到登录页面
              Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
            },
            child: Text(
              '确定',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
