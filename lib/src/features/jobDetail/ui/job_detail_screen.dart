import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/base.dart';
import 'package:flutter_kit/src/datasource/repositories/job_detail_repository.dart';
import 'package:flutter_kit/src/features/base_info/ui/widget/build_upload_avatar_option.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import '../../base_info/ui/widget/build_app_bar.dart';
import '../logic/job_detail_logic.dart';
import 'job_detail_view.dart';
import 'widgets/job_detail_skeleton.dart';

@RoutePage()
class JobDetailScreen extends ViewStateWidget<JobDetailLogic> {
  final String jobId;

  const JobDetailScreen({super.key, required this.jobId});

  @override
  JobDetailLogic createController() {
    return JobDetailLogic(
      repository: locator<JobDetailRepository>(),
      jobId: jobId,
    );
  }

  @override
  Widget? buildCustomAppBar(BuildContext context, JobDetailLogic logic) {
    return customTitleBar(context,'职位详情',leftCallback: (){
      context.router.pop();
    });
  }

  /// 重写自定义加载Widget，返回骨架屏

  Widget? buildCustomLoadingWidget() {
    return const JobDetailSkeleton();
  }

  /// 自定义状态切换动画时长
  @override
  Duration getTransitionDuration() => const Duration(milliseconds: 400);

  /// 自定义状态切换动画 - 骨架屏到内容的平滑过渡
  @override
  Widget Function(Widget, Animation<double>) getTransitionBuilder() {
    return (Widget child, Animation<double> animation) {
      return FadeTransition(
        opacity: animation,
        child: child,
      );
    };
  }

  @override
  Widget buildBody(BuildContext context, JobDetailLogic logic) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      logic.loadData();
    });
    final jobDetail = logic.jobDetail;

    // 如果没有数据，返回空容器（此时会显示骨架屏）
    if (jobDetail == null) {
      return const SizedBox.shrink();
    }

    return Stack(
      children: [
        CustomScrollView(
          slivers: [
            SliverPadding(
              padding: EdgeInsets.fromLTRB(16, 0, 16, 80 + MediaQuery.of(context).viewPadding.bottom + 20), // 为FooterBar留空间，包含安全区域
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  const SizedBox(height: 10),
                  JobTitleCard(jobDetailEntity: jobDetail),
                  const SizedBox(height: 20),
                  JobDescriptionCard(
                    jobDetailEntity: jobDetail,
                  ),
                  const SizedBox(height: 20),
                  JobHighlightsCard(),
                  const SizedBox(height: 20),
                  CompanyInfoCard(
                    jobDetailEntity: jobDetail,
                  ),
                  const SizedBox(height: 20),
                ]),
              ),
            ),
          ],
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: FooterBar(),
        ),
      ],
    );
  }
}
