import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kit/src/datasource/models/area_model_entity.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import 'package:provider/provider.dart';
import '../../logic/location_logic.dart';
import '../../providers/location_provider.dart';
import 'district_selection_dialog.dart';
import 'city_selection_dialog.dart';

/// 🎯 城市数据模型（替代AzListView）
class CityItem {
  final AreaModelEntity area;
  final String tag;
  final bool isHeader;

  CityItem({
    required this.area,
    required this.tag,
    this.isHeader = false,
  });
}



/// 🎨 地区选择页面视图组件
///
/// 功能特性：
/// - ✅ 使用Provider + ChangeNotifier（项目标准架构）
/// - ✅ 完全避免setState，使用ChangeNotifier状态管理
/// - ✅ 搜索状态独立管理，避免重复监听
/// - ✅ 性能优化：搜索防抖、状态分离、缓存优先
/// - ✅ 符合项目技术栈要求
/// - ✅ 优先展示城市，省份在最后
/// - ✅ 支持本地缓存和强制刷新
class LocationPageView extends StatelessWidget {
  final LocationLogic logic;
  final AreaLevel level;
  final String title;
  final AreaSelectionResult? initialSelection;

  const LocationPageView({
    super.key,
    required this.logic,
    required this.level,
    required this.title,
    this.initialSelection,
  });

  @override
  Widget build(BuildContext context) {
    // 🔥 使用MultiProvider管理多个状态
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: logic),
        ChangeNotifierProvider(create: (_) => SearchProvider()),
        ChangeNotifierProvider(create: (_) => NavigationProvider()),
      ],
      child: Scaffold(
        // 🔥 全局键盘优化
        resizeToAvoidBottomInset: false,
        body: const _LocationPageContent(),
      ),
    );
  }
}




/// 📱 页面内容组件
class _LocationPageContent extends StatefulWidget {
  const _LocationPageContent();

  @override
  State<_LocationPageContent> createState() => _LocationPageContentState();
}

class _LocationPageContentState extends State<_LocationPageContent>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // 🔥 性能优化：缓存构建的Widget
  final Map<String, Widget> _widgetCache = {};

  // 🔥 键盘优化：避免重复监听
  bool _keyboardListenerAdded = false;

  @override
  bool get wantKeepAlive => true; // 🔥 保持页面状态，避免重建

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);

    // 🔥 键盘监听优化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupKeyboardListener();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _setupKeyboardListener() {
    if (!_keyboardListenerAdded) {
      _keyboardListenerAdded = true;
      // 🔥 预热键盘相关的渲染管道，避免首次弹出卡顿
      MediaQuery.of(context).viewInsets.bottom;
      // 🔥 预热FocusNode，提升键盘响应速度
      FocusScope.of(context);
    }
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    final searchProvider = context.read<SearchProvider>();
    final logic = context.read<LocationLogic>();

    // 使用Provider的搜索功能
    if (query.isEmpty) {
      searchProvider.stopSearch();
    } else {
      searchProvider.startSearch();
      final results = logic.searchAreas(query);
      searchProvider.updateSearchResults(results, query);
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 🔥 AutomaticKeepAliveClientMixin 必需

    const double searchBarHeight = 64;

    return MediaQuery.removePadding(
      context: context,
      removeTop: true,
      child: Column(
        children: [
          // 🔥 顶部搜索框
          Material(
            elevation: 2,
            color: Colors.white,
            child: SizedBox(
              height: searchBarHeight,
              child: _buildSearchBar(),
            ),
          ),
          // 🔥 主内容
          Expanded(
            child: Consumer2<LocationLogic, SearchProvider>(
                builder: (context, logic, searchProvider, child) {

                  // 🔥 关键修复：检查ViewState状态
                  if (logic.viewState.isLoading()) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (logic.viewState.isError()) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error_outline, size: 48, color: Colors.red),
                          SizedBox(height: 16),
                          Text('加载失败: ${logic.viewState.errorMessage}'),
                          SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => '',
                            child: Text('重试'),
                          ),
                        ],
                      ),
                    );
                  }

                  if (logic.viewState.isEmpty()) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.location_off, size: 48, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('暂无地区数据'),
                        ],
                      ),
                    );
                  }

                  // 检查数据是否真的为空
                  if (logic.provinces.isEmpty && logic.groupedCities.isEmpty && logic.hotCities.isEmpty) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.location_off, size: 48, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('暂无地区数据'),
                        ],
                      ),
                    );
                  }

                  if (searchProvider.isSearching) {
                    return _buildSearchListView(searchProvider.searchResults, searchBarHeight);
                  }
                  try {
                    final result = _buildNativeListView(logic, searchBarHeight);
                    return result;
                  } catch (e) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error, size: 48, color: Colors.red),
                          SizedBox(height: 16),
                          Text('列表构建失败: $e'),
                          SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => '',
                            child: Text('重试'),
                          ),
                        ],
                      ),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      );
  }

  // 🔥 Widget缓存机制
  Widget _getCachedWidget(String key, Widget Function() builder) {
    return _widgetCache.putIfAbsent(key, builder);
  }

  // 🔥 高性能原生ListView：替代AzListView
  Widget _buildNativeListView(LocationLogic logic, double searchBarHeight) {
    final items = _buildListData(logic);

    // 🔥 如果没有数据，显示空状态
    if (items.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.only(top: searchBarHeight + 100),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.location_off, size: 48, color: Colors.grey),
              SizedBox(height: 16),
              Text('暂无地区数据', style: TextStyle(fontSize: 16, color: Colors.grey)),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => '',
                child: Text('重新加载'),
              ),
            ],
          ),
        ),
      );
    }

    try {

      return Container(
        color: Colors.white,
        child: _buildGridListView(items),
      );
    } catch (e) {
      return Container(
        color: Colors.yellow[100],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 48, color: Colors.red),
              SizedBox(height: 16),
              Text('渲染失败: $e'),
              SizedBox(height: 16),
              Text('数据项数: ${items.length}'),
            ],
          ),
        ),
      );
    }
  }

  // 🔥 构建网格列表视图（超高性能版本）
  Widget _buildGridListView(List<CityItem> items) {
    // 预处理数据，避免在itemBuilder中重复计算
    final processedRows = _preprocessGridData(items);

    return ListView.builder(
      controller: _scrollController,
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(16),
      itemCount: processedRows.length,
      // 🔥 性能优化参数
      cacheExtent: 500,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: false,
      itemBuilder: (context, index) {
        return _buildPreprocessedRow(processedRows[index]);
      },
    );
  }

  // 🔥 预处理网格数据
  List<List<CityItem>> _preprocessGridData(List<CityItem> items) {
    final List<List<CityItem>> rows = [];
    int i = 0;

    while (i < items.length) {
      final item = items[i];

      // 头部和间距项单独一行
      if (item.isHeader || item.tag == 'spacer') {
        rows.add([item]);
        i++;
      } else {
        // 普通项按3个一行分组
        final List<CityItem> rowItems = [];
        while (i < items.length &&
               !items[i].isHeader &&
               items[i].tag != 'spacer' &&
               rowItems.length < 3) {
          rowItems.add(items[i]);
          i++;
        }
        if (rowItems.isNotEmpty) {
          rows.add(rowItems);
        }
      }
    }

    return rows;
  }

  // 🔥 构建预处理的行
  Widget _buildPreprocessedRow(List<CityItem> rowItems) {
    if (rowItems.length == 1) {
      final item = rowItems[0];
      // 头部或间距项
      if (item.isHeader || item.tag == 'spacer') {
        return _buildListItem(item);
      }
    }

    // 普通项的网格行
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          for (int j = 0; j < 3; j++)
            Expanded(
              child: j < rowItems.length
                  ? Padding(
                      padding: EdgeInsets.only(right: j < 2 ? 8 : 0),
                      child: _buildGridItem(rowItems[j]),
                    )
                  : const SizedBox(),
            ),
        ],
      ),
    );
  }




  // 🔥 构建网格项（高性能版本）
  Widget _buildGridItem(CityItem item) {
    // 使用缓存避免重复构建
    final cacheKey = 'grid_item_${item.area.id}';
    return _getCachedWidget(cacheKey, () {
      return _GridItemWidget(
        area: item.area,
        onTap: () => _onAreaTap(item.area),
      );
    });
  }

  // 🔥 构建原生列表数据
  ///
  /// 数据展示顺序：
  /// 1. 热门城市（快捷选择）
  /// 2. 城市列表（按字母分组）
  /// 3. 省份列表（最后展示）
  List<CityItem> _buildListData(LocationLogic logic) {

    final List<CityItem> items = [];

    try {

    // 1. 添加热门城市（快捷选择）
    if (logic.hotCities.isNotEmpty) {
      final hotHeader = AreaModelEntity()
        ..id = 'hot_header'
        ..name = '热门城市'
        ..parent = '';
      items.add(CityItem(
        area: hotHeader,
        tag: '★',
        isHeader: true,
      ));

      for (final city in logic.hotCities) {
        items.add(CityItem(area: city, tag: '★'));
      }

      // 添加分隔间距
      items.add(CityItem(
        area: AreaModelEntity()..id = 'spacer_after_hot'..name = ''..parent = '',
        tag: 'spacer',
        isHeader: false,
      ));
    }

    // 2. 添加城市列表（按字母分组，优先展示）
    for (final group in logic.groupedCities) {
      // 添加分组头部
      final groupHeader = AreaModelEntity()
        ..id = '${group.letter}_header'
        ..name = group.letter
        ..parent = '';
      items.add(CityItem(
        area: groupHeader,
        tag: group.letter,
        isHeader: true,
      ));

      // 添加该分组下的城市
      for (final city in group.areas) {
        items.add(CityItem(area: city, tag: group.letter));
      }
    }

    // 3. 添加省份列表（最后展示）
    if (logic.provinces.isNotEmpty) {
      // 添加分隔间距
      items.add(CityItem(
        area: AreaModelEntity()..id = 'spacer_before_provinces'..name = ''..parent = '',
        tag: 'spacer',
        isHeader: false,
      ));

      final provinceHeader = AreaModelEntity()
        ..id = 'province_header'
        ..name = '省份/直辖市'
        ..parent = '';
      items.add(CityItem(
        area: provinceHeader,
        tag: '#',
        isHeader: true,
      ));

      for (final province in logic.provinces) {
        items.add(CityItem(area: province, tag: '#'));
      }
    }
      return items;
    } catch (e) {
      return [];
    }
  }

  // 🔥 构建列表项（只处理头部和间距项）
  Widget _buildListItem(CityItem item) {
    try {

      // 处理间距项
      if (item.tag == 'spacer') {
        return const SizedBox(height: 16);
      }

      // 处理分组头部
      if (item.isHeader) {
        return _buildSectionHeader(item.area.name, item.tag);
      }

      return const SizedBox.shrink();
    } catch (e) {
      return Container(
        height: 40,
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        color: Colors.red[100],
        child: Center(
          child: Text('渲染错误: ${item.area.name}'),
        ),
      );
    }
  }

  // 🔥 构建分组头部
  Widget _buildSectionHeader(String title, String tag) {
    Color textColor;
    IconData? icon;
    double fontSize = 16;
    FontWeight fontWeight = FontWeight.w600;

    switch (tag) {
      case '★': // 热门城市
        textColor = AppColors.primary;
        icon = Icons.star;
        fontSize = 19;
        fontWeight = FontWeight.w700;
        break;
      case '#': // 省份
        textColor = Colors.orange[700]!;
        icon = Icons.location_city;
        fontSize = 19;
        fontWeight = FontWeight.w700;
        break;
      default: // 字母分组
        textColor = AppColors.color_333333;
        fontSize = 15;
        fontWeight = FontWeight.w600;
    }

    return Container(
      height: 44,
      width: double.infinity,
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.fromLTRB(0, 10, 10, 10),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 18,
              color: textColor,
            ),
            const SizedBox(width: 8),
          ],
          Text(
            title,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: fontWeight,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  // 🔥 构建字母索引栏
  Widget _buildAlphabetSidebar(LocationLogic logic) {
    final tags = _buildIndexTags(logic);

    return SizedBox(
      width: 30,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: tags.map((tag) =>
          GestureDetector(
            onTap: () => _scrollToTag(tag),
            child: Container(
              height: 16,
              alignment: Alignment.center,
              child: Text(
                tag,
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.color_666666,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ).toList(),
      ),
    );
  }

  // 🔥 构建索引标签
  List<String> _buildIndexTags(LocationLogic logic) {
    final Set<String> tags = {'★'};

    // 添加字母索引
    for (final group in logic.groupedCities) {
      tags.add(group.letter);
    }

    // 添加省份索引
    if (logic.provinces.isNotEmpty) {
      tags.add('#');
    }

    return tags.toList();
  }

  // 🔥 滚动到指定标签
  void _scrollToTag(String tag) {
    final navProvider = context.read<NavigationProvider>();
    navProvider.showLetterIndicator(tag);
    // 触觉反馈
    HapticFeedback.lightImpact();

    // TODO: 实现滚动到指定位置的逻辑
  }





  // 🔥 构建字母指示器
  Widget _buildLetterIndicator(String letter) {
    return Center(
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.8),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            letter == '★' ? '热门' : (letter == '#' ? '省份' : letter),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  // 🔥 高性能搜索列表
  Widget _buildSearchListView(List<AreaModelEntity> searchResults, double searchBarHeight) {
    if (searchResults.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.only(top: searchBarHeight + 100),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.search_off, size: 48, color: Colors.grey),
              SizedBox(height: 16),
              Text('未找到相关城市', style: TextStyle(fontSize: 16, color: Colors.grey)),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      physics: const BouncingScrollPhysics(),
      padding: EdgeInsets.only(top: searchBarHeight),
      itemCount: searchResults.length,
      // 🔥 性能优化：固定item高度
      itemExtent: 56.0,
      cacheExtent: 500,
      itemBuilder: (context, index) {
        final area = searchResults[index];
        return ListTile(
          title: Text(area.name),
          onTap: () => _onAreaTap(area),
          // 🔥 优化点击反馈
          splashColor: AppColors.primary.withOpacity(0.1),
          hoverColor: AppColors.primary.withOpacity(0.05),
        );
      },
    );
  }



  // 🔥 优化的搜索框：减少重建
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: ValueListenableBuilder<TextEditingValue>(
        valueListenable: _searchController,
        builder: (context, value, child) {
          return TextField(
            controller: _searchController,
            // 🔥 键盘优化
            textInputAction: TextInputAction.search,
            keyboardType: TextInputType.text,
            decoration: InputDecoration(
              hintText: '搜索城市名/拼音',
              hintStyle: TextStyle(fontSize: 14, color: Colors.grey[500]),
              prefixIcon: Icon(Icons.search, color: Colors.grey[500], size: 20),
              suffixIcon: value.text.isNotEmpty
                  ? IconButton(
                      icon: Icon(Icons.clear, color: Colors.grey[500], size: 20),
                      onPressed: () {
                        _searchController.clear();
                        // 🔥 立即清除搜索状态
                        final searchProvider = context.read<SearchProvider>();
                        searchProvider.stopSearch();
                      },
                    )
                  : null,
              filled: true,
              fillColor: Colors.grey[50],
              border: const OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          );
        },
      ),
    );
  }







  void _onAreaTap(AreaModelEntity area) async {
    final logic = context.read<LocationLogic>();
    final result = logic.selectArea(area);
    if (result != null) {
      Navigator.of(context).pop(result);
    } else {
      // 需要弹窗选择下一级
      if (area.parent.isEmpty) {
        await _showCityDialog(area, logic);
      } else if (logic.level == AreaLevel.district) {
        await _showDistrictDialog(area, logic);
      }
    }
  }

  Future<void> _showCityDialog(
      AreaModelEntity province, LocationLogic logic) async {
    await CitySelectionDialog.show(
      context,
      province: province,
      logic: logic,
      onCitySelected: (result) {
        Navigator.of(context).pop(result);
      },
    );
  }

  Future<void> _showDistrictDialog(
      AreaModelEntity city, LocationLogic logic) async {
    await DistrictSelectionDialog.show(
      context,
      city: city,
      logic: logic,
      onDistrictSelected: (result) {
        Navigator.of(context).pop(result);
      },
    );
  }
}

// 🔥 高性能网格项组件
class _GridItemWidget extends StatelessWidget {
  final AreaModelEntity area;
  final VoidCallback onTap;

  const _GridItemWidget({
    required this.area,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 48,
      child: Material(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          splashColor: Colors.blue.withOpacity(0.1),
          highlightColor: Colors.blue.withOpacity(0.05),
          child: Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!, width: 0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              area.name,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }
}

// 🔥 高性能城市项组件
class _CityItem extends StatelessWidget {
  final AreaModelEntity area;
  final VoidCallback onTap;

  const _CityItem({required this.area, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40, // 🔥 固定高度，提升性能
      child: Material(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          // 🔥 优化点击反馈
          splashColor: AppColors.primary.withOpacity(0.1),
          highlightColor: AppColors.primary.withOpacity(0.05),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            alignment: Alignment.center,
            child: Text(
              area.name,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.color_333333,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }
}
