import 'package:flutter/material.dart';
import 'package:flutter_kit/src/datasource/models/area_model_entity.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import '../../logic/location_logic.dart';

/// 区县选择弹窗
///
/// 功能特性：
/// - ✅ 3列网格布局，优化空间利用
/// - ✅ 移除ScreenUtil依赖，使用原生响应式设计
/// - ✅ 优化交互体验和视觉效果
/// - ✅ 支持空状态和错误处理
/// - ✅ 符合项目设计规范
class DistrictSelectionDialog extends StatefulWidget {
  final AreaModelEntity city;
  final LocationLogic logic;
  final Function(AreaSelectionResult result) onDistrictSelected;

  const DistrictSelectionDialog({
    super.key,
    required this.city,
    required this.logic,
    required this.onDistrictSelected,
  });

  @override
  State<DistrictSelectionDialog> createState() => _DistrictSelectionDialogState();

  /// 显示弹窗
  static Future<void> show(
    BuildContext context, {
    required AreaModelEntity city,
    required LocationLogic logic,
    required Function(AreaSelectionResult result) onDistrictSelected,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DistrictSelectionDialog(
        city: city,
        logic: logic,
        onDistrictSelected: onDistrictSelected,
      ),
    );
  }
}

class _DistrictSelectionDialogState extends State<DistrictSelectionDialog> {
  List<AreaModelEntity> _districts = [];

  @override
  void initState() {
    super.initState();
    _loadDistricts();
  }

  void _loadDistricts() {
    try {
      _districts = widget.logic.getDistricts(widget.city.id);
      debugPrint('✅ DistrictSelectionDialog - 加载区县数据: ${_districts.length}条');
    } catch (e) {
      debugPrint('❌ DistrictSelectionDialog - 加载区县数据失败: $e');
      _districts = [];
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.75;
    final minHeight = screenHeight * 0.4;

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxHeight,
        minHeight: minHeight,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          Expanded(child: _buildContent()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!, width: 1),
        ),
      ),
      child: Row(
        children: [
          // 城市图标
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.location_city,
              size: 20,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '选择区县',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  widget.city.name,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          // 关闭按钮
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => Navigator.of(context).pop(),
              borderRadius: BorderRadius.circular(20),
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  Icons.close,
                  size: 24,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_districts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                Icons.location_off,
                size: 48,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              '暂无区县数据',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${widget.city.name}暂无下级区县',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        physics: const BouncingScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 2.2, // 稍微调整比例，让按钮更美观
        ),
        itemCount: _districts.length,
        itemBuilder: (context, index) {
          final district = _districts[index];
          return _buildDistrictItem(district);
        },
      ),
    );
  }

  Widget _buildDistrictItem(AreaModelEntity district) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          final result = widget.logic.selectDistrict(district);
          widget.onDistrictSelected(result);
          Navigator.of(context).pop(result);
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
          child: Text(
            district.name,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.color_333333,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }
}
