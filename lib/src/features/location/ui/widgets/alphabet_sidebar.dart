import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../core/theme/app_color.dart';

/// 字母导航状态管理器
class AlphabetSidebarProvider extends ChangeNotifier {
  String? _activeLetter;
  bool _isVisible = false;

  String? get activeLetter => _activeLetter;
  bool get isVisible => _isVisible;

  void setActiveLetter(String? letter) {
    if (_activeLetter != letter) {
      _activeLetter = letter;
      _isVisible = letter != null;
      notifyListeners();
    }
  }

  void clearActiveLetter() {
    if (_activeLetter != null || _isVisible) {
      _activeLetter = null;
      _isVisible = false;
      notifyListeners();
    }
  }

  void showOverlay(String letter) {
    _activeLetter = letter;
    _isVisible = true;
    notifyListeners();
  }

  void hideOverlay() {
    _isVisible = false;
    notifyListeners();
  }
}

/// 字母快捷导航侧边栏
///
/// 功能特性：
/// - ✅ 使用ChangeNotifier状态管理，完全避免setState
/// - ✅ 优化性能，减少重建
/// - ✅ 支持触摸反馈和视觉提示
/// - ✅ 符合项目架构标准
class AlphabetSidebar extends StatelessWidget {
  final List<String> letters;
  final Function(String letter) onLetterTap;
  final String? currentLetter;
  final double? height;

  const AlphabetSidebar({
    super.key,
    required this.letters,
    required this.onLetterTap,
    this.currentLetter,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => AlphabetSidebarProvider(),
      child: _AlphabetSidebarContent(
        letters: letters,
        onLetterTap: onLetterTap,
        currentLetter: currentLetter,
        height: height,
      ),
    );
  }
}

/// 字母侧边栏内容组件
class _AlphabetSidebarContent extends StatelessWidget {
  final List<String> letters;
  final Function(String letter) onLetterTap;
  final String? currentLetter;
  final double? height;

  const _AlphabetSidebarContent({
    required this.letters,
    required this.onLetterTap,
    this.currentLetter,
    this.height,
  });

  // 预缓存的样式和配置，避免重新计算
  static const double _kSidebarWidth = 32.0;
  static const double _kDefaultHeight = 400.0;
  static const double _kBorderRadius = 12.0;
  static const double _kFontSize = 12.0;
  static const double _kOverlaySize = 60.0;
  static const double _kOverlayFontSize = 24.0;
  static const double _kOverlayRight = 50.0;


  @override
  Widget build(BuildContext context) {
    final sidebarHeight = height ?? _kDefaultHeight;
    final itemHeight = sidebarHeight / letters.length;

    return Consumer<AlphabetSidebarProvider>(
      builder: (context, provider, child) {
        return Stack(
          children: [
            SizedBox(
              width: _kSidebarWidth,
              height: sidebarHeight,
              child: GestureDetector(
                onPanStart: (details) => _onPanStart(context, details, provider),
                onPanUpdate: (details) => _onPanUpdate(context, details, provider),
                onPanEnd: (details) => _onPanEnd(context, provider),
                child: Column(
                  children: letters.map((letter) =>
                    _buildLetterItem(context, letter, itemHeight, provider)
                  ).toList(),
                ),
              ),
            ),
            // 显示字母提示覆盖层
            if (provider.isVisible)
              _buildLetterOverlay(context, provider.activeLetter ?? ''),
          ],
        );
      },
    );
  }

  Widget _buildLetterItem(BuildContext context, String letter, double itemHeight, AlphabetSidebarProvider provider) {
    final isActive = letter == currentLetter;
    final isPressed = letter == provider.activeLetter;

    return GestureDetector(
      onTap: () {
        onLetterTap(letter);
        provider.clearActiveLetter();
      },
      onTapDown: (_) {
        provider.showOverlay(letter);
      },
      onTapUp: (_) {
        provider.hideOverlay();
      },
      onTapCancel: () {
        provider.hideOverlay();
      },
      child: Container(
        height: itemHeight,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: isPressed
              ? AppColors.primary.withOpacity(0.3)
              : isActive
                  ? AppColors.primary.withOpacity(0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(_kBorderRadius),
        ),
        child: Text(
          letter,
          style: TextStyle(
            fontSize: _kFontSize,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
            color: isActive ? AppColors.primary : AppColors.color_666666,
          ),
        ),
      ),
    );
  }

  Widget _buildLetterOverlay(BuildContext context, String letter) {
    return Positioned(
      right: _kOverlayRight,
      top: MediaQuery.of(context).size.height / 2 - (_kOverlaySize / 2),
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: _kOverlaySize,
          height: _kOverlaySize,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          alignment: Alignment.center,
          child: Text(
            letter,
            style: const TextStyle(
              fontSize: _kOverlayFontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  void _onPanStart(BuildContext context, DragStartDetails details, AlphabetSidebarProvider provider) {
    final letter = _getLetterFromPosition(details.localPosition);
    if (letter != null) {
      provider.showOverlay(letter);
      onLetterTap(letter);
    }
  }

  void _onPanUpdate(BuildContext context, DragUpdateDetails details, AlphabetSidebarProvider provider) {
    final letter = _getLetterFromPosition(details.localPosition);
    if (letter != null && letter != provider.activeLetter) {
      provider.showOverlay(letter);
      onLetterTap(letter);
    }
  }

  void _onPanEnd(BuildContext context, AlphabetSidebarProvider provider) {
    provider.hideOverlay();
  }

  String? _getLetterFromPosition(Offset position) {
    final sidebarHeight = height ?? _kDefaultHeight;
    final containerHeight = sidebarHeight - 16.0; // 减去padding
    final itemHeight = containerHeight / letters.length;
    final index = ((position.dy - 8.0) / itemHeight).floor(); // 减去顶部padding

    if (index >= 0 && index < letters.length) {
      return letters[index];
    }
    return null;
  }
}


