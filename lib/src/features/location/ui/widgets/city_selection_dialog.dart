import 'package:flutter/material.dart';
import 'package:flutter_kit/src/datasource/models/area_model_entity.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import '../../location.dart';
import '../../logic/location_logic.dart';
import 'district_selection_dialog.dart';

/// 城市选择弹窗
/// 
/// 功能特性：
/// - ✅ 3列网格布局，优化空间利用
/// - ✅ 优化交互体验和视觉效果
/// - ✅ 支持空状态和错误处理
/// - ✅ 符合项目设计规范
/// - ✅ 省份选择后的城市选择
class CitySelectionDialog extends StatefulWidget {
  final AreaModelEntity province;
  final LocationLogic logic;
  final Function(AreaSelectionResult result) onCitySelected;

  const CitySelectionDialog({
    super.key,
    required this.province,
    required this.logic,
    required this.onCitySelected,
  });

  @override
  State<CitySelectionDialog> createState() => _CitySelectionDialogState();

  /// 显示弹窗
  static Future<void> show(
    BuildContext context, {
    required AreaModelEntity province,
    required LocationLogic logic,
    required Function(AreaSelectionResult result) onCitySelected,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => CitySelectionDialog(
        province: province,
        logic: logic,
        onCitySelected: onCitySelected,
      ),
    );
  }
}

class _CitySelectionDialogState extends State<CitySelectionDialog> {
  List<AreaModelEntity> _cities = [];

  @override
  void initState() {
    super.initState();
    _loadCities();
  }

  void _loadCities() {
    try {
      _cities = widget.logic.getCities(widget.province.id);
      debugPrint('✅ CitySelectionDialog - 加载城市数据: ${_cities.length}条');
    } catch (e) {
      debugPrint('❌ CitySelectionDialog - 加载城市数据失败: $e');
      _cities = [];
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.8;
    final minHeight = screenHeight * 0.5;

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxHeight,
        minHeight: minHeight,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          Expanded(child: _buildContent()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!, width: 1),
        ),
      ),
      child: Row(
        children: [
          // 省份图标
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.location_city,
              size: 20,
              color: Colors.orange[700],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '选择城市',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  widget.province.name,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          // 关闭按钮
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => Navigator.of(context).pop(),
              borderRadius: BorderRadius.circular(20),
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  Icons.close,
                  size: 24,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_cities.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                Icons.location_off,
                size: 48,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              '暂无城市数据',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${widget.province.name}暂无下级城市',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        physics: const BouncingScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 2.2,
        ),
        itemCount: _cities.length,
        itemBuilder: (context, index) {
          final city = _cities[index];
          return _buildCityItem(city);
        },
      ),
    );
  }

  Widget _buildCityItem(AreaModelEntity city) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () async {
          debugPrint('🔍 CitySelectionDialog - 选择城市: ${city.name}');
          final result = widget.logic.selectArea(city);
          if (result != null) {
            widget.onCitySelected(result);
            Navigator.of(context).pop();
          } else {
            // 需要继续选择区县
            final currentContext = context;
            Navigator.of(context).pop();
            await Future.delayed(const Duration(milliseconds: 100));
            // 检查context是否仍然有效
            if (mounted && currentContext.mounted) {
              await DistrictSelectionDialog.show(
                currentContext,
                city: city,
                logic: widget.logic,
                onDistrictSelected: (result) {
                  widget.onCitySelected(result);
                },
              );
            }
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
          child: Text(
            city.name,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.color_333333,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }
}
