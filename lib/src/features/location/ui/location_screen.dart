import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/datasource/repositories/location_repository.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import '../../../base/base.dart';
import '../logic/location_logic.dart';
import 'widgets/location_page_view.dart';

/// 🏗️ 标准架构地区选择页面
///
/// 功能特性：
/// - ✅ 使用ViewStateWidget进行状态管理（项目标准架构）
/// - ✅ 自动处理Loading/Success/Error状态
/// - ✅ 集成LocationPageView组件
/// - ✅ 支持数据自动加载和刷新
/// - ✅ 完全避免setState，使用项目标准架构
/// - ✅ 无复杂缓存，无ScreenUtil，性能优化
/// - ✅ 符合项目技术栈：Provider + ChangeNotifier
@RoutePage()
class LocationScreen extends ViewStateWidget<LocationLogic> {
  final AreaLevel level;
  final String title;
  final AreaSelectionResult? initialSelection;

  const LocationScreen({
    super.key,
    required this.level,
    this.title = '选择现居住地',
    this.initialSelection,
  });

  @override
  LocationLogic createController() {
    return LocationLogic(
      repository: locator<LocationRepository>(),
      level: level,
    );
  }

  @override
  Widget? buildCustomAppBar(BuildContext context, LocationLogic logic) {
    return Container(
      color: Colors.white,
      child: SafeArea(
        bottom: false,
        child: Column(
          children: [
            SizedBox(
              height: 56,
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back_ios, color: Colors.black87, size: 20),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  Expanded(
                    child: Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(width: 48), // 平衡左边的IconButton
                ],
              ),
            ),
            Container(
              height: 1,
              color: Colors.grey[200],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget buildBody(BuildContext context, LocationLogic logic) {
    // 🔥 关键：将logic传递给PageView，让PageView处理具体的UI展示
    // 这样完全避免了setState，使用项目标准的ViewStateWidget架构
    return LocationPageView(
      logic: logic,
      level: level,
      title: title,
      initialSelection: initialSelection,
    );
  }

  @override
  bool hasData() {
    // 🔥 修复：始终返回false，让ViewStateWidget正确处理Loading状态
    // 因为数据需要异步加载，初始时肯定没有数据
    return false;
  }

  @override
  bool resizeToAvoidBottomInset() {
    // 🔥 关键：防止键盘重建，优化键盘性能
    return false;
  }
}
