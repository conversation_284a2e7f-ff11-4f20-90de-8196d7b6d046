import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_kit/src/datasource/models/area_model_entity.dart';
import 'package:flutter_kit/src/shared/services/storage/storage.dart';
import 'package:flutter_kit/src/shared/locator.dart';

/// 地区数据缓存服务
/// 
/// 功能特性：
/// - ✅ 本地数据缓存和读取
/// - ✅ 缓存有效期管理
/// - ✅ 数据版本控制
/// - ✅ 异常处理和降级策略
/// - ✅ 符合项目标准架构
class LocationCacheService {
  static const String _cacheKey = 'location_area_data';
  static const String _cacheVersionKey = 'location_cache_version';
  static const String _cacheTimestampKey = 'location_cache_timestamp';
  
  /// 缓存有效期：7天
  static const Duration _cacheValidDuration = Duration(days: 7);
  
  /// 当前缓存版本
  static const String _currentCacheVersion = '1.0.0';
  
  final Storage _storage = locator<Storage>();

  /// 缓存地区数据
  /// 
  /// [areas] 地区数据列表
  /// 返回是否缓存成功
  Future<bool> cacheAreaData(List<AreaModelEntity> areas) async {
    try {
      // 转换为JSON字符串
      final jsonList = areas.map((area) => area.toJson()).toList();
      final jsonString = jsonEncode(jsonList);
      
      // 保存数据
      final dataSuccess = await _storage.writeString(
        key: _cacheKey, 
        value: jsonString,
      );
      
      // 保存版本信息
      final versionSuccess = await _storage.writeString(
        key: _cacheVersionKey, 
        value: _currentCacheVersion,
      );
      
      // 保存时间戳
      final timestampSuccess = await _storage.writeInt(
        key: _cacheTimestampKey, 
        value: DateTime.now().millisecondsSinceEpoch,
      );
      
      final success = dataSuccess && versionSuccess && timestampSuccess;
      
      if (success) {
        debugPrint('🔄 LocationCacheService: 缓存地区数据成功，共${areas.length}条记录');
      } else {
        debugPrint('❌ LocationCacheService: 缓存地区数据失败');
      }
      
      return success;
    } catch (e) {
      debugPrint('❌ LocationCacheService: 缓存数据异常 - $e');
      return false;
    }
  }

  /// 获取缓存的地区数据
  /// 
  /// 返回缓存的地区数据列表，如果无有效缓存则返回null
  Future<List<AreaModelEntity>?> getCachedAreaData() async {
    try {
      // 检查缓存是否有效
      if (!await _isCacheValid()) {
        debugPrint('🔄 LocationCacheService: 缓存已过期或无效');
        return null;
      }
      
      // 读取缓存数据
      final jsonString = await _storage.read<String>(key: _cacheKey);
      if (jsonString == null || jsonString.isEmpty) {
        debugPrint('🔄 LocationCacheService: 无缓存数据');
        return null;
      }
      
      // 解析JSON数据
      final jsonList = jsonDecode(jsonString) as List<dynamic>;
      final areas = jsonList
          .map((json) => AreaModelEntity.fromJson(json as Map<String, dynamic>))
          .toList();
      
      debugPrint('✅ LocationCacheService: 读取缓存数据成功，共${areas.length}条记录');
      return areas;
    } catch (e) {
      debugPrint('❌ LocationCacheService: 读取缓存数据异常 - $e');
      return null;
    }
  }

  /// 检查缓存是否有效
  /// 
  /// 检查版本和时间戳
  Future<bool> _isCacheValid() async {
    try {
      // 检查版本
      final cachedVersion = await _storage.read<String>(key: _cacheVersionKey);
      if (cachedVersion != _currentCacheVersion) {
        debugPrint('🔄 LocationCacheService: 缓存版本不匹配 - 缓存:$cachedVersion, 当前:$_currentCacheVersion');
        return false;
      }
      
      // 检查时间戳
      final cachedTimestamp = await _storage.read<int>(key: _cacheTimestampKey);
      if (cachedTimestamp == null) {
        debugPrint('🔄 LocationCacheService: 无缓存时间戳');
        return false;
      }
      
      final cachedTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();
      final difference = now.difference(cachedTime);
      
      if (difference > _cacheValidDuration) {
        debugPrint('🔄 LocationCacheService: 缓存已过期 - 缓存时间:$cachedTime, 当前时间:$now');
        return false;
      }
      
      return true;
    } catch (e) {
      debugPrint('❌ LocationCacheService: 检查缓存有效性异常 - $e');
      return false;
    }
  }

  /// 清除缓存
  /// 
  /// 清除所有地区数据缓存
  Future<bool> clearCache() async {
    try {
      await _storage.remove(key: _cacheKey);
      await _storage.remove(key: _cacheVersionKey);
      await _storage.remove(key: _cacheTimestampKey);
      
      debugPrint('🔄 LocationCacheService: 缓存清除成功');
      return true;
    } catch (e) {
      debugPrint('❌ LocationCacheService: 清除缓存异常 - $e');
      return false;
    }
  }

  /// 获取缓存信息
  /// 
  /// 返回缓存的基本信息，用于调试
  Future<Map<String, dynamic>> getCacheInfo() async {
    try {
      final version = await _storage.read<String>(key: _cacheVersionKey);
      final timestamp = await _storage.read<int>(key: _cacheTimestampKey);
      final hasData = await _storage.containsKey(key: _cacheKey);
      
      DateTime? cachedTime;
      if (timestamp != null) {
        cachedTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      
      return {
        'hasData': hasData,
        'version': version,
        'cachedTime': cachedTime?.toIso8601String(),
        'isValid': await _isCacheValid(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }
}
