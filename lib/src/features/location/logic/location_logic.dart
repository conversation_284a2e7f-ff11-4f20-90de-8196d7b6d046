import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/datasource/models/area_model_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/location_repository.dart';

/// 列表项类型枚举
enum ListItemType {
  sectionHeader,  // 区块标题（如"快捷选择"）
  groupHeader,    // 分组标题（如"A", "B", "#"）
  cityRow,        // 城市行（3个城市）
  provinceRow,    // 省份行（3个省份）
  spacer,         // 间距
}

/// 统一列表项数据结构
class ListItemData {
  final ListItemType type;
  final String title;
  final dynamic data; // 可以是 List<AreaModelEntity> 或其他数据

  const ListItemData({
    required this.type,
    required this.title,
    required this.data,
  });
}

/// 地区选择级别
enum AreaLevel {
  /// 到城市级别
  city,

  /// 到区级别
  district,
  province
}

/// 地区选择结果
class AreaSelectionResult {
  final AreaModelEntity? province;
  final AreaModelEntity? city;
  final AreaModelEntity? district;
  final AreaLevel level;

  const AreaSelectionResult({
    this.province,
    this.city,
    this.district,
    required this.level,
  });

  /// 获取完整地址字符串
  String get fullAddress {
    final parts = <String>[];
    if (province != null) parts.add(province!.name);
    if (city != null && city!.name != province?.name) parts.add(city!.name);
    if (district != null) parts.add(district!.name);
    return parts.join(' ');
  }

  /// 获取最终选中的地区
  AreaModelEntity get selectedArea {
    switch (level) {
      case AreaLevel.city:
        return city ?? province!;
      case AreaLevel.district:
        return district ?? city ?? province!;
      case AreaLevel.province:
        return province!;
    }
  }

  @override
  String toString() =>
      'AreaSelectionResult(fullAddress: $fullAddress, level: $level)';
}

/// 分组的地区数据
class GroupedAreaData {
  final String letter;
  final List<AreaModelEntity> areas;

  const GroupedAreaData({
    required this.letter,
    required this.areas,
  });
}

/// 地区选择逻辑
///
/// 功能特性：
/// - ✅ 使用ViewStateLogic标准架构，完全避免setState
/// - ✅ 集成缓存优先策略，提升用户体验
/// - ✅ 高性能数据处理，预计算索引和分组
/// - ✅ 统一的状态管理，通过ChangeNotifier通知UI更新
/// - ✅ 错误处理和降级策略
class LocationLogic extends ViewStateLogic {
  final LocationRepository repository;

  // 数据缓存
  List<AreaModelEntity> _allAreas = [];
  List<GroupedAreaData> _groupedCities = []; // 城市分组数据
  List<AreaModelEntity> _provinces = []; // 省份列表
  Map<String, List<AreaModelEntity>> _childrenCache = {}; // 父子关系缓存

  // 搜索索引缓存（预计算提升性能）
  Map<String, String> _pinyinCache = {}; // 地区ID -> 拼音
  Map<String, String> _firstLettersCache = {}; // 地区ID -> 首字母

  // 统一列表项数据（高性能ListView使用）
  List<ListItemData> _unifiedListItems = [];

  // 选择状态
  AreaModelEntity? _selectedProvince;
  AreaModelEntity? _selectedCity;
  final AreaLevel level;

  // 热门城市配置
  List<AreaModelEntity> _hotCities = [];

  // 数据加载状态标记
  bool _isDataProcessed = false;

  LocationLogic({required this.repository, required this.level}) {
    debugPrint('🔍 LocationLogic - 初始化，选择级别: $level');
  }

  // Getters
  List<GroupedAreaData> get groupedCities => _groupedCities;

  List<AreaModelEntity> get provinces => _provinces;



  AreaModelEntity? get selectedProvince => _selectedProvince;

  AreaModelEntity? get selectedCity => _selectedCity;

  List<AreaModelEntity> get hotCities => _hotCities;

  List<AreaModelEntity> get allAreas => _allAreas;

  /// 获取统一列表项数据（高性能ListView使用）
  List<ListItemData> get unifiedListItems => _unifiedListItems;

  @override
  void loadData() {
    debugPrint('🔍 LocationLogic - 开始加载数据');
    _fetchAreaData();
  }

  /// 获取地区数据
  ///
  /// 使用缓存优先策略，提升用户体验
  Future<void> _fetchAreaData() async {
    debugPrint('🔍 LocationLogic - 开始获取地区数据');

    sendRequest(
      repository.getAreaData(),
      successCallback: (data) {
        debugPrint('🔍 LocationLogic - 数据获取成功，记录数: ${data?.length ?? 0}');
        if (data != null && data.isNotEmpty) {
          _processAreaData(data);
        } else {
          debugPrint('⚠️ LocationLogic - 获取到空数据');
        }
      },
      failCallback: () {
        debugPrint('❌ LocationLogic - 数据获取失败');
        // ViewStateLogic会自动处理错误状态，这里不需要额外处理
      },
    );
  }

  /// 处理地区数据
  ///
  /// 统一的数据处理流程，确保数据一致性
  void _processAreaData(List<AreaModelEntity> data) {
    try {
      debugPrint('🔄 LocationLogic - 开始处理地区数据');

      _allAreas = data;
      _buildChildrenCache();
      _buildSearchIndexCache(); // 预计算搜索索引
      _buildGroupedCities();
      _buildProvinces();
      _buildHotCities();
      _buildUnifiedListItems(); // 构建统一列表项数据

      _isDataProcessed = true;

      debugPrint('✅ LocationLogic - 数据处理完成: '
          '省份=${_provinces.length}, '
          '城市分组=${_groupedCities.length}, '
          '热门城市=${_hotCities.length}');

      // 通过ChangeNotifier通知UI更新，完全避免setState
      notifyListeners();
    } catch (e) {
      debugPrint('❌ LocationLogic - 数据处理异常: $e');
      // 可以考虑设置错误状态
    }
  }

  /// 构建子级缓存
  void _buildChildrenCache() {
    _childrenCache.clear();
    for (final area in _allAreas) {
      if (area.parent.isNotEmpty) {
        _childrenCache.putIfAbsent(area.parent, () => []).add(area);
      }
    }
  }

  /// 预计算搜索索引缓存
  void _buildSearchIndexCache() {
    _pinyinCache.clear();
    _firstLettersCache.clear();

    for (final area in _allAreas) {
      // 预计算拼音
      _pinyinCache[area.id] = _getPinyin(area.name).toLowerCase();
      // 预计算首字母
      _firstLettersCache[area.id] = _getFirstLetters(area.name).toLowerCase();
    }
  }

  /// 构建分组的城市数据
  void _buildGroupedCities() {
    // 获取所有城市级别的地区
    // 城市的特征：要么是直辖市（parent为空），要么是地级市（parent不为空且有下级区县）
    final cities = _allAreas.where((area) {
      // 直辖市（如北京、上海、天津、重庆）
      if (area.parent.isEmpty) {
        return true;
      }

      // 地级市（parent不为空，且自己的id是别人的parent）
      if (area.parent.isNotEmpty) {
        return _allAreas.any((child) => child.parent == area.id);
      }

      return false;
    }).toList();

    // 按拼音首字母分组
    final Map<String, List<AreaModelEntity>> grouped = {};

    for (final city in cities) {
      final firstLetter = _getFirstLetter(city.name);
      grouped.putIfAbsent(firstLetter, () => []).add(city);
    }

    // 排序并转换为 GroupedAreaData
    final sortedKeys = grouped.keys.toList()..sort();
    _groupedCities = sortedKeys
        .map((letter) => GroupedAreaData(
              letter: letter,
              areas: grouped[letter]!..sort((a, b) => a.name.compareTo(b.name)),
            ))
        .toList();
  }

  /// 构建省份数据
  void _buildProvinces() {
    _provinces = _allAreas.where((area) => area.parent.isEmpty).toList();
    _provinces.sort((a, b) => a.name.compareTo(b.name));
  }

  /// 构建统一的列表项数据（用于高性能ListView）
  void _buildUnifiedListItems() {
    _unifiedListItems.clear();

    // 1. 添加热门城市部分
    if (hotCities.isNotEmpty) {
      _unifiedListItems.add(ListItemData(
        type: ListItemType.sectionHeader,
        title: '快捷选择',
        data: null,
      ));

      // 将热门城市按3列分组
      for (int i = 0; i < hotCities.length; i += 3) {
        final rowCities = hotCities.skip(i).take(3).toList();
        _unifiedListItems.add(ListItemData(
          type: ListItemType.cityRow,
          title: '',
          data: rowCities,
        ));
      }

      _unifiedListItems.add(ListItemData(
        type: ListItemType.spacer,
        title: '',
        data: null,
      ));
    }

    // 2. 添加分组城市
    for (final group in _groupedCities) {
      // 分组标题
      _unifiedListItems.add(ListItemData(
        type: ListItemType.groupHeader,
        title: group.letter,
        data: null,
      ));

      // 城市行（每行3个）
      for (int i = 0; i < group.areas.length; i += 3) {
        final rowCities = group.areas.skip(i).take(3).toList();
        _unifiedListItems.add(ListItemData(
          type: ListItemType.cityRow,
          title: '',
          data: rowCities,
        ));
      }

      _unifiedListItems.add(ListItemData(
        type: ListItemType.spacer,
        title: '',
        data: null,
      ));
    }

    // 3. 添加省份部分
    if (_provinces.isNotEmpty) {
      _unifiedListItems.add(ListItemData(
        type: ListItemType.groupHeader,
        title: '#',
        data: null,
      ));

      // 省份行（每行3个）
      for (int i = 0; i < _provinces.length; i += 3) {
        final rowProvinces = _provinces.skip(i).take(3).toList();
        _unifiedListItems.add(ListItemData(
          type: ListItemType.provinceRow,
          title: '',
          data: rowProvinces,
        ));
      }
    }
  }

  /// 构建热门城市列表
  void _buildHotCities() {
    // 热门城市名称列表
    final hotCityNames = [
      '汕头',
      '北京',
      '上海',
      '广州',
      '深圳',
      '杭州',
      '南京',
      '苏州',
      '成都',
      '武汉',
      '西安',
      '重庆',
    ];

    _hotCities.clear();

    // 查找热门城市
    for (final cityName in hotCityNames) {
      // 查找匹配的地区（包括省级城市和地级市）
      final areaIndex = _allAreas.indexWhere(
        (area) => area.name.contains(cityName),
      );

      if (areaIndex != -1) {
        _hotCities.add(_allAreas[areaIndex]);
      }
    }
  }

  /// 搜索地区（不触发UI更新，由SearchProvider管理搜索状态）
  /// 使用预计算的索引，大幅提升搜索性能
  List<AreaModelEntity> searchAreas(String query) {
    final trimmedQuery = query.trim();

    if (trimmedQuery.isEmpty) {
      return [];
    }

    final lowerQuery = trimmedQuery.toLowerCase();
    return _allAreas.where((area) {
      final name = area.name.toLowerCase();
      final pinyin = _pinyinCache[area.id] ?? '';
      final firstLetters = _firstLettersCache[area.id] ?? '';

      return name.contains(lowerQuery) ||
          pinyin.contains(lowerQuery) ||
          firstLetters.contains(lowerQuery);
    }).toList();
  }



  /// 获取城市列表
  List<AreaModelEntity> getCities(String provinceId) {
    return _childrenCache[provinceId] ?? [];
  }

  /// 获取区县列表
  List<AreaModelEntity> getDistricts(String cityId) {
    return _childrenCache[cityId] ?? [];
  }

  /// 选择地区
  AreaSelectionResult? selectArea(AreaModelEntity area) {
    if (area.parent.isEmpty) {
      // 这是省份
      _selectedProvince = area;

      if (level == AreaLevel.province) {
        // 只需要选择到省份级别
        return AreaSelectionResult(
          province: area,
          city: null,
          level: level,
        );
      }

      // 需要选择城市，返回null表示需要弹出城市选择
      return null;
    } else {
      // 判断是城市还是区县
      final hasChildren = _allAreas.any((child) => child.parent == area.id);

      if (hasChildren) {
        // 这是城市（有下级区县）
        _selectedCity = area;

        // 查找城市所属的省份
        final provinceIndex = _allAreas.indexWhere(
          (p) => p.id == area.parent,
        );
        if (provinceIndex != -1) {
          _selectedProvince = _allAreas[provinceIndex];
        }

        if (level == AreaLevel.city) {
          // 只需要选择到城市级别
          return AreaSelectionResult(
            province: _selectedProvince,
            city: _selectedCity,
            level: level,
          );
        }

        // 需要选择区县，返回null表示需要弹出区县选择
        return null;
      } else {
        // 这是区县（没有下级）
        final district = area;

        // 查找区县所属的城市
        final cityIndex = _allAreas.indexWhere(
          (c) => c.id == area.parent,
        );
        if (cityIndex != -1) {
          _selectedCity = _allAreas[cityIndex];

          // 查找城市所属的省份
          final provinceIndex = _allAreas.indexWhere(
            (p) => p.id == _selectedCity!.parent,
          );
          if (provinceIndex != -1) {
            _selectedProvince = _allAreas[provinceIndex];
          }
        }

        // 区县级别选择
        return AreaSelectionResult(
          province: _selectedProvince,
          city: _selectedCity,
          district: district,
          level: level,
        );
      }
    }
  }

  /// 选择区县
  AreaSelectionResult selectDistrict(AreaModelEntity district) {
    return AreaSelectionResult(
      province: _selectedProvince,
      city: _selectedCity,
      district: district,
      level: level,
    );
  }

  /// 获取拼音首字母
  String _getFirstLetter(String name) {
    // 汉字拼音首字母映射表
    final Map<String, String> charMap = {
      // A
      '阿': 'A',
      '安': 'A',
      '鞍': 'A',
      '澳': 'A',
      '奥': 'A',

      // B
      '北': 'B',
      '白': 'B',
      '保': 'B',
      '包': 'B',
      '本': 'B',
      '滨': 'B',
      '亳': 'B',
      '博': 'B',
      '巴': 'B',
      '蚌': 'B',
      '宝': 'B',
      '毕': 'B',
      '百': 'B',
      '璧': 'B',
      '滨': 'B',
      '滨': 'B',

      // C
      '重': 'C',
      '成': 'C',
      '长': 'C',
      '常': 'C',
      '昌': 'C',
      '沧': 'C',
      '承': 'C',
      '池': 'C',
      '赤': 'C',
      '崇': 'C',
      '滁': 'C',
      '潮': 'C',
      '郴': 'C',
      '楚': 'C',
      '慈': 'C',
      '磁': 'C',
      '从': 'C',
      '茶': 'C',
      '朝': 'C',
      '澄': 'C',
      '城': 'C',
      '陈': 'C',

      // D
      '大': 'D',
      '丹': 'D',
      '德': 'D',
      '定': 'D',
      '东': 'D',
      '都': 'D',
      '达': 'D',
      '大': 'D',
      '当': 'D',
      '德': 'D',
      '登': 'D',
      '邓': 'D',
      '迪': 'D',
      '定': 'D',
      '东': 'D',
      '都': 'D',
      '敦': 'D',
      '多': 'D',

      // E
      '鄂': 'E',
      '恩': 'E',
      '峨': 'E',
      '额': 'E',

      // F
      '福': 'F',
      '佛': 'F',
      '防': 'F',
      '抚': 'F',
      '阜': 'F',
      '丰': 'F',
      '凤': 'F',
      '富': 'F',
      '扶': 'F',
      '福': 'F',
      '抚': 'F',
      '阜': 'F',
      '奉': 'F',
      '汾': 'F',
      '费': 'F',
      '肥': 'F',

      // G
      '广': 'G',
      '贵': 'G',
      '甘': 'G',
      '赣': 'G',
      '桂': 'G',
      '果': 'G',
      '高': 'G',
      '固': 'G',
      '古': 'G',
      '光': 'G',
      '广': 'G',
      '贵': 'G',
      '桂': 'G',
      '赣': 'G',
      '甘': 'G',
      '格': 'G',
      '个': 'G',
      '巩': 'G',
      '共': 'G',
      '古': 'G',
      '关': 'G',
      '冠': 'G',
      '广': 'G',
      '贵': 'G',
      '桂': 'G',
      '国': 'G',
      '果': 'G',

      // H
      '河': 'H',
      '湖': 'H',
      '海': 'H',
      '黑': 'H',
      '杭': 'H',
      '合': 'H',
      '哈': 'H',
      '邯': 'H',
      '汉': 'H',
      '鹤': 'H',
      '衡': 'H',
      '呼': 'H',
      '葫': 'H',
      '怀': 'H',
      '淮': 'H',
      '黄': 'H',
      '惠': 'H',
      '霍': 'H',
      '红': 'H',
      '洪': 'H',
      '侯': 'H',
      '华': 'H',
      '化': 'H',
      '淮': 'H',
      '怀': 'H',
      '环': 'H',
      '桓': 'H',
      '黄': 'H',
      '惠': 'H',
      '会': 'H',
      '霍': 'H',

      // J
      '江': 'J',
      '吉': 'J',
      '济': 'J',
      '金': 'J',
      '晋': 'J',
      '荆': 'J',
      '九': 'J',
      '佳': 'J',
      '嘉': 'J',
      '焦': 'J',
      '揭': 'J',
      '锦': 'J',
      '景': 'J',
      '鸡': 'J',
      '集': 'J',
      '济': 'J',
      '冀': 'J',
      '蓟': 'J',
      '佳': 'J',
      '嘉': 'J',
      '建': 'J',
      '江': 'J',
      '焦': 'J',
      '胶': 'J',
      '揭': 'J',
      '金': 'J',
      '锦': 'J',
      '晋': 'J',
      '荆': 'J',
      '景': 'J',
      '靖': 'J',
      '九': 'J',
      '酒': 'J',
      '句': 'J',
      '莒': 'J',

      // K
      '开': 'K',
      '克': 'K',
      '昆': 'K',
      '库': 'K',
      '喀': 'K',
      '凯': 'K',
      '康': 'K',
      '克': 'K',
      '昆': 'K',
      '库': 'K',

      // L
      '辽': 'L',
      '兰': 'L',
      '拉': 'L',
      '来': 'L',
      '廊': 'L',
      '乐': 'L',
      '雷': 'L',
      '丽': 'L',
      '连': 'L',
      '聊': 'L',
      '临': 'L',
      '柳': 'L',
      '六': 'L',
      '龙': 'L',
      '陇': 'L',
      '娄': 'L',
      '泸': 'L',
      '吕': 'L',
      '洛': 'L',
      '漯': 'L',
      '吕': 'L',
      '绿': 'L',
      '滦': 'L',
      '栾': 'L',
      '罗': 'L',
      '洛': 'L',
      '漯': 'L',
      '陆': 'L',
      '鹿': 'L',
      '潞': 'L',
      '吕': 'L',

      // M
      '马': 'M',
      '茂': 'M',
      '眉': 'M',
      '梅': 'M',
      '绵': 'M',
      '牡': 'M',
      '满': 'M',
      '毛': 'M',
      '茅': 'M',
      '眉': 'M',
      '美': 'M',
      '蒙': 'M',
      '孟': 'M',
      '汨': 'M',
      '密': 'M',
      '绵': 'M',
      '民': 'M',
      '闽': 'M',
      '明': 'M',
      '牡': 'M',
      '墨': 'M',

      // N
      '南': 'N',
      '内': 'N',
      '宁': 'N',
      '怒': 'N',
      '那': 'N',
      '南': 'N',
      '内': 'N',
      '宁': 'N',
      '牛': 'N',
      '农': 'N',

      // P
      '攀': 'P',
      '盘': 'P',
      '平': 'P',
      '萍': 'P',
      '莆': 'P',
      '濮': 'P',
      '普': 'P',
      '蒲': 'P',
      '浦': 'P',
      '朴': 'P',

      // Q
      '青': 'Q',
      '秦': 'Q',
      '清': 'Q',
      '庆': 'Q',
      '曲': 'Q',
      '衢': 'Q',
      '泉': 'Q',
      '七': 'Q',
      '齐': 'Q',
      '黔': 'Q',
      '钦': 'Q',
      '琼': 'Q',
      '邱': 'Q',
      '曲': 'Q',
      '衢': 'Q',
      '全': 'Q',
      '泉': 'Q',
      '确': 'Q',

      // R
      '日': 'R',
      '荣': 'R',
      '如': 'R',
      '瑞': 'R',
      '汝': 'R',
      '儒': 'R',
      '乳': 'R',
      '润': 'R',
      '若': 'R',

      // S
      '上': 'S',
      '山': 'S',
      '陕': 'S',
      '四': 'S',
      '深': 'S',
      '沈': 'S',
      '石': 'S',
      '苏': 'S',
      '汕': 'S',
      '商': 'S',
      '韶': 'S',
      '邵': 'S',
      '绍': 'S',
      '十': 'S',
      '双': 'S',
      '朔': 'S',
      '松': 'S',
      '宿': 'S',
      '随': 'S',
      '遂': 'S',
      '三': 'S',
      '沙': 'S',
      '汕': 'S',
      '商': 'S',
      '上': 'S',
      '韶': 'S',
      '邵': 'S',
      '绍': 'S',
      '深': 'S',
      '沈': 'S',
      '十': 'S',
      '石': 'S',
      '双': 'S',
      '朔': 'S',
      '四': 'S',
      '松': 'S',
      '苏': 'S',
      '宿': 'S',
      '随': 'S',
      '遂': 'S',
      '绥': 'S',
      '孙': 'S',

      // T
      '天': 'T',
      '台': 'T',
      '太': 'T',
      '泰': 'T',
      '唐': 'T',
      '铁': 'T',
      '通': 'T',
      '铜': 'T',
      '图': 'T',
      '土': 'T',
      '吐': 'T',
      '屯': 'T',
      '拖': 'T',
      '托': 'T',
      '塔': 'T',
      '台': 'T',
      '太': 'T',
      '泰': 'T',
      '谈': 'T',
      '唐': 'T',
      '桃': 'T',
      '腾': 'T',
      '天': 'T',
      '铁': 'T',
      '通': 'T',
      '铜': 'T',
      '图': 'T',
      '土': 'T',
      '吐': 'T',
      '屯': 'T',

      // W
      '武': 'W',
      '无': 'W',
      '温': 'W',
      '威': 'W',
      '潍': 'W',
      '渭': 'W',
      '文': 'W',
      '乌': 'W',
      '吴': 'W',
      '芜': 'W',
      '梧': 'W',
      '五': 'W',
      '舞': 'W',
      '婺': 'W',
      '武': 'W',
      '务': 'W',
      '雾': 'W',
      '物': 'W',
      '勿': 'W',

      // X
      '西': 'X',
      '新': 'X',
      '厦': 'X',
      '仙': 'X',
      '咸': 'X',
      '湘': 'X',
      '襄': 'X',
      '孝': 'X',
      '忻': 'X',
      '信': 'X',
      '兴': 'X',
      '邢': 'X',
      '徐': 'X',
      '许': 'X',
      '宣': 'X',
      '玄': 'X',
      '锡': 'X',
      '夏': 'X',
      '霞': 'X',
      '峡': 'X',
      '厦': 'X',
      '仙': 'X',
      '咸': 'X',
      '香': 'X',
      '湘': 'X',
      '襄': 'X',
      '萧': 'X',
      '孝': 'X',
      '忻': 'X',
      '新': 'X',
      '信': 'X',
      '兴': 'X',
      '邢': 'X',
      '雄': 'X',
      '修': 'X',
      '秀': 'X',
      '徐': 'X',
      '许': 'X',
      '宣': 'X',
      '玄': 'X',
      '薛': 'X',
      '雪': 'X',
      '寻': 'X',
      '浔': 'X',

      // Y
      '云': 'Y',
      '银': 'Y',
      '宜': 'Y',
      '伊': 'Y',
      '雅': 'Y',
      '烟': 'Y',
      '延': 'Y',
      '盐': 'Y',
      '扬': 'Y',
      '阳': 'Y',
      '永': 'Y',
      '榆': 'Y',
      '玉': 'Y',
      '岳': 'Y',
      '运': 'Y',
      '鹰': 'Y',
      '营': 'Y',
      '益': 'Y',
      '义': 'Y',
      '宜': 'Y',
      '沂': 'Y',
      '忆': 'Y',
      '易': 'Y',
      '弋': 'Y',
      '奕': 'Y',
      '逸': 'Y',
      '阴': 'Y',
      '银': 'Y',
      '应': 'Y',
      '英': 'Y',
      '鹰': 'Y',
      '迎': 'Y',
      '营': 'Y',
      '永': 'Y',
      '用': 'Y',
      '优': 'Y',
      '尤': 'Y',
      '友': 'Y',
      '有': 'Y',
      '右': 'Y',
      '于': 'Y',
      '余': 'Y',
      '鱼': 'Y',
      '渝': 'Y',
      '榆': 'Y',
      '虞': 'Y',
      '玉': 'Y',
      '育': 'Y',
      '郁': 'Y',
      '裕': 'Y',
      '豫': 'Y',
      '元': 'Y',
      '原': 'Y',
      '源': 'Y',
      '远': 'Y',
      '苑': 'Y',
      '院': 'Y',
      '愿': 'Y',
      '月': 'Y',
      '岳': 'Y',
      '越': 'Y',
      '跃': 'Y',
      '云': 'Y',
      '运': 'Y',

      // Z
      '浙': 'Z',
      '中': 'Z',
      '珠': 'Z',
      '株': 'Z',
      '驻': 'Z',
      '资': 'Z',
      '淄': 'Z',
      '自': 'Z',
      '遵': 'Z',
      '枣': 'Z',
      '湛': 'Z',
      '张': 'Z',
      '漳': 'Z',
      '昭': 'Z',
      '肇': 'Z',
      '镇': 'Z',
      '郑': 'Z',
      '周': 'Z',
      '舟': 'Z',
      '诸': 'Z',
      '竹': 'Z',
      '涿': 'Z',
      '卓': 'Z',
      '紫': 'Z',
      '邹': 'Z',
      '左': 'Z',
      '柞': 'Z',
    };

    // 获取第一个字符
    final firstChar = name.substring(0, 1);

    // 如果是英文字母，直接返回大写
    final codeUnit = firstChar.codeUnitAt(0);
    if ((codeUnit >= 65 && codeUnit <= 90) ||
        (codeUnit >= 97 && codeUnit <= 122)) {
      return firstChar.toUpperCase();
    }

    // 查找汉字对应的拼音首字母
    return charMap[firstChar] ?? 'Z'; // 默认分组到Z
  }

  /// 获取拼音（简化版本）
  String _getPinyin(String name) {
    final Map<String, String> cityPinyinMap = {
      '北京': 'beijing',
      '天津': 'tianjin',
      '上海': 'shanghai',
      '重庆': 'chongqing',
      '河北': 'hebei',
      '山西': 'shanxi',
      '辽宁': 'liaoning',
      '吉林': 'jilin',
      '黑龙江': 'heilongjiang',
      '江苏': 'jiangsu',
      '浙江': 'zhejiang',
      '安徽': 'anhui',
      '福建': 'fujian',
      '江西': 'jiangxi',
      '山东': 'shandong',
      '河南': 'henan',
      '湖北': 'hubei',
      '湖南': 'hunan',
      '广东': 'guangdong',
      '广西': 'guangxi',
      '海南': 'hainan',
      '四川': 'sichuan',
      '贵州': 'guizhou',
      '云南': 'yunnan',
      '西藏': 'xizang',
      '陕西': 'shaanxi',
      '甘肃': 'gansu',
      '青海': 'qinghai',
      '宁夏': 'ningxia',
      '新疆': 'xinjiang',
      '内蒙古': 'neimenggu',
      '台湾': 'taiwan',
      '香港': 'xianggang',
      '澳门': 'aomen',
    };

    return cityPinyinMap[name] ?? name;
  }

  /// 获取拼音首字母
  String _getFirstLetters(String name) {
    final Map<String, String> firstLetterMap = {
      '北京': 'bj',
      '天津': 'tj',
      '上海': 'sh',
      '重庆': 'cq',
      '河北': 'hb',
      '山西': 'sx',
      '辽宁': 'ln',
      '吉林': 'jl',
      '黑龙江': 'hlj',
      '江苏': 'js',
      '浙江': 'zj',
      '安徽': 'ah',
      '福建': 'fj',
      '江西': 'jx',
      '山东': 'sd',
      '河南': 'hn',
      '湖北': 'hb',
      '湖南': 'hn',
      '广东': 'gd',
      '广西': 'gx',
      '海南': 'hn',
      '四川': 'sc',
      '贵州': 'gz',
      '云南': 'yn',
      '西藏': 'xz',
      '陕西': 'sx',
      '甘肃': 'gs',
      '青海': 'qh',
      '宁夏': 'nx',
      '新疆': 'xj',
      '内蒙古': 'nmg',
      '台湾': 'tw',
      '香港': 'xg',
      '澳门': 'am',
    };

    return firstLetterMap[name] ?? name.substring(0, 1);
  }

  /// 强制刷新数据
  ///
  /// 清除缓存并重新获取数据
  Future<void> forceRefresh() async {
    debugPrint('🔄 LocationLogic - 强制刷新数据');

    try {
      // 重置数据状态
      _isDataProcessed = false;
      _allAreas.clear();
      _groupedCities.clear();
      _provinces.clear();
      _hotCities.clear();
      _childrenCache.clear();
      _pinyinCache.clear();
      _firstLettersCache.clear();
      _unifiedListItems.clear();

      // 通知UI进入加载状态
      notifyListeners();

      // 调用Repository的强制刷新
      final response = await repository.forceRefresh();

      if (response.data != null) {
        _processAreaData(response.data!);
      } else {
      }
    } catch (e) {
    }
  }

  /// 获取缓存信息
  ///
  /// 用于调试和状态检查
  Future<Map<String, dynamic>> getCacheInfo() async {
    final cacheInfo = await repository.getCacheInfo();
    return {
      ...cacheInfo,
      'isDataProcessed': _isDataProcessed,
      'dataCount': _allAreas.length,
      'provincesCount': _provinces.length,
      'citiesCount': _groupedCities.fold<int>(0, (sum, group) => sum + group.areas.length),
      'hotCitiesCount': _hotCities.length,
    };
  }

  /// 清除所有缓存
  ///
  /// 清除本地缓存和内存数据
  Future<bool> clearAllCache() async {
    try {
      // 清除Repository缓存
      final success = await repository.clearCache();

      if (success) {
        // 清除内存数据
        _allAreas.clear();
        _groupedCities.clear();
        _provinces.clear();
        _hotCities.clear();
        _childrenCache.clear();
        _pinyinCache.clear();
        _firstLettersCache.clear();
        _unifiedListItems.clear();
        _isDataProcessed = false;

        // 通知UI更新
        notifyListeners();

        debugPrint('✅ LocationLogic - 缓存清除成功');
      }

      return success;
    } catch (e) {
      debugPrint('❌ LocationLogic - 清除缓存异常: $e');
      return false;
    }
  }

  /// 检查数据是否已处理
  bool get isDataProcessed => _isDataProcessed;

  /// 获取数据统计信息
  Map<String, int> get dataStats => {
    'totalAreas': _allAreas.length,
    'provinces': _provinces.length,
    'cities': _groupedCities.fold<int>(0, (sum, group) => sum + group.areas.length),
    'hotCities': _hotCities.length,
    'groupedCities': _groupedCities.length,
  };
}
