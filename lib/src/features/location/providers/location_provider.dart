import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../../datasource/models/area_model_entity.dart';

/// 搜索状态管理
class SearchProvider extends ChangeNotifier {
  bool _isSearching = false;
  List<AreaModelEntity> _searchResults = [];
  String _searchQuery = '';

  bool get isSearching => _isSearching;
  List<AreaModelEntity> get searchResults => _searchResults;
  String get searchQuery => _searchQuery;

  void startSearch() {
    if (!_isSearching) {
      _isSearching = true;
      notifyListeners();
    }
  }

  void stopSearch() {
    if (_isSearching) {
      _isSearching = false;
      _searchResults.clear();
      _searchQuery = '';
      notifyListeners();
    }
  }

  void updateSearchResults(List<AreaModelEntity> results, String query) {
    _searchResults = results;
    _searchQuery = query;
    notifyListeners();
  }
}

/// 导航状态管理
class NavigationProvider extends ChangeNotifier {
  String? _currentLetter;
  bool _isLetterIndicatorVisible = false;
  Timer? _hideTimer;

  String? get currentLetter => _currentLetter;
  bool get isLetterIndicatorVisible => _isLetterIndicatorVisible;

  void setCurrentLetter(String? letter) {
    if (_currentLetter != letter) {
      _currentLetter = letter;
      notifyListeners();
    }
  }

  void clearCurrentLetter() {
    if (_currentLetter != null) {
      _currentLetter = null;
      notifyListeners();
    }
  }

  /// 显示字母指示器
  void showLetterIndicator(String letter) {
    _currentLetter = letter;
    _isLetterIndicatorVisible = true;
    notifyListeners();

    // 取消之前的定时器
    _hideTimer?.cancel();

    // 1秒后自动隐藏
    _hideTimer = Timer(const Duration(milliseconds: 1000), () {
      hideLetterIndicator();
    });
  }

  /// 隐藏字母指示器
  void hideLetterIndicator() {
    if (_isLetterIndicatorVisible) {
      _isLetterIndicatorVisible = false;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _hideTimer?.cancel();
    super.dispose();
  }
}
