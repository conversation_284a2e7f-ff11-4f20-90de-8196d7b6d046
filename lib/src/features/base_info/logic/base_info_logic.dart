import 'package:flutter/foundation.dart';
import 'package:flutter_kit/generated/json/base_info_entity.g.dart';
import 'package:flutter_kit/src/base/logic/view_state_load_logic.dart';
import 'package:flutter_kit/src/datasource/models/base_info_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/base_info_repository.dart';
import 'package:flutter_kit/src/shared/mixins/form_validation_mixin.dart';

class BaseInfoLogic extends ViewStateLoadLogic with FormValidationMixin {
  final BaseInfoRepository repository;
  BaseInfoEntity? _baseInfo;
  BaseInfoEntity? _editingEntity;

  BaseInfoEntity? get baseInfoEntity => _baseInfo;
  BaseInfoEntity? get editingEntity => _editingEntity;

  /// 🔥 重写必填字段配置
  @override
  Map<String, String> get requiredFields =>
      FormValidationHelper.generateRequiredFields(CommonFieldConfigs.baseInfoFields);

  BaseInfoLogic({required this.repository});

  @override
  void loadData() {
    sendRequest<BaseInfoEntity>(repository.getBaseInfo(),
        successCallback: (data) {
      _baseInfo = data;
      // 初始化编辑实体
      _editingEntity = data?.copyWith() ?? BaseInfoEntity.empty();
      notifyListeners();
    });
  }

  /// 🔥 通用字段更新方法 - 真正的 entity[fieldName] = value 语法

  void update(String fieldName, dynamic value) {
    if (_editingEntity == null) return;

    // 🔥 使用扩展方法，真正实现 entity[fieldName] = value
    _editingEntity![fieldName] = value;
    notifyListeners();
  }

  /// 🔥 批量更新多个字段
  ///
  /// 使用示例：
  /// updateBatch({
  ///   'name': '张三',
  ///   'genderCode': 'M',
  ///   'phone': '13800138000'
  /// })
  void updateBatch(Map<String, dynamic> fields) {
    if (_editingEntity == null || fields.isEmpty) return;

    // 🔥 使用扩展方法批量设置，只触发一次notifyListeners
    _editingEntity!.setFields(fields);
    notifyListeners();
  }

  /// 🔥 简化的表单验证 - 使用Mixin提供的方法
  bool validateForm() {
    if (_editingEntity == null) return false;

    // 构建验证数据
    final data = <String, dynamic>{
      'name': _editingEntity!.name,
      'phone': _editingEntity!.phone,
      'email': _editingEntity!.email,
      'genderCode': _editingEntity!.genderCode,
      'birthday': _editingEntity!.birthday,
      'educationCode': _editingEntity!.educationCode,
      'workingAgeCode': _editingEntity!.workingAgeCode,
      'liveAreaName': _editingEntity!.liveAreaName,
    };

    // 🔥 使用Mixin的验证方法
    return validateRequiredFields(data);
  }

  /// 🔥 保存基本信息
  Future<void> saveBaseInfo() async {
    if (_editingEntity == null) return;

    // 🔥 简化的表单验证
    if (!validateForm()) {
      debugPrint('表单验证失败，请检查必填项');
      return;
    }

    // 🔥 验证通过，执行保存逻辑
    try {
      setLoading();

      // TODO: 实现实际的保存逻辑
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求

      // sendRequest<BaseInfoEntity>(
      //   repository.saveBaseInfo(_editingEntity!),
      //   successCallback: (data) {
      //     _baseInfo = data;
      //     _editingEntity = data?.copyWith();
      //     clearAllErrors(); // 保存成功后清除所有错误
      //   }
      // );

      // 模拟保存成功
      _baseInfo = _editingEntity?.copyWith();
      clearAllErrors();
      setSuccess(null);

      debugPrint('基本信息保存成功');

    } catch (e) {
      setError('保存失败: ${e.toString()}');
      debugPrint('保存基本信息失败: $e');
    }
  }
}
