import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../resume/theme/resume_theme.dart';
import '../../../resume/ui/widget/resume_view.dart';

/// 构建上传头像选项（圆形虚线边框 + 加号）
Widget buildUploadAvatarOption(BuildContext context) {
  return GestureDetector(
    onTap: () {
      Navigator.pop(context);
      _showImageSourcePicker(context);
    },
    child: Column(
      children: [
        Container(
          width: 70.w,
          height: 70.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.transparent, // 透明边框，使用CustomPainter绘制虚线
              width: 2,
            ),
          ),
          child: CustomPaint(
            painter: DashedCirclePainter(
              color: ResumeTheme.primaryColor.withValues(alpha: 0.6),
              strokeWidth: 2,
            ),
            child: Center(
              child: Icon(
                Icons.add,
                size: 24.w,
                color: ResumeTheme.primaryColor.withValues(alpha: 0.8),
              ),
            ),
          ),
        ),
      ],
    ),
  );
}
/// 显示图片来源选择器
void _showImageSourcePicker(BuildContext context) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return Container(
        decoration: BoxDecoration(
          color: ResumeTheme.surfaceColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.r),
            topRight: Radius.circular(16.r),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 头部标题
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      '取消',
                      style: TextStyle(color: ResumeTheme.textSecondary),
                    ),
                  ),
                  Text(
                    '选择图片来源',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: ResumeTheme.textPrimary,
                    ),
                  ),
                  SizedBox(width: 48.w), // 占位，保持标题居中
                ],
              ),
            ),
            Container(height: 1.h, color: ResumeTheme.borderColor),
          ],
        ),
      );
    },
  );
}


