import 'package:flutter/material.dart';
import 'package:flutter_kit/src/datasource/models/base_info_entity.dart';
import 'package:flutter_kit/src/features/base_info/logic/base_info_logic.dart';
import 'package:flutter_kit/src/features/location/location.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../../shared/utils/validation_utils.dart';
import '../../../resume/theme/resume_theme.dart';
import '../../../resume/ui/widget/resume_pickers.dart';
import '../../../resume/ui/widget/selectable_item.dart';
import '../../../resume/ui/widget/selectable_item2.dart';
import '../../../resume/ui/widget/text_edit_dialog.dart';
import 'build_divider.dart';
import 'build_upload_avatar_option.dart';
import 'get_image_provider.dart';

/// 基本信息页面视图组件
///
/// 🎯 纯UI组件，符合项目架构规范：
/// - ✅ 只负责UI展示，不处理状态管理
/// - ✅ 不使用setState，通过Logic层管理状态
/// - ✅ 通过回调将用户操作传递给Logic层
/// - ✅ 使用Consumer监听Logic状态变化
class BaseInfoPageView extends StatelessWidget {
  final BaseInfoLogic logic;

  const BaseInfoPageView({
    super.key,
    required this.logic,
  });

  /// 获取当前编辑的实体数据
  BaseInfoEntity get _editingEntity =>
      logic.editingEntity ?? BaseInfoEntity.empty();

  @override
  Widget build(BuildContext context) {
    // 🔥 使用Consumer监听Logic状态变化，符合项目架构
    return Consumer<BaseInfoLogic>(
      builder: (context, logic, child) {
        return ListView(
          padding: EdgeInsets.all(16.w),
          // 禁用物理滚动效果，减少性能开销
          physics: const ClampingScrollPhysics(),
          children: [
            RepaintBoundary(
              child: _buildPersonalInfoSection(context),
            ),
            SizedBox(height: 16.h),
            // 可以继续添加其他信息区块
            RepaintBoundary(
              child: _buildContactInfoSection(),
            ),
            SizedBox(height: 16.h),
            // 可以补充其他信息
            RepaintBoundary(
              child: _buildOtherInfoSection(context),
            ),
            SizedBox(height: 30.h),
          ],
        );
      },
    );
  }

  /// 构建个人信息区块
  Widget _buildPersonalInfoSection(BuildContext context) {
    return _buildStyledCard(
      title: '个人信息',
      child: Column(
        children: [
          _buildAvatarRow(context),
          buildDivider(),
          SelectableItem2(
              label: '姓名',
              title: '姓名',
              value: _editingEntity.name,
              hint: '请输入姓名',
              maxLength: 20,
              isRequired: true,
              inputType: InputType.textField,
              validationType: ValidationType.chineseName,  // 🔥 验证类型
              errorMessage: logic.getFieldError('name'),   // 🔥 显示错误信息
              fieldName: 'name',                           // 🔥 字段名
              onValidate: logic.setFieldError,             // 🔥 验证回调
              onSave: (value) => logic.update('name', value),
          ),
          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _editingEntity.genderCode,
            groundName: 'Gender',
            isRequired: true,
            label: '性别',
            title: '选择性别',
            errorMessage: logic.getFieldError('genderCode'),  // 🔥 显示错误信息
            fieldName: 'genderCode',                          // 🔥 字段名
            onValidate: logic.setFieldError,                  // 🔥 验证回调
            onChanged: (value) => logic.update('genderCode', value),
          ),
          buildDivider(),
          ResumePickers.birthYearPicker(
            context: context,
            currentValue: _editingEntity.birthday,
            onChanged: (value) {
              // 🔥 通过Logic层处理数据变更，符合架构规范
              logic.update('birthday', value);
            },
          ),
          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _editingEntity.maritalStatusCode,
            groundName: 'MaritalStatus',
            isRequired: true,
            label: '婚姻状况',
            title: '选择婚姻状况',
            onChanged: (value) {
              // 🔥 通过Logic层处理数据变更，符合架构规范
              logic.update('maritalStatusCode', value);
            },
          ),
          // buildDivider(),
          // ResumePickers.xmlPicker(
          //   context: context,
          //   currentValue: _editingEntity.nationCode,
          //   groundName: 'Nation',
          //   label: '民族',
          //   title: '选择民族',
          //   onChanged: (value) {
          //     // 优化：只在值真正改变时才调用setState
          //     if (_editingEntity.nationCode != value) {
          //       setState(() => _editingEntity.nationCode = value);
          //     }
          //   },
          // ),
          // buildDivider(),
          // ResumePickers.xmlPicker(
          //   context: context,
          //   currentValue: _editingEntity.nationCode,
          //   groundName: 'NationCode',
          //   label: '民族',
          //   title: '选择民族',
          //   onChanged: (value) {
          //     // 优化：只在值真正改变时才调用setState
          //     if (_editingEntity.nationCode != value) {
          //       setState(() => _editingEntity.nationCode = value);
          //     }
          //   },
          // ),
          // buildDivider(),
          // ResumePickers.xmlPicker(
          //   context: context,
          //   currentValue: _editingEntity.nationCode,
          //   groundName: 'NationCode',
          //   label: '民族',
          //   title: '选择民族',
          //   onChanged: (value) {
          //     // 优化：只在值真正改变时才调用setState
          //     if (_editingEntity.nationCode != value) {
          //       setState(() => _editingEntity.nationCode = value);
          //     }
          //   },
          // ),

          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _editingEntity.workingAgeCode,
            groundName: 'WorkAge',
            isRequired: true,
            label: '工龄',
            title: '请选择工龄',
            errorMessage: logic.getFieldError('workingAgeCode'),  // 🔥 显示错误信息
            fieldName: 'workingAgeCode',                          // 🔥 字段名
            onValidate: logic.setFieldError,                      // 🔥 验证回调
            onChanged: (value) => logic.update('workingAgeCode', value),
          ),

          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _editingEntity.educationCode,
            groundName: 'Education',
            isRequired: true,
            label: '最高学历',
            title: '请选择最高学历',
            onChanged: (value) {
              // 🔥 通过Logic层处理数据变更，符合架构规范
              logic.update('educationCode', value);
            },
          ),

          buildDivider(),
          ResumePickers.liveAreaPicker(
            context: context,
            currentValue: _editingEntity.liveAreaName,
            isRequired: true,
            level: AreaLevel.district,
            label: '现居住地',
            title: '请选择现居住地',
            onChanged: (value) {
              // 🔥 通过Logic层处理数据变更，符合架构规范
              logic.update('liveAreaName', value.fullAddress);
            },
          ),

          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _editingEntity.labels,
            groundName: 'ResumeLabel',
            isRequired: true,
            label: '个人标签',
            title: '请选择个人标签',
            onChanged: (value) {
              // 🔥 通过Logic层处理数据变更，符合架构规范
              logic.update('labels', value);
            },
          ),
        ],
      ),
    );
  }

  /// 构建联系方式区块
  Widget _buildContactInfoSection() {
    return _buildStyledCard(
        title: '联系方式',
        child: Column(
          children: [
            buildDivider(),
            SelectableItem2(
                label: '手机号码',
                title: '手机号码',
                value: _editingEntity.phone,
                hint: '请输入手机号码',
                maxLength: 25,
                isRequired: true,
                inputType: InputType.textField,
                validationType: ValidationType.phone,      // 🔥 验证类型
                errorMessage: logic.getFieldError('phone'), // 🔥 显示错误信息
                fieldName: 'phone',                        // 🔥 字段名
                onValidate: logic.setFieldError,           // 🔥 验证回调
                onSave: (value) => logic.update('phone', value),
            ),
            buildDivider(),
            SelectableItem2(
                label: '邮箱',
                title: '邮箱',
                value: _editingEntity.email,
                hint: '请输入邮箱',
                maxLength: 25,
                isRequired: false,
                inputType: InputType.textField,
                validationType: ValidationType.email,      // 🔥 验证类型
                errorMessage: logic.getFieldError('email'), // 🔥 显示错误信息
                fieldName: 'email',                        // 🔥 字段名
                onValidate: logic.setFieldError,           // 🔥 验证回调
                onSave: (value) => logic.update('email', value),
            ),
          ],
        ));
  }

  /// 构建其他信息区块
  Widget _buildOtherInfoSection(BuildContext context) {
    return _buildStyledCard(
        title: '其他信息',
        child: Column(
          children: [
            buildDivider(),
            SelectableItem2(
                label: '毕业院校',
                title: '毕业院校',
                value: _editingEntity.graduateSchool,
                hint: '请输入毕业院校',
                maxLength: 25,
                isRequired: false,
                inputType: InputType.textField,
                onSave: (value) {
                  // 🔥 通过Logic层处理数据变更，符合架构规范
                  logic.update('graduateSchool', value);
                }),
            buildDivider(),
            SelectableItem2(
                label: '主修专业',
                title: '主修专业',
                value: _editingEntity.majorIn,
                hint: '请输入主修专业',
                maxLength: 25,
                isRequired: false,
                inputType: InputType.textField,
                onSave: (value) {
                  // 🔥 通过Logic层处理数据变更，符合架构规范
                  logic.update('majorIn', value);
                }),
            buildDivider(),
            ResumePickers.xmlPicker(
              context: context,
              currentValue: _editingEntity.computerLevelCode,
              groundName: 'ComputerLevel',
              label: '计算机等级',
              title: '请选择计算机等级',
              isRequired: false,
              onChanged: (value) {
                // 🔥 通过Logic层处理数据变更，符合架构规范
                logic.update('computerLevelCode', value);
              },
            ),
            buildDivider(),
            ResumePickers.xmlPicker(
              context: context,
              currentValue: _editingEntity.englishLevelCode,
              groundName: 'EnglishLevel',
              isRequired: false,
              label: '英语等级',
              title: '请选择英语等级',
              onChanged: (value) {
                // 🔥 通过Logic层处理数据变更，符合架构规范
                logic.update('englishLevelCode', value);
              },
            ),
          ],
        ));
  }

  /// 构建头像行
  Widget _buildAvatarRow(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _showAvatarPicker(context);
      },
      behavior: HitTestBehavior.opaque, // 确保整个区域都可以点击
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '头像',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: ResumeTheme.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 7.h),
                  Text(
                    '点击修改头像',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: ResumeTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.w), // 添加间距
            Container(
              width: 56.w,
              height: 56.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                  image: getImageProvider(''),
                  fit: BoxFit.cover,
                ),
                border: Border.all(
                  color: ResumeTheme.borderColor,
                  width: 1,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示头像选择器
  void _showAvatarPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: ResumeTheme.surfaceColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r),
              topRight: Radius.circular(16.r),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 头部标题
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        '取消',
                        style: TextStyle(color: ResumeTheme.textSecondary),
                      ),
                    ),
                    Text(
                      '选择头像',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: ResumeTheme.textPrimary,
                      ),
                    ),
                    SizedBox(width: 48.w), // 占位，保持标题居中
                  ],
                ),
              ),
              Container(height: 1.h, color: ResumeTheme.borderColor),

              // 默认头像选项
              Padding(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 16.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildAvatarOption(
                          context,
                          'assets/images/ic_head_kt1.png',
                          isAsset: true,
                        ),
                        _buildAvatarOption(
                          context,
                          'assets/images/ic_head_kt2.png',
                          isAsset: true,
                        ),
                        buildUploadAvatarOption(context),
                      ],
                    ),
                    SizedBox(height: 30.h),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 统一样式的卡片容器
  Widget _buildStyledCard({
    required String title,
    required Widget child,
    String? actionText,
    VoidCallback? onActionTap,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: ResumeTheme.textPrimary,
                  ),
                ),
                if (actionText != null)
                  GestureDetector(
                    onTap: onActionTap,
                    child: Text(
                      actionText,
                      style: TextStyle(
                        color: ResumeTheme.primaryColor,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )
              ],
            ),
          ),
          Container(
            height: 1.h,
            color: ResumeTheme.borderColor,
          ),
          child,
        ],
      ),
    );
  }

  /// 构建头像选项
  Widget _buildAvatarOption(BuildContext context, String imagePath,
      {bool isAsset = false}) {
    // final isSelected = _baseInfoEntity.a == imagePath;
    return GestureDetector(
      onTap: () {
        // 🔥 通过Logic层处理头像更新，符合架构规范
        logic.update('',imagePath);
        Navigator.pop(context);
      },
      child: Column(
        children: [
          Container(
            width: 70.w,
            height: 70.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                image: isAsset
                    ? AssetImage(imagePath) as ImageProvider
                    : NetworkImage(imagePath),
                fit: BoxFit.cover,
              ),
              border: Border.all(
                  // color: isSelected ? ResumeTheme.primaryColor : ResumeTheme
                  //     .borderColor,
                  // width: isSelected ? 3 : 1,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
