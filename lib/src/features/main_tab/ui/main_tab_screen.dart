import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/widgets/view_state_widget.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import 'package:flutter_kit/src/features/discover/ui/discover_screen.dart';
import 'package:flutter_kit/src/features/home/<USER>/home_screen_new.dart';
import 'package:flutter_kit/src/features/main_tab/logic/main_tab_logic.dart';
import 'package:flutter_kit/src/features/messages/ui/messages_screen.dart';
import 'package:flutter_kit/src/features/profile/ui/profile_screen.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

@RoutePage()
class MainTabScreen extends ViewStateWidget<MainTabLogic> {
  const MainTabScreen({super.key});

  @override
  MainTabLogic createController() {
    return locator<MainTabLogic>();
  }

  @override
  Widget? buildCustomAppBar(BuildContext context, MainTabLogic logic) {
    return null;
  }

  @override
  Widget buildBody(BuildContext context, MainTabLogic logic) {
    return Scaffold(
      body: IndexedStack(
        index: logic.currentIndex,
        children: const [
          HomeScreenNew(), // 首页
          DiscoverScreen(), // 发现页面
          MessagesScreen(), // 消息页面
          ProfileScreen(), // 我的页面
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(context, logic),
    );
  }

  Widget _buildBottomNavigationBar(BuildContext context, MainTabLogic logic) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: logic.currentIndex,
      onTap: logic.switchTab,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.color_999999,
      selectedFontSize: 12.sp,
      unselectedFontSize: 12.sp,
      backgroundColor: AppColors.color_white,
      elevation: 8,
      items: [
        BottomNavigationBarItem(
          icon: Icon(Icons.home_outlined, size: 24.w),
          activeIcon: Icon(Icons.home, size: 24.w),
          label: '首页',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.explore_outlined, size: 24.w),
          activeIcon: Icon(Icons.explore, size: 24.w),
          label: '发现',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.message_outlined, size: 24.w),
          activeIcon: Icon(Icons.message, size: 24.w),
          label: '消息',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person_outline, size: 24.w),
          activeIcon: Icon(Icons.person, size: 24.w),
          label: '我的',
        ),
      ],
    );
  }

  @override
  bool useScaffold() => false; // 不使用外层Scaffold，在buildBody中自己处理
}


