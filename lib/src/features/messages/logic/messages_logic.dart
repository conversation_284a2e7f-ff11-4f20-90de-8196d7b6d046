import 'package:flutter_kit/src/base/logic/view_state_logic.dart';

/// 消息数据模型
class MessageItem {
  final String id;
  final String title;
  final String content;
  final String time;
  final bool isRead;
  final String avatar;

  MessageItem({
    required this.id,
    required this.title,
    required this.content,
    required this.time,
    required this.isRead,
    required this.avatar,
  });
}

/// 消息页面逻辑
class MessagesLogic extends ViewStateLogic {
  List<MessageItem> _messages = [];
  
  List<MessageItem> get messages => _messages;
  
  /// 未读消息数量
  int get unreadCount => _messages.where((msg) => !msg.isRead).length;
  
  @override
  void loadData() {
    
    // 模拟网络请求延迟
    Future.delayed(const Duration(milliseconds: 800), () {
      _messages = [
        MessageItem(
          id: '1',
          title: '系统通知',
          content: '您有新的职位推荐，快来查看吧！',
          time: '2分钟前',
          isRead: false,
          avatar: '🔔',
        ),
        MessageItem(
          id: '2',
          title: '面试邀请',
          content: '恭喜您！某某公司邀请您参加面试',
          time: '1小时前',
          isRead: false,
          avatar: '📧',
        ),
        MessageItem(
          id: '3',
          title: '简历查看',
          content: 'HR已查看您的简历',
          time: '3小时前',
          isRead: true,
          avatar: '👀',
        ),
        MessageItem(
          id: '4',
          title: '职位匹配',
          content: '为您推荐了5个匹配的职位',
          time: '昨天',
          isRead: true,
          avatar: '🎯',
        ),
        MessageItem(
          id: '5',
          title: '活动通知',
          content: '招聘会即将开始，不要错过！',
          time: '2天前',
          isRead: true,
          avatar: '🎉',
        ),
      ];
    });
  }
  
  /// 标记消息为已读
  void markAsRead(String messageId) {
    final index = _messages.indexWhere((msg) => msg.id == messageId);
    if (index != -1 && !_messages[index].isRead) {
      _messages[index] = MessageItem(
        id: _messages[index].id,
        title: _messages[index].title,
        content: _messages[index].content,
        time: _messages[index].time,
        isRead: true,
        avatar: _messages[index].avatar,
      );
      notifyListeners();
    }
  }
  
  /// 标记所有消息为已读
  void markAllAsRead() {
    _messages = _messages.map((msg) => MessageItem(
      id: msg.id,
      title: msg.title,
      content: msg.content,
      time: msg.time,
      isRead: true,
      avatar: msg.avatar,
    )).toList();
    notifyListeners();
  }
  
  /// 刷新数据
  void refreshData() {
    loadData();
  }
}
