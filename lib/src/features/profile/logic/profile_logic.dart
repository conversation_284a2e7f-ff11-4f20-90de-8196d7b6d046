import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/base/core/view_state.dart';
import 'package:flutter_kit/src/datasource/models/user_account_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/profile_repository.dart';
import 'package:flutter_kit/src/features/login/logic/examinee_helper.dart';
import 'package:flutter_kit/src/shared/locator.dart';

/// 菜单项模型
class ProfileMenuItem {
  final String title;
  final IconData icon;

  ProfileMenuItem({
    required this.title,
    required this.icon,
  });
}

/// 我的页面逻辑
class ProfileLogic extends ViewStateLogic {
  final ProfileRepository repository;
  UserAccountEntity? _userAccount;
  List<ProfileMenuItem> _menuItems = [];
  bool _isLoggedIn = false; // 登录状态

  UserAccountEntity? get userAccount => _userAccount;

  List<ProfileMenuItem> get menuItems => _menuItems;

  bool get isLoggedIn => _isLoggedIn;

  // 便捷的状态访问方法
  bool get isLoading => viewState is ViewStateLoading;

  bool get hasError => viewState is ViewStateError;

  bool get hasData => viewState is ViewStateSuccess;

  ProfileLogic({required this.repository}) {
    try {
      // 初始化菜单项，不依赖用户数据
      _initMenuItems();
      // 设置初始状态为成功，避免一直loading
      // 异步检查登录状态并加载数据
      _checkLoginAndLoadData();
    } catch (e) {
      // 🔥 初始化失败也设置为未登录状态
      debugPrint('ProfileLogic初始化失败: $e');
      _handleAnyError(e);
    }
  }

  /// 🔥 统一错误处理方法 - 任何错误都设置为未登录状态
  void _handleAnyError(dynamic error) {
    debugPrint('ProfileLogic发生错误，设置为未登录状态: $error');
    _isLoggedIn = false;
    _userAccount = null;
    notifyListeners();
  }

  /// 检查登录状态并加载数据
  void _checkLoginAndLoadData() async {
    try {
      final examineeHelper = locator<ExamineeHelper>();
      _isLoggedIn = await examineeHelper.isLogin();

      if (_isLoggedIn) {
        loadData();
      } else {
        notifyListeners();
      }
    } catch (e) {
      // 🔥 任何错误都设置为未登录状态，不显示错误页面
      _handleAnyError(e);
    }
  }

  @override
  void loadData() {
    if (!_isLoggedIn) {
      // 未登录状态，直接设置成功状态
      notifyListeners();
      return;
    }

    // 🔥 重写请求逻辑，确保任何错误都不显示错误页面
    _loadUserDataSafely();
  }

  /// 安全加载用户数据，任何错误都设置为未登录状态
  void _loadUserDataSafely() async {
    try {
      sendRequestSilently(repository.getCurrentUser(), successCallback: (data) {
        // 成功获取用户信息
        _userAccount = data;
        notifyListeners();
      });
    } catch (e) {
      final examineeHelper = locator<ExamineeHelper>();
      await examineeHelper.logout();
      _handleAnyError(e);
    }
  }

  /// 初始化菜单项
  void _initMenuItems() {
    _menuItems = [
      ProfileMenuItem(
        title: '账号管理',
        icon: Icons.person_outline,
      ),
      ProfileMenuItem(
        title: '隐私设置',
        icon: Icons.lock_outline,
      ),
      ProfileMenuItem(
        title: '帮助与反馈',
        icon: Icons.help_outline,
      ),
      ProfileMenuItem(
        title: '关于我们',
        icon: Icons.info_outline,
      ),
    ];
  }

  /// 刷新数据
  void refreshData() {
    try {
      loadData();
    } catch (e) {
      // 🔥 刷新失败也设置为未登录状态
      _handleAnyError(e);
    }
  }

  /// 登录成功后刷新状态
  void onLoginSuccess() {
    try {
      _checkLoginAndLoadData();
    } catch (e) {
      // 🔥 登录后刷新失败也设置为未登录状态
      _handleAnyError(e);
    }
  }

  /// 退出登录
  void logout() async {
    try {
      final examineeHelper = locator<ExamineeHelper>();
      await examineeHelper.logout();

      _isLoggedIn = false;
      _userAccount = null;
      notifyListeners();
    } catch (e) {
      // 🔥 即使退出登录失败，也要设置为未登录状态
      debugPrint('退出登录失败，强制设置为未登录状态: $e');
      _handleAnyError(e);
    }
  }
}
