import 'package:flutter_kit/generated/json/base/json_field.dart';
import 'package:flutter_kit/generated/json/base_info_entity.g.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';
export 'package:flutter_kit/generated/json/base_info_entity.g.dart';

@JsonSerializable()
class BaseInfoEntity {
	@J<PERSON><PERSON>ield(name: "Name")
	late String name;
	@JSONField(name: "GenderCode")
	late String genderCode;
	@JSONField(name: "NationCode")
	late String nationCode;
	@JSONField(name: "MaritalStatusCode")
	late String maritalStatusCode;
	@JSONField(name: "Birthday")
	late String birthday;
	@J<PERSON><PERSON><PERSON>(name: "Stature")
	late int stature;
	@JSO<PERSON>ield(name: "Phone")
	late String phone;
	@JSONField(name: "Email")
	late String email;
	@JSONField(name: "QQ")
	late String qQ;
	@J<PERSON>NField(name: "Address")
	late String address;
	@JSONField(name: "ComputerLevelCode")
	late String computerLevelCode;
	@JSONField(name: "EnglishLevelCode")
	late String englishLevelCode;
	@J<PERSON><PERSON><PERSON>(name: "LiveAreaId")
	late String liveAreaId;
	@J<PERSON><PERSON>ield(name: "LiveAreaName")
	late String liveAreaName;
	@JSONField(name: "LiveAreaCascadeName")
	late String liveAreaCascadeName;
	@JSONField(name: "NativePlaceAreaId")
	late String nativePlaceAreaId;
	@JSONField(name: "NativePlaceAreaName")
	late String nativePlaceAreaName;
	@JSONField(name: "NativePlaceAreaCascadeName")
	late String nativePlaceAreaCascadeName;
	@JSONField(name: "EducationCode")
	late String educationCode;
	@JSONField(name: "WorkingAgeCode")
	late String workingAgeCode;
	@JSONField(name: "GraduateSchool")
	late String graduateSchool;
	@JSONField(name: "MajorIn")
	late String majorIn;
	@JSONField(name: "Labels")
	late String labels;
	@JSONField(name: "Street")
	late String street;
	@JSONField(name: "MapLocation")
	late String mapLocation;
	@JSONField(name: "JobSeekerGroupCode")
	late String jobSeekerGroupCode;

	BaseInfoEntity();

	/// 创建带有默认值的BaseInfoEntity
	factory BaseInfoEntity.empty() {
		final entity = BaseInfoEntity();
		entity.name = '';
		entity.genderCode = '';
		entity.nationCode = '';
		entity.maritalStatusCode = '';
		entity.birthday = '';
		entity.stature = 0;
		entity.phone = '';
		entity.email = '';
		entity.qQ = '';
		entity.address = '';
		entity.computerLevelCode = '';
		entity.englishLevelCode = '';
		entity.liveAreaId = '';
		entity.liveAreaName = '';
		entity.liveAreaCascadeName = '';
		entity.nativePlaceAreaId = '';
		entity.nativePlaceAreaName = '';
		entity.nativePlaceAreaCascadeName = '';
		entity.educationCode = '';
		entity.workingAgeCode = '';
		entity.graduateSchool = '';
		entity.majorIn = '';
		entity.labels = '';
		entity.street = '';
		entity.mapLocation = '';
		entity.jobSeekerGroupCode = '';
		return entity;
	}

	factory BaseInfoEntity.fromJson(Map<String, dynamic> json) => $BaseInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $BaseInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

/// 🔥 BaseInfoEntity 扩展 - 支持类似 entity[fieldName] = value 的语法
extension BaseInfoEntityExtension on BaseInfoEntity {

  /// 🔥 获取字段值 - 类似 entity[fieldName]
  dynamic operator [](String fieldName) {
    switch (fieldName) {
      case 'name': return name;
      case 'genderCode': return genderCode;
      case 'nationCode': return nationCode;
      case 'maritalStatusCode': return maritalStatusCode;
      case 'birthday': return birthday;
      case 'stature': return stature;
      case 'phone': return phone;
      case 'email': return email;
      case 'qQ': return qQ;
      case 'address': return address;
      case 'computerLevelCode': return computerLevelCode;
      case 'englishLevelCode': return englishLevelCode;
      case 'liveAreaId': return liveAreaId;
      case 'liveAreaName': return liveAreaName;
      case 'liveAreaCascadeName': return liveAreaCascadeName;
      case 'nativePlaceAreaId': return nativePlaceAreaId;
      case 'nativePlaceAreaName': return nativePlaceAreaName;
      case 'nativePlaceAreaCascadeName': return nativePlaceAreaCascadeName;
      case 'educationCode': return educationCode;
      case 'workingAgeCode': return workingAgeCode;
      case 'graduateSchool': return graduateSchool;
      case 'majorIn': return majorIn;
      case 'labels': return labels;
      default:
        debugPrint('⚠️ BaseInfoEntity: 未知字段 $fieldName');
        return null;
    }
  }

  /// 🔥 设置字段值 - 类似 entity[fieldName] = value
  void operator []=(String fieldName, dynamic value) {
    try {
      switch (fieldName) {
        case 'name': name = value as String; break;
        case 'genderCode': genderCode = value as String; break;
        case 'nationCode': nationCode = value as String; break;
        case 'maritalStatusCode': maritalStatusCode = value as String; break;
        case 'birthday': birthday = value as String; break;
        case 'stature': stature = value as int; break;
        case 'phone': phone = value as String; break;
        case 'email': email = value as String; break;
        case 'qQ': qQ = value as String; break;
        case 'address': address = value as String; break;
        case 'computerLevelCode': computerLevelCode = value as String; break;
        case 'englishLevelCode': englishLevelCode = value as String; break;
        case 'liveAreaId': liveAreaId = value as String; break;
        case 'liveAreaName': liveAreaName = value as String; break;
        case 'liveAreaCascadeName': liveAreaCascadeName = value as String; break;
        case 'nativePlaceAreaId': nativePlaceAreaId = value as String; break;
        case 'nativePlaceAreaName': nativePlaceAreaName = value as String; break;
        case 'nativePlaceAreaCascadeName': nativePlaceAreaCascadeName = value as String; break;
        case 'educationCode': educationCode = value as String; break;
        case 'workingAgeCode': workingAgeCode = value as String; break;
        case 'graduateSchool': graduateSchool = value as String; break;
        case 'majorIn': majorIn = value as String; break;
        case 'labels': labels = value as String; break;
        default:
          debugPrint('⚠️ BaseInfoEntity: 未知字段 $fieldName');
      }
    } catch (e) {
      debugPrint('⚠️ BaseInfoEntity: 设置字段 $fieldName 失败: $e');
    }
  }

  /// 🔥 批量设置字段值
  void setFields(Map<String, dynamic> fields) {
    for (final entry in fields.entries) {
      this[entry.key] = entry.value;
    }
  }

  /// 🔥 获取所有字段名
  static List<String> get fieldNames => [
    'name', 'genderCode', 'nationCode', 'maritalStatusCode', 'birthday',
    'stature', 'phone', 'email', 'qQ', 'address', 'computerLevelCode',
    'englishLevelCode', 'liveAreaId', 'liveAreaName', 'liveAreaCascadeName',
    'nativePlaceAreaId', 'nativePlaceAreaName', 'nativePlaceAreaCascadeName',
    'educationCode', 'workingAgeCode', 'graduateSchool', 'majorIn', 'labels'
  ];
}