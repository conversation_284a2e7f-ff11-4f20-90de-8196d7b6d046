import 'package:flutter_kit/generated/json/base/json_field.dart';
import 'package:flutter_kit/generated/json/job_record_entity.g.dart';
import 'dart:convert';
export 'package:flutter_kit/generated/json/job_record_entity.g.dart';

@JsonSerializable()
class JobRecordEntity {
	@JSONField(name: "Id")
	late String id = '';
	@JSONField(name: "ApplyTime")
	late String applyTime = '';
	@JSONField(name: "IsDispose")
	late bool isDispose = false;
	@JSONField(name: "DisposeTime")
	late String disposeTime = '';
	@JSONField(name: "IsPass")
	late bool isPass = false;
	@JSONField(name: "DisposeContent")
	late String disposeContent = '';
	@JSONField(name: "ReplyContent")
	late String replyContent = '';
	@JSONField(name: "JobSeekerId")
	late String jobSeekerId = '';
	@JSONField(name: "Job")
	late JobRecordJob job;
	@JSONField(name: "Resume")
	late JobRecordResume resume;
	@JSONField(name: "JobseekerName")
	late String jobseekerName = '';
	@JSONField(name: "ResumeIntegral")
	late int resumeIntegral = 0;
	@JSONField(name: "IsResumeSelectAuth")
	late bool isResumeSelectAuth = false;
	@JSONField(name: "JobApplyDisposeStatus")
	late int jobApplyDisposeStatus = 0;
	@JSONField(name: "IsRead")
	late bool isRead = false;

	JobRecordEntity();

	factory JobRecordEntity.fromJson(Map<String, dynamic> json) => $JobRecordEntityFromJson(json);

	Map<String, dynamic> toJson() => $JobRecordEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class JobRecordJob {
	@JSONField(name: "Id")
	late String id = '';
	@JSONField(name: "EnterpriseId")
	late String enterpriseId = '';
	@JSONField(name: "EnterpriseName")
	late String enterpriseName = '';
	@JSONField(name: "UserAuths")
	late List<JobRecordJobUserAuths> userAuths = [];
	@JSONField(name: "EnterpriseLogoSmall")
	late String enterpriseLogoSmall = '';
	@JSONField(name: "Name")
	late String name = '';
	@JSONField(name: "Nature")
	late String nature = '';
	@JSONField(name: "JobTypeId")
	late String jobTypeId = '';
	@JSONField(name: "JobTypeName")
	late String jobTypeName = '';
	@JSONField(name: "Department")
	late String department = '';
	@JSONField(name: "RecruitingCount")
	late int recruitingCount = 0;
	@JSONField(name: "PayWay")
	late String payWay = '';
	@JSONField(name: "WorkAreaId")
	late String workAreaId = '';
	@JSONField(name: "WorkAreaName")
	late String workAreaName = '';
	@JSONField(name: "WorkAreaCascadeName")
	late String workAreaCascadeName = '';
	@JSONField(name: "Pay")
	late String pay = '';
	@JSONField(name: "JobPayUnit")
	late String jobPayUnit = '';
	@JSONField(name: "Welfare")
	late String welfare = '';
	@JSONField(name: "WelfareValue")
	late String welfareValue = '';
	@JSONField(name: "MapLocation")
	late String mapLocation = '';
	@JSONField(name: "Street")
	late String street = '';
	@JSONField(name: "DemandEducationCode")
	late String demandEducationCode = '';
	@JSONField(name: "WorkAddress")
	late String workAddress = '';
	@JSONField(name: "ReleaseTime")
	late String releaseTime = '';
	@JSONField(name: "IsPutaway")
	late bool isPutaway = false;
	@JSONField(name: "ApplyCount")
	late int applyCount = 0;
	@JSONField(name: "SelectCount")
	late int selectCount = 0;
	@JSONField(name: "ReleaseChannel")
	late String releaseChannel = '';

	JobRecordJob();

	factory JobRecordJob.fromJson(Map<String, dynamic> json) => $JobRecordJobFromJson(json);

	Map<String, dynamic> toJson() => $JobRecordJobToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class JobRecordJobUserAuths {
	@JSONField(name: "Type")
	late String type = '';
	@JSONField(name: "AddTime")
	late String addTime = '';

	JobRecordJobUserAuths();

	factory JobRecordJobUserAuths.fromJson(Map<String, dynamic> json) => $JobRecordJobUserAuthsFromJson(json);

	Map<String, dynamic> toJson() => $JobRecordJobUserAuthsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class JobRecordResume {
	@JSONField(name: "Id")
	late String id = '';
	@JSONField(name: "Title")
	late String title = '';
	@JSONField(name: "Realname")
	late String realname = '';
	@JSONField(name: "UserAuths")
	late List<dynamic> userAuths = [];
	@JSONField(name: "GenderCode")
	late String genderCode = '';
	@JSONField(name: "NationCode")
	late String nationCode = '';
	@JSONField(name: "MaritalStatusCode")
	late String maritalStatusCode = '';
	@JSONField(name: "Stature")
	late int stature = 0;
	@JSONField(name: "Address")
	late String address = '';
	@JSONField(name: "ComputerLevelCode")
	late String computerLevelCode = '';
	@JSONField(name: "EnglishLevelCode")
	late String englishLevelCode = '';
	@JSONField(name: "NativePlaceAreaId")
	late String nativePlaceAreaId = '';
	@JSONField(name: "NativePlaceAreaName")
	late String nativePlaceAreaName = '';
	@JSONField(name: "NativePlaceAreaCascadeName")
	late String nativePlaceAreaCascadeName = '';
	@JSONField(name: "GraduateSchool")
	late String graduateSchool = '';
	@JSONField(name: "MajorIn")
	late String majorIn = '';
	@JSONField(name: "HeadImage")
	late String headImage = '';
	@JSONField(name: "Idcard")
	late String idcard = '';
	@JSONField(name: "Gender")
	late String gender = '';
	@JSONField(name: "Nation")
	late String nation = '';
	@JSONField(name: "QQ")
	late String qQ = '';
	@JSONField(name: "Birthday")
	late String birthday = '';
	@JSONField(name: "LiveAreaId")
	late String liveAreaId = '';
	@JSONField(name: "LiveAreaName")
	late String liveAreaName = '';
	@JSONField(name: "LiveAreaCascadeName")
	late String liveAreaCascadeName = '';
	@JSONField(name: "IntentionAreaIds")
	late String intentionAreaIds = '';
	@JSONField(name: "IntentionAreaNames")
	late String intentionAreaNames = '';
	@JSONField(name: "IntentionJobTypeId")
	late String intentionJobTypeId = '';
	@JSONField(name: "IntentionJobType")
	late String intentionJobType = '';
	@JSONField(name: "IntentionJobTypeIds")
	late String intentionJobTypeIds = '';
	@JSONField(name: "IntentionJobTypeNames")
	late String intentionJobTypeNames = '';
	@JSONField(name: "WorkStatusCode")
	late String workStatusCode = '';
	@JSONField(name: "WorkingAgeCode")
	late String workingAgeCode = '';
	@JSONField(name: "IntentionPayCode")
	late String intentionPayCode = '';
	@JSONField(name: "IntentionPayValue")
	late String intentionPayValue = '';
	@JSONField(name: "IntentionPayWayCode")
	late String intentionPayWayCode = '';
	@JSONField(name: "EducationCode")
	late String educationCode = '';
	@JSONField(name: "WorkExperience")
	late String workExperience = '';
	@JSONField(name: "EduExperience")
	late String eduExperience = '';
	@JSONField(name: "PersonalProfile")
	late String personalProfile = '';
	@JSONField(name: "Proportion")
	late double proportion;
	@JSONField(name: "Certificate")
	late String certificate = '';
	@JSONField(name: "Skill")
	late String skill = '';
	@JSONField(name: "IsDefault")
	late bool isDefault = false;
	@JSONField(name: "Integral")
	late int integral = 0;
	@JSONField(name: "Phone")
	late String phone = '';
	@JSONField(name: "Email")
	late String email = '';
	@JSONField(name: "UserId")
	late String userId = '';
	@JSONField(name: "ReleaseTime")
	late String releaseTime = '';
	@JSONField(name: "Labels")
	late String labels = '';
	@JSONField(name: "MapLocation")
	late String mapLocation = '';
	@JSONField(name: "Street")
	late String street = '';
	@JSONField(name: "JobSeekerGroupCode")
	late String jobSeekerGroupCode = '';
	@JSONField(name: "SelectCount")
	late int selectCount = 0;

	JobRecordResume();

	factory JobRecordResume.fromJson(Map<String, dynamic> json) => $JobRecordResumeFromJson(json);

	Map<String, dynamic> toJson() => $JobRecordResumeToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}