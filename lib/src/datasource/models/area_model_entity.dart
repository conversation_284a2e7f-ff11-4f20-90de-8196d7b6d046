import 'package:flutter_kit/generated/json/base/json_field.dart';
import 'package:flutter_kit/generated/json/area_model_entity.g.dart';
import 'dart:convert';
export 'package:flutter_kit/generated/json/area_model_entity.g.dart';

@JsonSerializable()
class AreaModelEntity {
	@JSONField(name: "Id")
	late String id;
	@JSONField(name: "Name")
	late String name;
	@JSONField(name: "Parent")
	late String parent;

	AreaModelEntity() {
		// 初始化默认值，避免 LateInitializationError
		id = '';
		name = '';
		parent = '';
	}

	factory AreaModelEntity.fromJson(Map<String, dynamic> json) => $AreaModelEntityFromJson(json);

	Map<String, dynamic> toJson() => $AreaModelEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}