import 'package:flutter_kit/generated/json/base/json_field.dart';
import 'package:flutter_kit/generated/json/collect_enterprise_entity.g.dart';
import 'dart:convert';
export 'package:flutter_kit/generated/json/collect_enterprise_entity.g.dart';

@JsonSerializable()
class CollectEnterpriseEntity {
	@JSONField(name: "EnterpriseCollectId")
	late String enterpriseCollectId = '';
	@JSONField(name: "CollectTime")
	late String collectTime = '';
	@JSONField(name: "EnterpriseId")
	late String enterpriseId = '';
	@JSONField(name: "EnterpriseName")
	late String enterpriseName = '';

	CollectEnterpriseEntity();

	factory CollectEnterpriseEntity.fromJson(Map<String, dynamic> json) => $CollectEnterpriseEntityFromJson(json);

	Map<String, dynamic> toJson() => $CollectEnterpriseEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}