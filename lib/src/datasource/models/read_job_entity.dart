import 'package:flutter_kit/generated/json/base/json_field.dart';
import 'package:flutter_kit/generated/json/read_job_entity.g.dart';
import 'dart:convert';
export 'package:flutter_kit/generated/json/read_job_entity.g.dart';

@JsonSerializable()
class ReadJobEntity {
	@JSONField(name: "Id")
	late String id = '';
	@JSONField(name: "Name")
	late String name = '';
	@JSONField(name: "Image")
	late String image = '';
	@JSONField(name: "EnterpriseName")
	late String enterpriseName = '';
	@JSONField(name: "PayWay")
	late String payWay = '';
	@JSONField(name: "Pay")
	late String pay = '';
	@JSONField(name: "Time")
	late String time = '';

	ReadJobEntity();

	factory ReadJobEntity.fromJson(Map<String, dynamic> json) => $ReadJobEntityFromJson(json);

	Map<String, dynamic> toJson() => $ReadJobEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}