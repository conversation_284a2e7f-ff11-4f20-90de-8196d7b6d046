import 'package:flutter_kit/generated/json/base/json_field.dart';
import 'package:flutter_kit/generated/json/user_account_entity.g.dart';
import 'dart:convert';
export 'package:flutter_kit/generated/json/user_account_entity.g.dart';

@JsonSerializable()
class UserAccountEntity {
	@JSONField(name: "Id")
	late String id;
	@JSONField(name: "Rold")
	late String rold;
	@JSONField(name: "NickName")
	late String nickName;
	@JSONField(name: "HeadPic")
	late String headPic;
	@JSONField(name: "Phone")
	late String phone;
	@JSONField(name: "RegisterTime")
	late String registerTime;
	@JSONField(name: "MailCount")
	late int mailCount;
	@JSONField(name: "MailNotReadCount")
	late int mailNotReadCount;
	@JSONField(name: "InformCount")
	late int informCount;
	@JSONField(name: "Proportion")
	late double proportion;
	@JSONField(name: "IsHasResumeBaseInfo")
	late bool isHasResumeBaseInfo;
	@JSONField(name: "IsHasResume")
	late bool isHasResume;
	@JSONField(name: "JobCollectsCount")
	late int jobCollectsCount;
	@JSONField(name: "EnterpriseCollectsCount")
	late int enterpriseCollectsCount;
	@JSONField(name: "JobApplysCount")
	late int jobApplysCount;
	@JSONField(name: "CollectJobIds")
	late List<dynamic> collectJobIds;
	@JSONField(name: "JobApplyIds")
	late List<dynamic> jobApplyIds;
	@JSONField(name: "RealName")
	late String realName;

	UserAccountEntity();

	factory UserAccountEntity.fromJson(Map<String, dynamic> json) => $UserAccountEntityFromJson(json);

	Map<String, dynamic> toJson() => $UserAccountEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}