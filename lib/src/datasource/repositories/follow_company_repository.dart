
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/repositories/base_repository.dart';

import '../../shared/locator.dart';
import '../http/api_service.dart';
import '../http/dio_config.dart';
import '../models/collect_enterprise_entity.dart';

class FollowCompanyRepository extends BaseRepository{

  final ApiService apiService;
  FollowCompanyRepository({
    ApiService? apiService,
  }) : apiService = apiService ?? ApiService(dio: locator<DioConfig>().dio);

  Future<UnifiedResponse<List<CollectEnterpriseEntity>>> getCollectEnterpriseList() async{
    return runApiCall(
      call: () async {
        return await apiService.getCollectEnterpriseList();
      },
    );

  }
}