import 'package:flutter/foundation.dart';
import 'package:flutter_kit/src/datasource/http/api_service.dart';
import 'package:flutter_kit/src/datasource/http/dio_config.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/models/area_model_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/base_repository.dart';
import 'package:flutter_kit/src/features/location/services/location_cache_service.dart';

import '../../shared/locator.dart';

/// 地区数据仓库
///
/// 功能特性：
/// - ✅ 缓存优先策略：优先使用本地缓存，无缓存时调用接口
/// - ✅ 自动缓存机制：接口调用成功后自动缓存数据
/// - ✅ 错误处理：网络异常时尝试使用缓存数据
/// - ✅ 符合项目标准架构
class LocationRepository extends BaseRepository {
  final ApiService apiService;
  final LocationCacheService _cacheService;

  LocationRepository({
    ApiService? apiService,
    LocationCacheService? cacheService,
  }) : apiService = apiService ?? ApiService(dio: locator<DioConfig>().dio),
       _cacheService = cacheService ?? LocationCacheService();

  /// 获取省市区的地区数据
  ///
  /// 实现缓存优先策略：
  /// 1. 优先检查本地缓存
  /// 2. 缓存有效则直接返回
  /// 3. 缓存无效则调用接口
  /// 4. 接口成功后更新缓存
  /// 5. 接口失败时尝试使用过期缓存
  Future<UnifiedResponse<List<AreaModelEntity>>> getAreaData() async {
    try {
      // 1. 优先尝试获取缓存数据
      final cachedData = await _cacheService.getCachedAreaData();
      if (cachedData != null && cachedData.isNotEmpty) {
        debugPrint('✅ LocationRepository: 使用缓存数据，共${cachedData.length}条记录');
        return UnifiedResponseSuccess(cachedData, message: '获取成功');
      }

      // 2. 缓存无效，调用接口获取数据
      debugPrint('🔄 LocationRepository: 缓存无效，调用接口获取数据');
      return await runApiCall(call: () async {
        final response = await apiService.getAreaModel(0);

        // 3. 接口调用成功，缓存数据
        if (response.isSuccess && response.data != null && response.data!.isNotEmpty) {
          final cacheSuccess = await _cacheService.cacheAreaData(response.data!);
          if (cacheSuccess) {
            debugPrint('✅ LocationRepository: 数据缓存成功');
          } else {
            debugPrint('⚠️ LocationRepository: 数据缓存失败');
          }
        }

        return response;
      });
    } catch (e) {
      debugPrint('❌ LocationRepository: 获取数据异常 - $e');

      // 4. 发生异常时，尝试使用任何可用的缓存数据（即使过期）
      try {
        final emergencyCachedData = await _getEmergencyCachedData();
        if (emergencyCachedData != null && emergencyCachedData.isNotEmpty) {
          debugPrint('🆘 LocationRepository: 使用应急缓存数据');
          return UnifiedResponseSuccess(emergencyCachedData, message: '使用缓存数据');
        }
      } catch (cacheError) {
        debugPrint('❌ LocationRepository: 应急缓存也失败 - $cacheError');
      }

      // 5. 所有方式都失败，返回错误
      return UnifiedResponseFailure('获取地区数据失败：$e');
    }
  }

  /// 获取应急缓存数据（忽略有效期检查）
  ///
  /// 在网络异常时使用，即使缓存过期也尝试使用
  Future<List<AreaModelEntity>?> _getEmergencyCachedData() async {
    try {
      // 直接读取缓存数据，不检查有效期
      final storage = locator<LocationCacheService>();
      return await storage.getCachedAreaData();
    } catch (e) {
      debugPrint('❌ LocationRepository: 读取应急缓存失败 - $e');
      return null;
    }
  }

  /// 强制刷新数据
  ///
  /// 清除缓存并重新获取数据
  Future<UnifiedResponse<List<AreaModelEntity>>> forceRefresh() async {
    try {
      // 清除缓存
      await _cacheService.clearCache();
      debugPrint('🔄 LocationRepository: 缓存已清除，强制刷新数据');

      // 重新获取数据
      return await getAreaData();
    } catch (e) {
      debugPrint('❌ LocationRepository: 强制刷新失败 - $e');
      return UnifiedResponseFailure('强制刷新失败：$e');
    }
  }

  /// 获取缓存信息
  ///
  /// 用于调试和状态检查
  Future<Map<String, dynamic>> getCacheInfo() async {
    return await _cacheService.getCacheInfo();
  }

  /// 清除缓存
  ///
  /// 手动清除所有缓存数据
  Future<bool> clearCache() async {
    return await _cacheService.clearCache();
  }
}
