import 'package:flutter_kit/src/datasource/http/api_service.dart';
import 'package:flutter_kit/src/datasource/http/dio_config.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/base_repository.dart';
import 'package:flutter_kit/src/shared/locator.dart';

class CollectionJobRepository extends BaseRepository {
  final ApiService apiService;

  CollectionJobRepository({ApiService? apiService})
      : apiService = apiService ?? ApiService(dio: locator<DioConfig>().dio);

  Future<UnifiedResponse<List<JobInfoEntity>>> getCollectionJobList() async {
    return runApiCall(call: () async {
      return await apiService.getCollectJobList();
    });
  }
}
