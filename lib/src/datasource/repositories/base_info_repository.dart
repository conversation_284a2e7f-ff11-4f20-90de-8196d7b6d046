import 'package:flutter_kit/src/datasource/http/api_service.dart';
import 'package:flutter_kit/src/datasource/http/dio_config.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/models/base_info_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/base_repository.dart';

import '../../shared/locator.dart';

class BaseInfoRepository extends BaseRepository {
  final ApiService apiService;

  BaseInfoRepository({
    ApiService? apiService,
  }) : apiService = apiService ?? ApiService(dio: locator<DioConfig>().dio);

  Future<UnifiedResponse<BaseInfoEntity>> getBaseInfo() async{
    return runApiCall(call: () async{
      return await apiService.GetBaseInfo();
    });
  }
}
