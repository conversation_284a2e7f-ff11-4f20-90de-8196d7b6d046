import 'package:flutter_kit/src/datasource/http/api_service.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/models/user_account_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/base_repository.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:flutter_kit/src/datasource/http/dio_config.dart';

/// 个人资料相关数据仓库
class ProfileRepository extends BaseRepository {
  final ApiService apiService;

  ProfileRepository({
    ApiService? apiService,
  }) : apiService = apiService ?? ApiService(dio: locator<DioConfig>().dio);

  /// 获取当前用户信息
  Future<UnifiedResponse<UserAccountEntity>> getCurrentUser() async {
    return runApiCall(call: () async {
      return await apiService.GetCurrentUser();
    });
  }
}
