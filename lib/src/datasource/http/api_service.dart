import 'package:dio/dio.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/models/area_model_entity.dart';
import 'package:flutter_kit/src/datasource/models/base_info_entity.dart';
import 'package:flutter_kit/src/datasource/models/collect_enterprise_entity.dart';
import 'package:flutter_kit/src/datasource/models/enterprise_detail_entity.dart';
import 'package:flutter_kit/src/datasource/models/job_detail_entity.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';
import 'package:flutter_kit/src/datasource/models/job_record_entity.dart';
import 'package:flutter_kit/src/datasource/models/read_job_entity.dart';
import 'package:flutter_kit/src/datasource/models/resume_entity.dart';
import 'package:flutter_kit/src/datasource/models/user_account_entity.dart';

class ApiService {
  final Dio dio;

  ApiService({required this.dio});

  Future<String> getExample() async {
    await Future.delayed(const Duration(seconds: 2));
    return 'Example';
  }

  Future<String> getExamplePublic() async {
    await dio.get('/public', options: Options(extra: {'auth': false}));
    return 'Example Public';
  }

  //求职端 - 首页
  Future<UnifiedResponse<List<JobInfoEntity>>> getHomeList(int page,int pageSize) async {
    final response = await dio.get('/Common/Job/GetHomeJobList',queryParameters: {'PageIndex':page,"PageSize":pageSize});
    return UnifiedResponse<List<JobInfoEntity>>.fromJson(response.data);
  }

  ///求职端 - 获取职位详情
  Future<UnifiedResponse<JobDetailEntity>> getJob(String jobId) async {
    final response = await dio.get(
      '/Common/Job/GetJob',
      queryParameters: {'jobId': jobId},
    );
    return UnifiedResponse<JobDetailEntity>.fromJson(response.data);
  }

  ///求职端 - 企业详情
  Future<UnifiedResponse<EnterpriseDetailEntity>> getEnterprise(
      String entId) async {
    final response = await dio.get(
      '/Common/Enterprise/GetEnterprise',
      queryParameters: {'entId': entId},
    );
    return UnifiedResponse<EnterpriseDetailEntity>.fromJson(response.data);
  }

  ///求职端 - 登录
  Future<UnifiedResponse<String>> ExamineeLogin(
      String userName, String password, String ident, String vercode) async {
    final response = await dio.post('/JobSeeker/User/Login', data: {
      'UserName': userName,
      'Password': password,
      'Ident': ident,
      'Vercode': vercode
    });
    return UnifiedResponse<String>.fromJson(response.data);
  }

  ///求职端 - 获取账户信息
  Future<UnifiedResponse<UserAccountEntity>> GetCurrentUser() async {
    final response = await dio.get('/JobSeeker/User/GetCurrentUser');
    return UnifiedResponse<UserAccountEntity>.fromJson(response.data);
  }

  ///求职端 - 获取基本信息
  Future<UnifiedResponse<BaseInfoEntity>> GetBaseInfo() async {
    final response = await dio.get('/JobSeeker/Resume/GetBaseInfo');
    return UnifiedResponse<BaseInfoEntity>.fromJson(response.data);
  }

  ///公共 - 获取图形验证码
  Future<UnifiedResponse<String>> getImage(
      String width, String height, int type) async {
    final response = await dio.get('/Common/Captcha/GetImage',
        queryParameters: {'Width': width, 'Height': height, 'Type': type});
    return UnifiedResponse<String>.fromJson(response.data);
  }

  ///公共 - 获取地区
  Future<UnifiedResponse<List<AreaModelEntity>>> getAreaModel(int level) async {
    final response = await dio
        .get('/Common/Data/GetArea', queryParameters: {'level': level});
    return UnifiedResponse<List<AreaModelEntity>>.fromJson(response.data);
  }

  ///求职端 - 收藏职位列表
  Future<UnifiedResponse<List<JobInfoEntity>>> getCollectJobList() async {
    final response = await dio.get('/JobSeeker/Job/GetCollectJobList');
    return UnifiedResponse<List<JobInfoEntity>>.fromJson(response.data);
  }

  ///求职端 - 收藏企业列表
  Future<UnifiedResponse<List<CollectEnterpriseEntity>>>
      getCollectEnterpriseList() async {
    final response =
        await dio.get('/JobSeeker/Enterprise/GetCollectEnterpriseList');
    return UnifiedResponse<List<CollectEnterpriseEntity>>.fromJson(
        response.data);
  }

  ///求职端 - 申请记录
  Future<UnifiedResponse<List<JobRecordEntity>>> getApplyJobRecord() async {
    final response = await dio.get('/JobSeeker/Job/GetApplyJobRecord');
    return UnifiedResponse<List<JobRecordEntity>>.fromJson(response.data);
  }

  ///求职端 - 我看过的职位
  Future<UnifiedResponse<List<ReadJobEntity>>> getHaveReadJobList() async {
    final response = await dio.get('/JobSeeker/Log/GetHaveReadJobList');
    return UnifiedResponse<List<ReadJobEntity>>.fromJson(response.data);
  }

  ///求职端 - 获取简历列表
  Future<UnifiedResponse<List<ResumeEntity>>> getResumeList() async {
    final response = await dio.get('/JobSeeker/Resume/GetResumeList');
    return UnifiedResponse<List<ResumeEntity>>.fromJson(response.data);
  }

  ///求职端 - 收藏职位
  Future<UnifiedResponse<String>> collectJob(String jobId, int ope) async {
    final response = await dio
        .post('/JobSeeker/Job/CollectJob', data: {'JobId': jobId, 'Ope': ope});
    return UnifiedResponse<String>.fromJson(response.data);
  }

  ///求职端 - 申请岗位
  Future<UnifiedResponse<String>> applyJob(String jobId,String resumeId) async{
    final response = await dio.post('/JobSeeker/Job/ApplyJob',data: {'JobId':jobId,'ResumeId':resumeId});
    return UnifiedResponse<String>.fromJson(response.data);
  }
}
