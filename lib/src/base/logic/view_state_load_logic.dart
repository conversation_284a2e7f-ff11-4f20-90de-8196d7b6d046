import 'package:flutter/foundation.dart';
import 'package:flutter_kit/src/base/core/view_state.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';

/// 视图状态逻辑基类
abstract class ViewStateLoadLogic extends ChangeNotifier {
  ViewState _viewState = ViewState();

  ViewState get viewState => _viewState;

  /// 设置视图状态（供子类使用）
  @protected
  set viewState(ViewState state) {
    _viewState = state;
    notifyListeners();
  }

  /// 便捷的状态设置方法
  @protected
  void setLoading() {
    viewState = ViewStateLoading();
  }

  @protected
  void setSuccess<T>(T data) {
    viewState = ViewStateSuccess(data);
  }

  @protected
  void setError(String message, {int? code}) {
    viewState = ViewStateError(code ?? -1, message);
  }

  @protected
  void setEmpty() {
    viewState = ViewStateEmpty();
  }

  @protected
  void setRefreshing<T>(T? data) {
    viewState = ViewStateRefreshing(data);
  }

  @protected
  void setLoadingMore<T>(T? data) {
    viewState = ViewStateLoadingMore(data);
  }

  /// 子类重写此方法来加载数据
  void loadData() {}

  /// 发送请求的通用方法
  void sendRequest<T>(
      Future<UnifiedResponse<T>> sendRequestBlock, {
        bool bindViewState = true,
        bool showLoadingDialog = false,
        bool needLogin = false,
        bool emptyAsSuccess = false,
        bool Function(UnifiedResponse<T>)? judgeEmptyCallback,
        ValueChanged<T?>? successCallback,
        VoidCallback? emptyCallback,
        VoidCallback? failCallback,
      }) {
    if (bindViewState) {
      _viewState = ViewStateLoading();
      notifyListeners();
    }

    sendRequestBlock.then((result) {
      result.when(
        success: (data, message) {
          if (bindViewState) {
            _viewState = ViewStateSuccess(data);
            notifyListeners();
          }
          if (successCallback != null) {
            successCallback(data);
          }
        },
        failure: (message, code, error) {
          if (bindViewState) {
            _viewState = ViewStateFail(code, message);
            notifyListeners();
          }
          if (failCallback != null) {
            failCallback();
          }
        },
        empty: (message) {
          if (emptyAsSuccess) {
            if (bindViewState) {
              _viewState = ViewStateSuccess(null);
              notifyListeners();
            }
            if (successCallback != null) {
              successCallback(null);
            }
          } else {
            if (bindViewState) {
              _viewState = ViewStateEmpty();
              notifyListeners();
            }
            if (emptyCallback != null) {
              emptyCallback();
            }
          }
        },
      );
    }).catchError((e) {
      if (bindViewState) {
        _viewState = ViewStateError(-1, e.toString());
        notifyListeners();
      }
      if (failCallback != null) {
        failCallback();
      }
      debugPrint('sendRequest catchError: $e');
    });
  }

  /// 🔥 静默请求方法 - 不显示任何错误页面，只记录日志
  /// 适用于不希望影响用户体验的后台请求
  void sendRequestSilently<T>(
      Future<UnifiedResponse<T>> sendRequestBlock, {
        ValueChanged<T?>? successCallback,
        VoidCallback? failCallback,
        VoidCallback? emptyCallback,
        String? logTag,
      }) {
    final tag = logTag ?? 'SilentRequest';

    debugPrint('[$tag] 开始静默请求');

    sendRequestBlock.then((result) {
      result.when(
        success: (data, message) {
          debugPrint('[$tag] 请求成功: $message');
          if (successCallback != null) {
            try {
              successCallback(data);
            } catch (e) {
              debugPrint('[$tag] 成功回调执行失败: $e');
            }
          }
        },
        failure: (message, code, error) {
          debugPrint('[$tag] 请求失败: code=$code, message=$message, error=$error');
          if (failCallback != null) {
            try {
              failCallback();
            } catch (e) {
              debugPrint('[$tag] 失败回调执行失败: $e');
            }
          }
        },
        empty: (message) {
          debugPrint('[$tag] 请求返回空数据: $message');
          if (emptyCallback != null) {
            try {
              emptyCallback();
            } catch (e) {
              debugPrint('[$tag] 空数据回调执行失败: $e');
            }
          }
        },
      );
    }).catchError((e) {
      debugPrint('[$tag] 请求异常: $e');
      if (failCallback != null) {
        try {
          failCallback();
        } catch (callbackError) {
          debugPrint('[$tag] 异常回调执行失败: $callbackError');
        }
      }
    });
  }

  /// 🔥 超级静默请求方法 - 连回调异常都不抛出，完全静默
  /// 适用于完全不希望影响主流程的请求（如埋点、统计等）
  void sendRequestUltraSilently<T>(
      Future<UnifiedResponse<T>> sendRequestBlock, {
        ValueChanged<T?>? successCallback,
        VoidCallback? failCallback,
        VoidCallback? emptyCallback,
        String? logTag,
      }) {
    final tag = logTag ?? 'UltraSilentRequest';

    try {
      debugPrint('[$tag] 开始超级静默请求');

      sendRequestBlock.then((result) {
        try {
          result.when(
            success: (data, message) {
              debugPrint('[$tag] 请求成功: $message');
              try {
                successCallback?.call(data);
              } catch (e) {
                debugPrint('[$tag] 成功回调异常(已忽略): $e');
              }
            },
            failure: (message, code, error) {
              debugPrint('[$tag] 请求失败: code=$code, message=$message');
              try {
                failCallback?.call();
              } catch (e) {
                debugPrint('[$tag] 失败回调异常(已忽略): $e');
              }
            },
            empty: (message) {
              debugPrint('[$tag] 请求返回空数据: $message');
              try {
                emptyCallback?.call();
              } catch (e) {
                debugPrint('[$tag] 空数据回调异常(已忽略): $e');
              }
            },
          );
        } catch (e) {
          debugPrint('[$tag] 结果处理异常(已忽略): $e');
        }
      }).catchError((e) {
        debugPrint('[$tag] 请求异常(已忽略): $e');
        try {
          failCallback?.call();
        } catch (callbackError) {
          debugPrint('[$tag] 异常回调异常(已忽略): $callbackError');
        }
      });
    } catch (e) {
      debugPrint('[$tag] 请求发起异常(已忽略): $e');
    }
  }

  /// 🔥 简单静默请求方法 - 直接支持Future<T>，不需要UnifiedResponse包装
  /// 适用于简单的API调用，只记录日志，不影响UI状态
  void sendSimpleRequestSilently<T>(
      Future<T> future, {
        ValueChanged<T>? successCallback,
        Function(dynamic error)? errorCallback,
        String? logTag,
      }) {
    final tag = logTag ?? 'SimpleRequest';

    try {
      debugPrint('[$tag] 开始简单静默请求');

      future.then((data) {
        debugPrint('[$tag] 请求成功，数据类型: ${T.toString()}');
        try {
          successCallback?.call(data);
        } catch (e) {
          debugPrint('[$tag] 成功回调异常(已忽略): $e');
        }
      }).catchError((error) {
        debugPrint('[$tag] 请求失败(已忽略): $error');
        try {
          errorCallback?.call(error);
        } catch (e) {
          debugPrint('[$tag] 错误回调异常(已忽略): $e');
        }
      });
    } catch (e) {
      debugPrint('[$tag] 请求发起异常(已忽略): $e');
    }
  }

  /// 刷新数据
  void refresh() {
    loadData();
  }

  /// 重试
  void retry() {
    loadData();
  }

  /// 重置状态
  void reset() {
    _viewState = ViewState();
    notifyListeners();
  }
}
