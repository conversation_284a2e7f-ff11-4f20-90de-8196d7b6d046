import 'package:flutter/foundation.dart';
import 'package:flutter_kit/src/base/logic/view_state_load_logic.dart';
import '../base.dart';

/// 🎯 分页逻辑基类
///
/// 功能特性：
/// - ✅ 统一的分页参数管理
/// - ✅ 灵活的分页请求处理
/// - ✅ 支持自定义数据源
/// - ✅ 完善的状态管理
/// - ✅ 高性能优化设计
///
/// 使用方式：
/// 1. 继承ViewStatePagingLogic
/// 2. 实现refreshPaging()和loadMorePaging()方法
/// 3. 可选择使用内置的sendRefreshPagingRequest/sendLoadMorePagingRequest
/// 4. 或者自定义分页逻辑（如CustomListLogic）
abstract class ViewStatePagingLogic extends ViewStateLoadLogic {
  /// 当前页码（从1开始）
  int curPage = 1;

  /// 每页数据量
  int pageSize = 10;

  /// 是否正在刷新
  bool _isRefreshing = false;

  /// 是否正在加载更多
  bool _isLoadingMore = false;

  /// 子类重写此方法来刷新分页数据
  void refreshPaging();

  /// 子类重写此方法来加载更多分页数据
  void loadMorePaging();

  // Getters
  bool get isRefreshing => _isRefreshing;
  bool get isLoadingMore => _isLoadingMore;
  
  /// 🔥 发送刷新分页请求（增强版）
  ///
  /// [sendRequestBlock] 请求函数
  /// [bindViewState] 是否绑定ViewState状态
  /// [resetPage] 是否重置页码（默认true）
  /// [showLoading] 是否显示Loading状态
  /// [successBlock] 成功回调
  /// [emptyCallback] 空数据回调
  /// [failCallback] 失败回调
  Future<void> sendRefreshPagingRequest<T>(
    Future<BaseResult<T>> sendRequestBlock, {
    bool bindViewState = true,
    bool resetPage = true,
    bool showLoading = true,
    ValueChanged<T>? successBlock,
    VoidCallback? emptyCallback,
    VoidCallback? failCallback,
  }) async {
    if (_isRefreshing) return;

    _isRefreshing = true;

    try {
      // 🔥 显示Loading状态
      if (bindViewState && showLoading) {
        setLoading();
      }

      // 🔥 重置页码
      if (resetPage) {
        curPage = 1;
      }

      final result = await sendRequestBlock;

      result.when(
        success: (data) {
          if (bindViewState) {
            setSuccess(data);
          }
          if (successBlock != null) {
            successBlock(data);
          }
        },
        failure: (message) {
          if (bindViewState) {
            setError(message);
          }
          if (failCallback != null) {
            failCallback();
          }
        },
        empty: () {
          if (bindViewState) {
            setEmpty();
          }
          if (emptyCallback != null) {
            emptyCallback();
          }
        },
      );
    } catch (e) {
      if (bindViewState) {
        setError(e.toString());
      }
      if (failCallback != null) {
        failCallback();
      }
      debugPrint('sendRefreshPagingRequest catchError: $e');
    } finally {
      _isRefreshing = false;
      notifyListeners();
    }
  }
  
  /// 🔥 发送加载更多分页请求（增强版）
  ///
  /// [sendRequestBlock] 请求函数
  /// [bindViewState] 是否绑定ViewState状态
  /// [autoIncrementPage] 是否自动增加页码（默认true）
  /// [judgeNoMoreDataBlock] 判断是否还有更多数据的函数
  /// [successBlock] 成功回调
  /// [emptyCallback] 空数据回调
  /// [failCallback] 失败回调
  Future<void> sendLoadMorePagingRequest<T>(
    Future<BaseResult<T>> sendRequestBlock, {
    bool bindViewState = true,
    bool autoIncrementPage = true,
    bool Function(BaseResult<T>)? judgeNoMoreDataBlock,
    ValueChanged<T>? successBlock,
    VoidCallback? emptyCallback,
    VoidCallback? failCallback,
  }) async {
    if (_isLoadingMore) return;

    _isLoadingMore = true;
    final originalPage = curPage;

    try {
      // 🔥 自动增加页码
      if (autoIncrementPage) {
        curPage++;
      }

      final result = await sendRequestBlock;

      result.when(
        success: (data) {
          // 🔥 判断是否还有更多数据
          if (judgeNoMoreDataBlock != null) {
            final hasMoreData = judgeNoMoreDataBlock(result);
            if (!hasMoreData) {
              // 可以在这里设置hasMore状态
            }
          }

          if (successBlock != null) {
            successBlock(data);
          }
        },
        failure: (message) {
          // 🔥 失败时回退页码
          if (autoIncrementPage) {
            curPage = originalPage;
          }

          if (bindViewState) {
            setError(message);
          }
          if (failCallback != null) {
            failCallback();
          }
        },
        empty: () {
          // 🔥 空数据时也可能需要回退页码
          if (autoIncrementPage) {
            curPage = originalPage;
          }

          if (emptyCallback != null) {
            emptyCallback();
          }
        },
      );
    } catch (e) {
      // 🔥 异常时回退页码
      if (autoIncrementPage) {
        curPage = originalPage;
      }

      if (bindViewState) {
        setError(e.toString());
      }
      if (failCallback != null) {
        failCallback();
      }
      debugPrint('sendLoadMorePagingRequest catchError: $e');
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  /// 是否有更多数据（子类可重写）
  bool get hasMore => true;

  /// 🔥 重置分页参数（增强版）
  void resetPaging({
    int? newPageSize,
    bool resetToFirstPage = true,
  }) {
    if (resetToFirstPage) {
      curPage = 1;
    }
    if (newPageSize != null) {
      pageSize = newPageSize;
    }
    _isRefreshing = false;
    _isLoadingMore = false;
    notifyListeners();
  }

  /// 🔥 设置页码
  void setCurrentPage(int page) {
    if (page > 0) {
      curPage = page;
      notifyListeners();
    }
  }

  /// 🔥 设置页面大小
  void setPageSize(int size) {
    if (size > 0) {
      pageSize = size;
      notifyListeners();
    }
  }

  /// 🔥 获取下一页页码
  int get nextPage => curPage + 1;

  /// 🔥 是否是第一页
  bool get isFirstPage => curPage == 1;

  /// 🔥 是否正在进行任何分页操作
  bool get isPagingInProgress => _isRefreshing || _isLoadingMore;

  /// 🔥 获取分页信息（增强版）
  Map<String, dynamic> get pagingInfo => {
    'currentPage': curPage,
    'nextPage': nextPage,
    'pageSize': pageSize,
    'hasMore': hasMore,
    'isFirstPage': isFirstPage,
    'isRefreshing': isRefreshing,
    'isLoadingMore': isLoadingMore,
    'isPagingInProgress': isPagingInProgress,
  };

  /// 🔥 便捷的刷新方法
  Future<void> performRefresh() async {
    if (!_isRefreshing) {
      refreshPaging();
    }
  }

  /// 🔥 便捷的加载更多方法
  Future<void> performLoadMore() async {
    if (!_isLoadingMore && hasMore) {
      loadMorePaging();
    }
  }

  /// 🔥 停止所有分页操作
  void stopPagingOperations() {
    _isRefreshing = false;
    _isLoadingMore = false;
    notifyListeners();
  }

  @override
  void dispose() {
    stopPagingOperations();
    super.dispose();
  }

  @override
  String toString() {
    return 'ViewStatePagingLogic('
        'curPage: $curPage, '
        'pageSize: $pageSize, '
        'hasMore: $hasMore, '
        'isRefreshing: $isRefreshing, '
        'isLoadingMore: $isLoadingMore'
        ')';
  }
}
