import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/base/widgets/state_widget_builder.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

abstract class ViewStateWidget<T extends ViewStateLogic>
    extends StatelessWidget {
  const ViewStateWidget({
    super.key,
    this.autoLoad = true,
  });

  /// 是否自动加载数据
  final bool autoLoad;

  /// 创建Controller实例，子类必须实现
  T createController();

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<T>(
      create: (_) {
        final controller = createController();
        // 🔥 根据必须执行.md规则，ViewStateWidget需要默认调用logic.loadData
        if (autoLoad) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            try {
              controller.loadData();
            } catch (e) {
              debugPrint('ViewStateWidget loadData error: $e');
            }
          });
        }
        return controller;
      },
      child: Consumer<T>(
        builder: (context, logic, child) {
          Widget body = buildBody(context, logic);
          return useScaffold()
              ? Scaffold(
                  backgroundColor:
                      backgroundColor() ?? AppColors.pageBackground,
                  resizeToAvoidBottomInset: resizeToAvoidBottomInset(),
                  body: _buildBodyWithCustomAppBar(context, logic, body),
                )
              : Container(
                  color: backgroundColor() ?? AppColors.pageBackground,
                  child: _buildBodyWithCustomAppBar(context, logic, body),
                );
        },
      ),
    );
  }



  /// 获取状态切换动画时长
  Duration getTransitionDuration() => const Duration(milliseconds: 300);

  /// 获取状态切换动画构建器
  Widget Function(Widget, Animation<double>) getTransitionBuilder() {
    return _buildFadeSlideTransition;
  }

  /// 淡入淡出 + 轻微滑动动画（默认）
  static Widget _buildFadeSlideTransition(
      Widget child, Animation<double> animation) {
    return FadeTransition(
      opacity: animation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0.0, 0.1),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        )),
        child: child,
      ),
    );
  }



  /// 构建自定义AppBar（普通Widget）
  Widget? buildCustomAppBar(BuildContext context, T logic) => null;

  /// 🔥 构建带自定义AppBar的body
  Widget _buildBodyWithCustomAppBar(
      BuildContext context, T logic, Widget body) {
    final customAppBar = buildCustomAppBar(context, logic);

    if (customAppBar != null) {
      return Column(
        children: [
          customAppBar,
          Expanded(child: body),
        ],
      );
    }

    return body;
  }

  /// 构建主体内容
  Widget buildBody(BuildContext context, T logic);

  /// 是否使用Scaffold
  bool useScaffold() => true;

  /// 是否调整避免底部输入法
  bool resizeToAvoidBottomInset() => false;

  /// 背景色
  Color? backgroundColor() => null;
}
