import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_kit/src/shared/utils/safe_area_manager.dart';
import 'package:flutter_kit/src/features/resume/theme/resume_theme.dart';

/// 应用全局背景组件
///
/// 提供统一的渐变背景，使用缓存的安全区域信息优化性能
/// 可以作为页面的底层背景使用
///
/// 键盘优化版本：
/// - 使用RepaintBoundary隔离重绘
/// - 缓存渐变对象避免重复创建
/// - 避免在键盘弹出时重建背景
class AppBackground extends StatelessWidget {
  final Widget child;
  final double? customHeight;
  final bool includeBottomPadding;
  final bool keyboardOptimized;

  const AppBackground({
    super.key,
    required this.child,
    this.customHeight,
    this.includeBottomPadding = false,
    this.keyboardOptimized = true,
  });

  // 缓存渐变对象，避免重复创建
  static final _cachedGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      ResumeTheme.backgroundColor, // 使用统一的页面背景色
      ResumeTheme.backgroundColor,
      ResumeTheme.backgroundColor,
      ResumeTheme.backgroundColor,
    ],
  );

  @override
  Widget build(BuildContext context) {
    final safeAreaManager = SafeAreaManager.instance;

    // 如果未初始化，使用MediaQuery作为fallback
    final topPadding = safeAreaManager.isInitialized
        ? safeAreaManager.topPadding
        : MediaQuery.of(context).padding.top;

    final bottomPadding = includeBottomPadding
        ? (safeAreaManager.isInitialized
            ? safeAreaManager.bottomPadding
            : MediaQuery.of(context).padding.bottom)
        : 0.0;

    if (keyboardOptimized) {
      // 键盘优化版本：使用Container + decoration，避免Stack重建
      return Container(
        decoration: BoxDecoration(gradient: _cachedGradient),
        child: RepaintBoundary(
          child: child,
        ),
      );
    }

    // 原版本：使用Stack布局
    return Stack(
      children: [
        // 渐变背景
        RepaintBoundary(
          child: Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: customHeight ?? (200.h + topPadding + bottomPadding),
              decoration: BoxDecoration(gradient: _cachedGradient),
            ),
          ),
        ),
        // 页面内容
        child,
      ],
    );
  }
}

/// 简化版背景组件，只包含渐变背景
class SimpleAppBackground extends StatelessWidget {
  final double? height;
  final List<Color>? gradientColors;

  const SimpleAppBackground({
    super.key,
    this.height,
    this.gradientColors,
  });

  @override
  Widget build(BuildContext context) {
    final safeAreaManager = SafeAreaManager.instance;
    final topPadding = safeAreaManager.isInitialized 
        ? safeAreaManager.topPadding 
        : MediaQuery.of(context).padding.top;

    return Container(
      height: height ?? (200.h + topPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: gradientColors ?? [
            ResumeTheme.primaryColor.withValues(alpha: 0.08),
            ResumeTheme.primaryColor.withValues(alpha: 0.04),
            ResumeTheme.primaryColor.withValues(alpha: 0.02),
            Colors.transparent,
          ],
        ),
      ),
    );
  }
}

/// 全屏背景组件，覆盖整个屏幕
class FullScreenBackground extends StatelessWidget {
  final Widget child;
  final List<Color>? gradientColors;

  const FullScreenBackground({
    super.key,
    required this.child,
    this.gradientColors,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: gradientColors ?? [
            ResumeTheme.primaryColor.withValues(alpha: 0.08),
            ResumeTheme.primaryColor.withValues(alpha: 0.04),
            ResumeTheme.primaryColor.withValues(alpha: 0.02),
            Colors.transparent,
          ],
        ),
      ),
      child: child,
    );
  }
}
