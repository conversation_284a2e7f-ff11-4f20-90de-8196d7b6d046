import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/theme/app_color.dart';
import '../../shared/extensions/asset_ext.dart';
import '../l10n/base_localizations.dart';

/// 状态Widget构建器
/// 提供统一的加载、错误、空状态视图
class StateWidgetBuilder {
  StateWidgetBuilder._();
  
  /// 加载中Widget
  static Widget loading({
    BuildContext? context,
    String? message,
    Color? color,
    double? size,
    double? strokeWidth,
  }) {
    return Center(
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 16.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: size ?? 24.w,
              height: size ?? 24.w,
              child: CircularProgressIndicator(
                strokeWidth: strokeWidth ?? 4.w,
                color: color ?? AppColors.primary,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              message ?? (context != null ? BaseLocalizations.of(context).loading : '数据加载中...'),
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.color_666666,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 加载更多Widget
  static Widget loadingMore({
    BuildContext? context,
    String? message,
    Color? color,
    double? size,
    double? strokeWidth,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size ?? 16.w,
            height: size ?? 16.w,
            child: CircularProgressIndicator(
              strokeWidth: strokeWidth ?? 2.w,
              color: color ?? AppColors.color_f02e4b,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            message ?? (context != null ? BaseLocalizations.of(context).loadingMore : 'Loading more...'),
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.color_666666,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 错误Widget
  static Widget error({
    BuildContext? context,
    String? message,
    VoidCallback? onRetry,
    String? retryText,
    IconData? icon,
    Color? iconColor,
    String? imagePath,
    double? imageSize,
  }) {
    return Center(
      child: Container(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 图标或图片
            if (imagePath != null)
              Image.asset(
                imagePath,
                width: imageSize ?? 120.w,
                height: imageSize ?? 120.w,
                color: iconColor ?? AppColors.color_f02e4b,
              )
            else
              Icon(
                icon ?? Icons.error_outline,
                size: imageSize ?? 64.w,
                color: iconColor ?? AppColors.color_f02e4b,
              ),
            
            SizedBox(height: 16.h),
            
            // 错误消息
            Text(
              message ?? (context != null ? BaseLocalizations.of(context).somethingWentWrong : 'Something went wrong'),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.color_666666,
              ),
            ),
            
            // 重试按钮
            if (onRetry != null) ...[
              SizedBox(height: 16.h),
              ElevatedButton(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.color_f02e4b,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                ),
                child: Text(
                  retryText ?? (context != null ? BaseLocalizations.of(context).retry : 'Retry'),
                  style: TextStyle(fontSize: 14.sp),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  /// 空状态Widget
  static Widget empty({
    BuildContext? context,
    String? message,
    VoidCallback? onRefresh,
    String? refreshText,
    IconData? icon,
    Color? iconColor,
    String? imagePath,
    double? imageSize,
  }) {
    return Center(
      child: Container(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 图标或图片
            if (imagePath != null)
              Image.asset(
                imagePath,
                width: imageSize ?? 120.w,
                height: imageSize ?? 120.w,
                color: iconColor ?? AppColors.color_f02e4b,
              )
            else
            // 空状态消息
            Text(
              message ?? (context != null ? BaseLocalizations.of(context).noDataAvailable : '暂无数据'),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 15.sp,
                color: AppColors.color_666666,
              ),
            ),
            
            // 刷新按钮
            if (onRefresh != null) ...[
              SizedBox(height: 16.h),
              TextButton(
                onPressed: onRefresh,
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.color_f02e4b,
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                ),
                child: Text(
                  refreshText ?? (context != null ? BaseLocalizations.of(context).refresh : 'Refresh'),
                  style: TextStyle(fontSize: 14.sp),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  /// 没有更多数据Widget
  static Widget noMoreData({
    BuildContext? context,
    String? message,
    Color? textColor,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Center(
        child: Text(
          message ?? (context != null ? BaseLocalizations.of(context).noMoreData : 'No more data'),
          style: TextStyle(
            fontSize: 14.sp,
            color: textColor ?? AppColors.color_666666,
          ),
        ),
      ),
    );
  }
  
  /// 网络错误Widget
  static Widget networkError({
    BuildContext? context,
    String? message,
    VoidCallback? onRetry,
    String? retryText,
  }) {
    return error(
      context: context,
      message: message ?? (context != null ? BaseLocalizations.of(context).networkConnectionFailed : 'Network connection failed'),
      onRetry: onRetry,
      retryText: retryText,
      icon: Icons.wifi_off,
      imagePath: 'ic_view_fail'.webp,
    );
  }
  
  /// 服务器错误Widget
  static Widget serverError({
    BuildContext? context,
    String? message,
    VoidCallback? onRetry,
    String? retryText,
  }) {
    return error(
      context: context,
      message: message ?? (context != null ? BaseLocalizations.of(context).serverErrorOccurred : 'Server error occurred'),
      onRetry: onRetry,
      retryText: retryText,
      icon: Icons.cloud_off,
    );
  }
  
  /// 自定义状态Widget
  static Widget custom({
    required Widget child,
    EdgeInsetsGeometry? padding,
    bool center = true,
  }) {
    Widget content = Container(
      padding: padding ?? EdgeInsets.all(24.w),
      child: child,
    );
    
    return center ? Center(child: content) : content;
  }
}
