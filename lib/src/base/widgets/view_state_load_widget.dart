import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/base/widgets/state_widget_builder.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../logic/view_state_load_logic.dart';

/// 🎯 页面状态自动切换组件
///
/// 功能特性：
/// - ✅ 统一的状态管理（Loading/Success/Error/Empty）
/// - ✅ 自动数据加载和状态切换
/// - ✅ 优雅的动画过渡效果
/// - ✅ 符合项目架构规范
/// - ✅ 高性能优化设计
///
/// 注意：此组件专注于基础状态管理，不包含上拉加载、下拉刷新功能
/// 这些功能应该在CustomListView等专门的列表组件中实现
abstract class ViewStateLoadWidget<T extends ViewStateLoadLogic>
    extends StatelessWidget {
  const ViewStateLoadWidget({
    super.key,
    this.bindViewState = true,
    this.autoLoad = true,
  });

  /// 是否绑定ViewState状态管理
  final bool bindViewState;

  /// 是否自动加载数据
  final bool autoLoad;

  /// 创建Controller实例，子类必须实现
  T createController();

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<T>(
      create: (_) {
        final controller = createController();
        if (autoLoad) {
          // 🔥 延迟执行，避免在build过程中调用，提高性能
          WidgetsBinding.instance.addPostFrameCallback((_) {
            try {
              controller.loadData();
            } catch (e) {
              // 🔥 错误处理：防止初始化时的异常导致应用崩溃
              debugPrint('ViewStateWidget loadData error: $e');
              // 注意：这里不能直接调用setError，因为它是protected方法
              // 错误会在loadData方法内部处理
            }
          });
        }
        return controller;
      },
      child: Consumer<T>(
        builder: (context, logic, child) {
          Widget body;

          if (bindViewState) {
            final viewState = logic.viewState;

            Widget targetBody;

            // 🔥 优化状态判断逻辑，提高性能和可读性
            if (viewState.isSuccess()) {
              targetBody = buildBody(context, logic);
            } else if (viewState.isError()) {
              targetBody = buildCustomErrorWidget() ??
                  StateWidgetBuilder.error(
                    context: context,
                    message: viewState.errorMessage ?? '加载失败',
                    onRetry: () {
                      try {
                        logic.retry();
                      } catch (e) {
                        debugPrint('ViewStateWidget retry error: $e');
                      }
                    },
                  );
            } else if (viewState.isEmpty()) {
              targetBody = buildCustomEmptyWidget() ??
                  StateWidgetBuilder.empty(
                    context: context,
                    onRefresh: () {
                      try {
                        logic.refresh();
                      } catch (e) {
                        debugPrint('ViewStateWidget refresh error: $e');
                      }
                    },
                  );
            } else if (viewState.isFail()) {
              targetBody = buildCustomFailWidget() ??
                  StateWidgetBuilder.error(
                    context: context,
                    message: viewState.errorMessage ?? '请求失败',
                    onRetry: () {
                      try {
                        logic.retry();
                      } catch (e) {
                        debugPrint('ViewStateWidget retry error: $e');
                      }
                    },
                  );
            } else if (viewState.isLoading()) {
              // 🔥 优化Loading显示逻辑
              if (hasData()) {
                // 有数据时，显示内容 + 顶部 loading（适用于刷新场景）
                targetBody = Stack(
                  children: [
                    buildBody(context, logic),
                    // buildTopLoadingWidget(),
                  ],
                );
              } else {
                // 🔥 无数据时，显示顶部loading（适用于首次加载）
                targetBody = buildCustomLoadingWidget() ??
                    _buildTopLoading(context);
              }
            } else {
              // 🔥 默认状态处理
              targetBody = buildCustomLoadingWidget() ??
                  _buildTopLoading(context);
            }

            // 添加动画过渡
            body = AnimatedSwitcher(
              duration: getTransitionDuration(),
              transitionBuilder: getTransitionBuilder(),
              child: Container(
                key: ValueKey(viewState.runtimeType),
                color: AppColors.pageBackground, // 确保容器背景透明
                child: targetBody,
              ),
            );
          } else {
            body = buildBody(context, logic);
          }

          return useScaffold()
              ? Scaffold(
            backgroundColor: backgroundColor() ?? AppColors.pageBackground,
            resizeToAvoidBottomInset: resizeToAvoidBottomInset(),
            body: _buildBodyWithCustomAppBar(context, logic, body),
          )
              : Container(
            color: backgroundColor() ?? AppColors.pageBackground,
            child: _buildBodyWithCustomAppBar(context, logic, body),
          );
        },
      ),
    );
  }

  /// 构建自定义加载Widget
  Widget? buildCustomLoadingWidget() => null;

  /// 构建自定义失败Widget
  Widget? buildCustomFailWidget() => null;

  /// 构建自定义空数据Widget
  Widget? buildCustomEmptyWidget() => null;

  /// 构建自定义错误Widget
  Widget? buildCustomErrorWidget() => null;

  /// 检查是否有数据（子类重写）
  bool hasData() => false;

  /// 获取状态切换动画时长
  Duration getTransitionDuration() => const Duration(milliseconds: 300);

  /// 获取状态切换动画构建器
  Widget Function(Widget, Animation<double>) getTransitionBuilder() {
    return _buildFadeSlideTransition;
  }

  /// 淡入淡出 + 轻微滑动动画（默认）
  static Widget _buildFadeSlideTransition(Widget child,
      Animation<double> animation) {
    return FadeTransition(
      opacity: animation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0.0, 0.1),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        )),
        child: child,
      ),
    );
  }

  /// 纯淡入淡出动画
  static Widget _buildFadeTransition(Widget child,
      Animation<double> animation) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }

  /// 缩放动画
  static Widget _buildScaleTransition(Widget child,
      Animation<double> animation) {
    return ScaleTransition(
      scale: Tween<double>(
        begin: 0.8,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Curves.easeOutBack,
      )),
      child: FadeTransition(
        opacity: animation,
        child: child,
      ),
    );
  }

  /// 构建顶部加载Widget（用于有数据时的刷新状态）
  Widget buildTopLoadingWidget() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: SizedBox(
        height: 3,
        child: LinearProgressIndicator(
          backgroundColor: AppColors.pageBackground,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      ),
    );
  }



  /// 构建自定义AppBar（普通Widget）
  Widget? buildCustomAppBar(BuildContext context, T logic) => null;

  /// 🔥 构建带自定义AppBar的body
  Widget _buildBodyWithCustomAppBar(BuildContext context, T logic,
      Widget body) {
    final customAppBar = buildCustomAppBar(context, logic);

    if (customAppBar != null) {
      return Column(
        children: [
          customAppBar,
          Expanded(child: body),
        ],
      );
    }

    return body;
  }


  /// 🔥 构建顶部Loading（显示在页面顶部）
  Widget _buildTopLoading(BuildContext context) {
    return Container(
      color: backgroundColor() ?? AppColors.pageBackground,
      child: Column(
        children: [
          // 顶部Loading区域
          Container(
            padding: const EdgeInsets.only(top: 10.0, bottom: 20.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '加载中',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          // 剩余空间
          Expanded(child: Container()),
        ],
      ),
    );
  }

  /// 构建主体内容
  Widget buildBody(BuildContext context, T logic);

  /// 是否使用Scaffold
  bool useScaffold() => true;

  /// 是否调整避免底部输入法
  bool resizeToAvoidBottomInset() => false;

  /// 背景色
  Color? backgroundColor() => null;
}
