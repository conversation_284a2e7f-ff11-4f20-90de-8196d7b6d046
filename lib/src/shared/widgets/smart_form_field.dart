import 'package:flutter/material.dart';
import 'package:flutter_kit/src/features/resume/ui/widget/resume_pickers.dart';
import 'package:flutter_kit/src/features/resume/ui/widget/selectable_item2.dart';
import 'package:flutter_kit/src/shared/mixins/form_validation_mixin.dart';
import 'package:flutter_kit/src/shared/utils/validation_utils.dart';
import 'package:flutter_kit/src/shared/utils/datetime_utils.dart';
import 'package:flutter_kit/src/shared/widgets/smart_date_picker.dart';

import '../../features/resume/ui/widget/selectable_item.dart';

/// 🔥 智能表单字段组件 - 自动处理验证和错误显示
///
/// 参考优秀GitHub项目的设计理念：
/// - 零配置：自动推断验证类型
/// - 高度封装：一个组件搞定所有表单需求
/// - 类型安全：强类型约束
/// - 易于扩展：支持自定义配置
class SmartFormField extends StatelessWidget {
  final String fieldName;
  final String label;
  final dynamic value;
  final String? hint;
  final bool isRequired;
  final FormValidationMixin logic;
  final Function(dynamic) onChanged;

  // 字段类型配置
  final SmartFieldType fieldType;
  final Map<String, dynamic>? fieldConfig;

  const SmartFormField({
    super.key,
    required this.fieldName,
    required this.label,
    required this.value,
    required this.logic,
    required this.onChanged,
    this.hint,
    this.isRequired = false,
    this.fieldType = SmartFieldType.text,
    this.fieldConfig,
  });

  @override
  Widget build(BuildContext context) {
    switch (fieldType) {
      case SmartFieldType.text:
        return _buildTextInput();
      case SmartFieldType.phone:
        return _buildPhoneInput();
      case SmartFieldType.email:
        return _buildEmailInput();
      case SmartFieldType.name:
        return _buildNameInput();
      case SmartFieldType.xmlPicker:
        return _buildXmlPicker(context);
      case SmartFieldType.multiXmlPicker:
        return _buildMultiXmlPicker(context);
      case SmartFieldType.datePicker:
        return _buildDatePicker(context);
      default:
        return _buildTextInput();
    }
  }

  /// 🔥 构建文本输入
  Widget _buildTextInput() {
    return SelectableItem2(
      label: label,
      title: label,
      value: value?.toString(),
      hint: hint ?? '请输入$label',
      maxLength: fieldConfig?['maxLength'] ?? 100,
      isRequired: isRequired,
      inputType: fieldConfig?['inputType'] ?? InputType.textField,
      errorMessage: logic.getFieldError(fieldName),
      fieldName: fieldName,
      onValidate: logic.setFieldError,
      onSave: onChanged,
    );
  }

  /// 🔥 构建手机号输入
  Widget _buildPhoneInput() {
    return SelectableItem2(
      label: label,
      title: label,
      value: value?.toString(),
      hint: hint ?? '请输入$label',
      maxLength: 11,
      isRequired: isRequired,
      inputType: InputType.textField,
      validationType: ValidationType.phone,
      errorMessage: logic.getFieldError(fieldName),
      fieldName: fieldName,
      onValidate: logic.setFieldError,
      onSave: onChanged,
    );
  }

  /// 🔥 构建邮箱输入
  Widget _buildEmailInput() {
    return SelectableItem2(
      label: label,
      title: label,
      value: value?.toString(),
      hint: hint ?? '请输入$label',
      maxLength: 50,
      isRequired: isRequired,
      inputType: InputType.textField,
      validationType: ValidationType.email,
      errorMessage: logic.getFieldError(fieldName),
      fieldName: fieldName,
      onValidate: logic.setFieldError,
      onSave: onChanged,
    );
  }

  /// 🔥 构建姓名输入
  Widget _buildNameInput() {
    return SelectableItem2(
      label: label,
      title: label,
      value: value?.toString(),
      hint: hint ?? '请输入$label',
      maxLength: 20,
      isRequired: isRequired,
      inputType: InputType.textField,
      validationType: ValidationType.chineseName,
      errorMessage: logic.getFieldError(fieldName),
      fieldName: fieldName,
      onValidate: logic.setFieldError,
      onSave: onChanged,
    );
  }

  /// 🔥 构建XML选择器
  Widget _buildXmlPicker(BuildContext context) {
    final groundName = fieldConfig?['groundName'] as String?;
    if (groundName == null) {
      throw ArgumentError('xmlPicker requires groundName in fieldConfig');
    }

    return ResumePickers.xmlPicker(
      context: context,
      currentValue: value?.toString(),
      groundName: groundName,
      isRequired: isRequired,
      label: label,
      title: fieldConfig?['title'] ?? '请选择$label',
      errorMessage: logic.getFieldError(fieldName),
      fieldName: fieldName,
      onValidate: logic.setFieldError,
      onChanged: onChanged,
    );
  }

  /// 🔥 构建多选XML选择器
  Widget _buildMultiXmlPicker(BuildContext context) {
    final groundName = fieldConfig?['groundName'] as String?;
    if (groundName == null) {
      throw ArgumentError('multiXmlPicker requires groundName in fieldConfig');
    }

    return ResumePickers.multiXmlPicker(
      context: context,
      currentValues: value as List<String>?,
      groundName: groundName,
      isRequired: isRequired,
      label: label,
      title: fieldConfig?['title'] ?? '请选择$label',
      maxSelection: fieldConfig?['maxSelection'],
      onChanged: onChanged,
    );
  }

  /// 🔥 构建日期选择器
  Widget _buildDatePicker(BuildContext context) {
    final config = fieldConfig?['datePickerConfig'] as DatePickerConfig? ??
        DatePickerConfig.workingAgeBirthday();

    // 解析当前值
    DateTime? currentDate;
    String displayValue = '';

    if (value != null && value.toString().isNotEmpty) {
      currentDate = DateTimeUtils.parseDate(value.toString());
      if (currentDate != null) {
        displayValue = config.mode == DatePickeMode.yearMonth
            ? DateTimeUtils.formatToYearMonth(currentDate)
            : DateTimeUtils.formatToYearMonthDay(currentDate);
      }
    }

    return SelectableDataItem(
      label: label,
      value: displayValue.isEmpty ? null : displayValue,
      hint: hint ?? '请选择$label',
      isRequired: isRequired,
      config: config,
      currDate: currentDate,
      errorMessage: logic.getFieldError(fieldName),
      onTap: () async {
        // 🔥 显示日期选择器
        final selectedDate = await SmartDatePicker.show(
          context: context,
          initialDate: currentDate,
          mode: config.mode,
          minAge: config.minAge,
          maxAge: config.maxAge,
          title: config.title,
        );

        if (selectedDate != null) {
          // 格式化并返回选中的日期
          final formattedDate = config.mode == DatePickeMode.yearMonth
              ? DateTimeUtils.formatToYearMonth(selectedDate)
              : DateTimeUtils.formatToYearMonthDay(selectedDate);

          onChanged(formattedDate);

          // 🔥 验证日期
          final validationResult =
              DateTimeUtils.validateBirthDate(formattedDate);
          logic.setFieldError(
            validationResult.isValid ? null : validationResult.errorMessage,
            fieldName,
          );
        }
      },
    );
  }
}

/// 🔥 智能字段类型枚举
enum SmartFieldType {
  text, // 普通文本
  phone, // 手机号
  email, // 邮箱
  name, // 姓名
  xmlPicker, // XML单选
  multiXmlPicker, // XML多选
  datePicker, // 日期选择器
}

/// 🔥 智能表单构建器 - 批量创建表单字段
class SmartFormBuilder {
  /// 🔥 根据字段配置批量创建表单字段
  static List<Widget> buildFields({
    required List<SmartFieldConfig> configs,
    required Map<String, dynamic> values,
    required FormValidationMixin logic,
    required Function(String, dynamic) onFieldChanged,
    Widget Function()? dividerBuilder,
  }) {
    final widgets = <Widget>[];

    for (int i = 0; i < configs.length; i++) {
      final config = configs[i];

      // 添加字段
      widgets.add(
        SmartFormField(
          fieldName: config.fieldName,
          label: config.label,
          value: values[config.fieldName],
          hint: config.hint,
          isRequired: config.isRequired,
          fieldType: config.fieldType,
          fieldConfig: config.fieldConfig,
          logic: logic,
          onChanged: (value) => onFieldChanged(config.fieldName, value),
        ),
      );

      // 添加分割线（除了最后一个）
      if (i < configs.length - 1 && dividerBuilder != null) {
        widgets.add(dividerBuilder());
      }
    }

    return widgets;
  }
}

/// 🔥 智能字段配置类
class SmartFieldConfig {
  final String fieldName;
  final String label;
  final String? hint;
  final bool isRequired;
  final SmartFieldType fieldType;
  final Map<String, dynamic>? fieldConfig;

  const SmartFieldConfig({
    required this.fieldName,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.fieldType = SmartFieldType.text,
    this.fieldConfig,
  });

  /// 🔥 创建文本字段配置
  factory SmartFieldConfig.text({
    required String fieldName,
    required String label,
    String? hint,
    bool isRequired = false,
    int maxLength = 100,
    InputType inputType = InputType.textField,
  }) {
    return SmartFieldConfig(
      fieldName: fieldName,
      label: label,
      hint: hint,
      isRequired: isRequired,
      fieldType: SmartFieldType.text,
      fieldConfig: {
        'maxLength': maxLength,
        'inputType': inputType,
      },
    );
  }

  /// 🔥 创建XML选择器配置
  factory SmartFieldConfig.xmlPicker({
    required String fieldName,
    required String label,
    required String groundName,
    String? hint,
    String? title,
    bool isRequired = false,
  }) {
    return SmartFieldConfig(
      fieldName: fieldName,
      label: label,
      hint: hint,
      isRequired: isRequired,
      fieldType: SmartFieldType.xmlPicker,
      fieldConfig: {
        'groundName': groundName,
        'title': title,
      },
    );
  }

  /// 🔥 创建多选XML选择器配置
  factory SmartFieldConfig.multiXmlPicker({
    required String fieldName,
    required String label,
    required String groundName,
    String? hint,
    String? title,
    bool isRequired = false,
    int? maxSelection,
  }) {
    return SmartFieldConfig(
      fieldName: fieldName,
      label: label,
      hint: hint,
      isRequired: isRequired,
      fieldType: SmartFieldType.multiXmlPicker,
      fieldConfig: {
        'groundName': groundName,
        'title': title,
        'maxSelection': maxSelection,
      },
    );
  }

  /// 🔥 创建日期选择器配置
  factory SmartFieldConfig.datePicker({
    required String fieldName,
    required String label,
    String? hint,
    bool isRequired = false,
    DatePickerConfig? datePickerConfig,
  }) {
    return SmartFieldConfig(
      fieldName: fieldName,
      label: label,
      hint: hint,
      isRequired: isRequired,
      fieldType: SmartFieldType.datePicker,
      fieldConfig: {
        'datePickerConfig':
            datePickerConfig ?? DatePickerConfig.workingAgeBirthday(),
      },
    );
  }

  /// 🔥 创建生日选择器配置
  factory SmartFieldConfig.birthday({
    required String fieldName,
    String? label,
    String? hint,
    bool isRequired = false,
  }) {
    return SmartFieldConfig.datePicker(
      fieldName: fieldName,
      label: label ?? '生日',
      hint: hint ?? '请选择生日',
      isRequired: isRequired,
      datePickerConfig: DatePickerConfig.workingAgeBirthday(),
    );
  }
}
