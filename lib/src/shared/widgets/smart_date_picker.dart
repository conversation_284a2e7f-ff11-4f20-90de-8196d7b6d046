import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/datetime_utils.dart';
import '../../features/resume/theme/resume_theme.dart';

/// 🔥 智能日期选择器 - 参考优秀GitHub项目设计
/// 
/// 参考项目：
/// - flutter_datetime_picker: 滚轮选择器设计
/// - cupertino_date_picker: iOS风格日期选择
/// - date_picker_timeline: 时间轴选择器
/// 
/// 功能特性：
/// - 支持年-月和年-月-日两种模式
/// - 智能的年龄范围限制
/// - 流畅的滚轮交互体验
/// - 符合项目架构规范
class SmartDatePicker {
  
  /// 🔥 显示日期选择器
  static Future<DateTime?> show({
    required BuildContext context,
    DateTime? initialDate,
    DatePickeMode mode = DatePickeMode.yearMonthDay,
    int? minAge,
    int? maxAge,
    String? title,
  }) async {
    return await showModalBottomSheet<DateTime>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return _DatePickerBottomSheet(
          initialDate: initialDate,
          mode: mode,
          minAge: minAge,
          maxAge: maxAge,
          title: title ?? (mode == DatePickeMode.yearMonth ? '选择年月' : '选择生日'),
        );
      },
    );
  }
}

/// 🔥 日期选择模式
enum DatePickeMode {
  yearMonth,    // 年-月模式
  yearMonthDay, // 年-月-日模式
}

/// 🔥 日期选择器底部弹窗
class _DatePickerBottomSheet extends StatefulWidget {
  final DateTime? initialDate;
  final DatePickeMode mode;
  final int? minAge;
  final int? maxAge;
  final String title;

  const _DatePickerBottomSheet({
    this.initialDate,
    required this.mode,
    this.minAge,
    this.maxAge,
    required this.title,
  });

  @override
  State<_DatePickerBottomSheet> createState() => _DatePickerBottomSheetState();
}

class _DatePickerBottomSheetState extends State<_DatePickerBottomSheet> {
  late FixedExtentScrollController _yearController;
  late FixedExtentScrollController _monthController;
  late FixedExtentScrollController _dayController;
  
  late List<int> _years;
  late List<int> _months;
  late List<int> _days;
  
  late int _selectedYear;
  late int _selectedMonth;
  late int _selectedDay;

  @override
  void initState() {
    super.initState();
    _initializeData();
    _initializeControllers();
  }

  /// 🔥 初始化数据
  void _initializeData() {
    // 计算年份范围
    final range = widget.minAge != null || widget.maxAge != null
        ? DateTimeUtils.getBirthYearRange(
            minAge: widget.minAge ?? 0,
            maxAge: widget.maxAge ?? 100,
          )
        : DateTimeUtils.getWorkingAgeBirthYearRange();
    
    _years = range.years.reversed.toList(); // 倒序，最新年份在前
    _months = List.generate(12, (index) => index + 1);
    
    // 设置初始选中值
    final now = DateTime.now();
    final initialDate = widget.initialDate ?? DateTime(now.year - 25, now.month, now.day);
    
    _selectedYear = initialDate.year;
    _selectedMonth = initialDate.month;
    _selectedDay = initialDate.day;
    
    // 确保选中的年份在范围内
    if (!_years.contains(_selectedYear)) {
      _selectedYear = _years.first;
    }
    
    _updateDays();
  }

  /// 🔥 初始化控制器
  void _initializeControllers() {
    _yearController = FixedExtentScrollController(
      initialItem: _years.indexOf(_selectedYear),
    );
    _monthController = FixedExtentScrollController(
      initialItem: _selectedMonth - 1,
    );
    _dayController = FixedExtentScrollController(
      initialItem: _selectedDay - 1,
    );
  }

  /// 🔥 更新天数列表
  void _updateDays() {
    final daysInMonth = DateTimeUtils.getDaysInMonth(_selectedYear, _selectedMonth);
    _days = List.generate(daysInMonth, (index) => index + 1);
    
    // 如果当前选中的天数超过了该月的天数，调整为最后一天
    if (_selectedDay > daysInMonth) {
      _selectedDay = daysInMonth;
      if (mounted) {
        _dayController.animateToItem(
          _selectedDay - 1,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  @override
  void dispose() {
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 350.h,
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        children: [
          // 头部
          _buildHeader(),
          // 分割线
          Container(height: 1.h, color: ResumeTheme.borderColor),
          // 选择器
          Expanded(child: _buildPickers()),
        ],
      ),
    );
  }

  /// 🔥 构建头部
  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 取消按钮
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              '取消',
              style: TextStyle(
                color: ResumeTheme.textSecondary,
                fontSize: 16.sp,
              ),
            ),
          ),
          // 标题
          Text(
            widget.title,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16.sp,
              color: ResumeTheme.textPrimary,
            ),
          ),
          // 确定按钮
          TextButton(
            onPressed: _handleConfirm,
            child: Text(
              '确定',
              style: TextStyle(
                color: ResumeTheme.primaryColor,
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🔥 构建选择器
  Widget _buildPickers() {
    return Row(
      children: [
        // 年份选择器
        Expanded(child: _buildYearPicker()),
        // 月份选择器
        Expanded(child: _buildMonthPicker()),
        // 日期选择器（仅在年月日模式下显示）
        if (widget.mode == DatePickeMode.yearMonthDay)
          Expanded(child: _buildDayPicker()),
      ],
    );
  }

  /// 🔥 构建年份选择器
  Widget _buildYearPicker() {
    return _buildWheelPicker(
      controller: _yearController,
      itemCount: _years.length,
      itemBuilder: (index) => '${_years[index]}年',
      onSelectedItemChanged: (index) {
        setState(() {
          _selectedYear = _years[index];
          _updateDays();
        });
      },
    );
  }

  /// 🔥 构建月份选择器
  Widget _buildMonthPicker() {
    return _buildWheelPicker(
      controller: _monthController,
      itemCount: _months.length,
      itemBuilder: (index) => '${_months[index].toString().padLeft(2, '0')}月',
      onSelectedItemChanged: (index) {
        setState(() {
          _selectedMonth = _months[index];
          _updateDays();
        });
      },
    );
  }

  /// 🔥 构建日期选择器
  Widget _buildDayPicker() {
    return _buildWheelPicker(
      controller: _dayController,
      itemCount: _days.length,
      itemBuilder: (index) => '${_days[index].toString().padLeft(2, '0')}日',
      onSelectedItemChanged: (index) {
        setState(() {
          _selectedDay = _days[index];
        });
      },
    );
  }

  /// 🔥 构建滚轮选择器
  Widget _buildWheelPicker({
    required FixedExtentScrollController controller,
    required int itemCount,
    required String Function(int) itemBuilder,
    required Function(int) onSelectedItemChanged,
  }) {
    return ListWheelScrollView.useDelegate(
      controller: controller,
      itemExtent: 40.h,
      perspective: 0.005,
      diameterRatio: 1.2,
      physics: const FixedExtentScrollPhysics(),
      onSelectedItemChanged: onSelectedItemChanged,
      childDelegate: ListWheelChildBuilderDelegate(
        childCount: itemCount,
        builder: (context, index) {
          return Center(
            child: Text(
              itemBuilder(index),
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w500,
                color: ResumeTheme.textPrimary,
              ),
            ),
          );
        },
      ),
    );
  }

  /// 🔥 处理确认
  void _handleConfirm() {
    final selectedDate = DateTime(_selectedYear, _selectedMonth, _selectedDay);
    Navigator.pop(context, selectedDate);
  }
}

/// 🔥 日期选择器配置类
class DatePickerConfig {
  final DatePickeMode mode;
  final int? minAge;
  final int? maxAge;
  final String? title;

  const DatePickerConfig({
    this.mode = DatePickeMode.yearMonthDay,
    this.minAge,
    this.maxAge,
    this.title,
  });

  /// 🔥 生日选择器配置
  factory DatePickerConfig.birthday({String? title}) {
    return DatePickerConfig(
      mode: DatePickeMode.yearMonthDay,
      minAge: 0,
      maxAge: 100,
      title: title ?? '选择生日',
    );
  }

  /// 🔥 工作年龄生日选择器配置
  factory DatePickerConfig.workingAgeBirthday({String? title}) {
    return DatePickerConfig(
      mode: DatePickeMode.yearMonthDay,
      minAge: 16,
      maxAge: 65,
      title: title ?? '选择生日',
    );
  }

  /// 🔥 年月选择器配置
  factory DatePickerConfig.yearMonth({String? title}) {
    return DatePickerConfig(
      mode: DatePickeMode.yearMonth,
      title: title ?? '选择年月',
    );
  }
}
