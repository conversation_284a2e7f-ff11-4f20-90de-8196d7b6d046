# CustomListView 使用指南

## 🎯 概述

CustomListView是一个高性能的自定义ListView组件，专为Flutter Kit项目架构设计，支持上拉加载、下拉刷新、动画效果等功能。

## ✨ 核心特性

- ✅ **架构兼容** - 使用ViewStatePagingLogic和Provider模式
- ✅ **泛型支持** - 支持任意数据类型
- ✅ **上拉加载** - 自动检测滚动位置触发加载更多
- ✅ **下拉刷新** - 内置RefreshIndicator支持
- ✅ **动画效果** - 丰富的Item动画和加载动画
- ✅ **高性能** - 内存优化和渲染优化
- ✅ **数据源适配** - 支持API、本地、静态、缓存等多种数据源
- ✅ **错误处理** - 完善的错误处理和重试机制

## 📦 组件结构

```
custom_list_view/
├── custom_list_view.dart          # 主组件
├── custom_list_logic.dart         # 业务逻辑
├── custom_list_data_source.dart   # 数据源适配器
└── widgets/
    └── animated_list_item.dart    # 动画组件
```

## 🚀 快速开始

### 1. 创建数据源

```dart
// API数据源
final dataSource = ApiListDataSource<JobInfoEntity>(
  apiCall: (page, pageSize) async {
    return await repository.getJobList(page, pageSize);
  },
  pageSize: 20,
  name: 'JobListDataSource',
);

// 静态数据源
final staticDataSource = StaticListDataSource<String>(
  staticData: ['Item 1', 'Item 2', 'Item 3'],
  pageSize: 10,
);
```

### 2. 使用CustomListView

```dart
CustomListView<JobInfoEntity>(
  dataSource: dataSource,
  itemBuilder: (context, item, index) {
    return JobListItem(job: item);
  },
  enableRefresh: true,
  enableLoadMore: true,
  enableItemAnimation: true,
  padding: EdgeInsets.all(16),
)
```

## 📋 数据源类型

### ApiListDataSource
用于从API接口获取数据，支持数据转换和验证。

### LocalListDataSource  
用于从本地数据源（数据库、缓存）获取数据。

### StaticListDataSource
用于静态数据列表，支持分页模拟。

### CachedListDataSource
支持数据缓存的数据源，提高性能。

### MixedListDataSource
支持多个数据源的组合使用，提供容错能力。

## 🎨 动画配置

### Item动画类型
- `AnimationType.fade` - 淡入动画
- `AnimationType.slide` - 滑入动画  
- `AnimationType.scale` - 缩放动画
- `AnimationType.slideAndFade` - 滑入+淡入
- `AnimationType.scaleAndFade` - 缩放+淡入
- `AnimationType.all` - 全部动画

### 滑入方向
- `SlideDirection.fromTop` - 从顶部滑入
- `SlideDirection.fromBottom` - 从底部滑入
- `SlideDirection.fromLeft` - 从左侧滑入
- `SlideDirection.fromRight` - 从右侧滑入

## ⚙️ 高级配置

### 自定义空状态
```dart
CustomListView<T>(
  // ...
  emptyWidget: CustomEmptyWidget(),
)
```

### 自定义错误状态
```dart
CustomListView<T>(
  // ...
  errorWidget: CustomErrorWidget(),
)
```

### 自定义分隔符
```dart
CustomListView<T>(
  // ...
  separatorBuilder: (context, index) => Divider(),
)
```

### 滚动控制
```dart
final scrollController = ScrollController();

CustomListView<T>(
  // ...
  scrollController: scrollController,
  physics: BouncingScrollPhysics(),
)
```

## 🔧 性能优化

### 1. 合理设置pageSize
```dart
// 根据Item高度和屏幕大小设置合理的pageSize
pageSize: 20, // 推荐值：15-30
```

### 2. 使用缓存数据源
```dart
final cachedDataSource = CachedListDataSource<T>(
  primaryDataSource: apiDataSource,
  cacheExpireTime: 300000, // 5分钟
);
```

### 3. 禁用不必要的动画
```dart
CustomListView<T>(
  // ...
  enableItemAnimation: false, // 大量数据时禁用
)
```

### 4. 使用shrinkWrap
```dart
CustomListView<T>(
  // ...
  shrinkWrap: true, // 在ScrollView中使用
)
```

## 🐛 常见问题

### Q: 如何处理网络错误？
A: 数据源会自动处理网络错误，组件会显示错误状态和重试按钮。

### Q: 如何自定义加载指示器？
A: 重写`_buildLoadMoreIndicator`方法或使用自定义Footer。

### Q: 如何实现无限滚动？
A: 设置`enableLoadMore: true`，组件会自动检测滚动位置触发加载。

### Q: 如何优化大量数据的性能？
A: 使用合理的pageSize、禁用动画、使用缓存数据源。

## 📚 最佳实践

### 1. 数据源选择
- **API数据** - 使用ApiListDataSource
- **本地数据** - 使用LocalListDataSource  
- **静态数据** - 使用StaticListDataSource
- **需要缓存** - 使用CachedListDataSource
- **多数据源** - 使用MixedListDataSource

### 2. 错误处理
- 实现数据验证逻辑
- 提供友好的错误提示
- 支持重试机制

### 3. 用户体验
- 合理的加载动画
- 平滑的滚动体验
- 清晰的状态反馈

### 4. 性能优化
- 避免频繁的setState
- 使用Provider状态管理
- 合理的缓存策略

## 🔗 相关组件

- `ViewStatePagingLogic` - 分页逻辑基类
- `UnifiedResponse` - 统一响应格式
- `Provider` - 状态管理
- `AnimatedListItem` - 动画组件

## 📝 注意事项

1. **架构兼容** - 必须使用项目的ViewStatePagingLogic
2. **状态管理** - 使用Provider，避免setState
3. **内存管理** - 及时释放资源，避免内存泄漏
4. **错误处理** - 完善的错误处理和用户反馈
5. **性能优化** - 根据实际需求调整配置参数
