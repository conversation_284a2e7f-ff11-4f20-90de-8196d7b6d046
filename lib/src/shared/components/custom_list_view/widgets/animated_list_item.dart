import 'package:flutter/material.dart';

/// 🎯 动画列表项组件
/// 
/// 功能特性：
/// - ✅ 支持淡入动画
/// - ✅ 支持滑入动画
/// - ✅ 支持缩放动画
/// - ✅ 可配置动画延迟
/// - ✅ 高性能优化
class AnimatedListItem extends StatefulWidget {
  /// 子组件
  final Widget child;
  
  /// 列表索引
  final int index;
  
  /// 动画延迟
  final Duration delay;
  
  /// 动画持续时间
  final Duration duration;
  
  /// 动画类型
  final AnimationType animationType;
  
  /// 动画曲线
  final Curve curve;
  
  /// 滑入方向
  final SlideDirection slideDirection;
  
  /// 是否启用动画
  final bool enabled;

  const AnimatedListItem({
    super.key,
    required this.child,
    required this.index,
    this.delay = const Duration(milliseconds: 100),
    this.duration = const Duration(milliseconds: 500),
    this.animationType = AnimationType.slideAndFade,
    this.curve = Curves.easeOutCubic,
    this.slideDirection = SlideDirection.fromBottom,
    this.enabled = true,
  });

  @override
  State<AnimatedListItem> createState() => _AnimatedListItemState();
}

class _AnimatedListItemState extends State<AnimatedListItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimation();
  }

  /// 设置动画
  void _setupAnimations() {
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    // 淡入动画
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    // 滑入动画
    _slideAnimation = Tween<Offset>(
      begin: _getSlideBeginOffset(),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    // 缩放动画
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));
  }

  /// 获取滑入起始偏移
  Offset _getSlideBeginOffset() {
    switch (widget.slideDirection) {
      case SlideDirection.fromTop:
        return const Offset(0.0, -0.5);
      case SlideDirection.fromBottom:
        return const Offset(0.0, 0.5);
      case SlideDirection.fromLeft:
        return const Offset(-0.5, 0.0);
      case SlideDirection.fromRight:
        return const Offset(0.5, 0.0);
    }
  }

  /// 开始动画
  void _startAnimation() {
    if (!widget.enabled) {
      _controller.value = 1.0;
      return;
    }

    // 计算延迟时间
    final totalDelay = Duration(
      milliseconds: widget.delay.inMilliseconds * widget.index,
    );

    Future.delayed(totalDelay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return _buildAnimatedWidget();
      },
    );
  }

  /// 构建动画组件
  Widget _buildAnimatedWidget() {
    Widget animatedChild = widget.child;

    switch (widget.animationType) {
      case AnimationType.fade:
        animatedChild = FadeTransition(
          opacity: _fadeAnimation,
          child: animatedChild,
        );
        break;

      case AnimationType.slide:
        animatedChild = SlideTransition(
          position: _slideAnimation,
          child: animatedChild,
        );
        break;

      case AnimationType.scale:
        animatedChild = ScaleTransition(
          scale: _scaleAnimation,
          child: animatedChild,
        );
        break;

      case AnimationType.slideAndFade:
        animatedChild = SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: animatedChild,
          ),
        );
        break;

      case AnimationType.scaleAndFade:
        animatedChild = ScaleTransition(
          scale: _scaleAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: animatedChild,
          ),
        );
        break;

      case AnimationType.all:
        animatedChild = SlideTransition(
          position: _slideAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: animatedChild,
            ),
          ),
        );
        break;
    }

    return animatedChild;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

/// 动画类型枚举
enum AnimationType {
  /// 仅淡入
  fade,
  
  /// 仅滑入
  slide,
  
  /// 仅缩放
  scale,
  
  /// 滑入 + 淡入
  slideAndFade,
  
  /// 缩放 + 淡入
  scaleAndFade,
  
  /// 全部动画
  all,
}

/// 滑入方向枚举
enum SlideDirection {
  /// 从顶部滑入
  fromTop,
  
  /// 从底部滑入
  fromBottom,
  
  /// 从左侧滑入
  fromLeft,
  
  /// 从右侧滑入
  fromRight,
}

/// 🎯 刷新动画组件
class RefreshAnimationWidget extends StatefulWidget {
  /// 是否正在刷新
  final bool isRefreshing;
  
  /// 刷新进度 (0.0 - 1.0)
  final double progress;
  
  /// 动画颜色
  final Color? color;
  
  /// 动画大小
  final double size;

  const RefreshAnimationWidget({
    super.key,
    required this.isRefreshing,
    this.progress = 0.0,
    this.color,
    this.size = 24.0,
  });

  @override
  State<RefreshAnimationWidget> createState() => _RefreshAnimationWidgetState();
}

class _RefreshAnimationWidgetState extends State<RefreshAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimation();
  }

  void _setupAnimation() {
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_controller);
  }

  @override
  void didUpdateWidget(RefreshAnimationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isRefreshing && !oldWidget.isRefreshing) {
      _controller.repeat();
    } else if (!widget.isRefreshing && oldWidget.isRefreshing) {
      _controller.stop();
      _controller.reset();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isRefreshing) {
      return RotationTransition(
        turns: _rotationAnimation,
        child: Icon(
          Icons.refresh,
          size: widget.size,
          color: widget.color ?? Theme.of(context).primaryColor,
        ),
      );
    }

    return Icon(
      Icons.arrow_downward,
      size: widget.size,
      color: widget.color ?? Theme.of(context).primaryColor,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

/// 🎯 加载动画组件
class LoadMoreAnimationWidget extends StatefulWidget {
  /// 是否正在加载
  final bool isLoading;
  
  /// 动画颜色
  final Color? color;
  
  /// 动画大小
  final double size;
  
  /// 提示文本
  final String? text;

  const LoadMoreAnimationWidget({
    super.key,
    required this.isLoading,
    this.color,
    this.size = 20.0,
    this.text,
  });

  @override
  State<LoadMoreAnimationWidget> createState() => _LoadMoreAnimationWidgetState();
}

class _LoadMoreAnimationWidgetState extends State<LoadMoreAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimation();
  }

  void _setupAnimation() {
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(LoadMoreAnimationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isLoading && !oldWidget.isLoading) {
      _controller.repeat(reverse: true);
    } else if (!widget.isLoading && oldWidget.isLoading) {
      _controller.stop();
      _controller.reset();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widget.isLoading) ...[
            ScaleTransition(
              scale: _scaleAnimation,
              child: SizedBox(
                width: widget.size,
                height: widget.size,
                child: CircularProgressIndicator(
                  strokeWidth: 2.0,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    widget.color ?? Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ),
            if (widget.text != null) ...[
              const SizedBox(width: 12),
              Text(
                widget.text!,
                style: TextStyle(
                  color: widget.color ?? Theme.of(context).primaryColor,
                  fontSize: 14,
                ),
              ),
            ],
          ] else ...[
            Icon(
              Icons.keyboard_arrow_up,
              size: widget.size,
              color: Colors.grey,
            ),
            if (widget.text != null) ...[
              const SizedBox(width: 8),
              Text(
                widget.text!,
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
              ),
            ],
          ],
        ],
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
