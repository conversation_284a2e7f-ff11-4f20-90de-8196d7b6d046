import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'custom_list_logic.dart';
import 'custom_list_data_source.dart';
import 'widgets/animated_list_item.dart';

/// 🎯 高性能自定义ListView组件
/// 
/// 功能特性：
/// - ✅ 支持上拉加载、下拉刷新
/// - ✅ 使用ViewStatePagingLogic进行状态管理
/// - ✅ 支持泛型数据类型
/// - ✅ 内置动画效果
/// - ✅ 高性能优化
/// - ✅ 符合项目架构规范
class CustomListView<T> extends StatefulWidget {
  /// 数据源适配器
  final CustomListDataSource<T> dataSource;

  /// 外部提供的CustomListLogic实例（可选）
  final CustomListLogic<T>? externalLogic;

  /// 列表项构建器
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  
  /// 自定义刷新头部
  final Widget? customHeader;
  
  /// 自定义加载尾部
  final Widget? customFooter;
  
  /// 是否启用下拉刷新
  final bool enableRefresh;
  
  /// 是否启用上拉加载
  final bool enableLoadMore;
  
  /// 是否启用Item动画
  final bool enableItemAnimation;
  
  /// Item动画延迟
  final Duration itemAnimationDelay;
  
  /// 列表内边距
  final EdgeInsetsGeometry? padding;
  
  /// 分隔符构建器
  final Widget Function(BuildContext context, int index)? separatorBuilder;
  
  /// 空数据占位符
  final Widget? emptyWidget;
  
  /// 错误占位符
  final Widget? errorWidget;
  
  /// 滚动控制器
  final ScrollController? scrollController;
  
  /// 滚动物理效果
  final ScrollPhysics? physics;
  
  /// 是否收缩包装
  final bool shrinkWrap;
  
  /// 滚动方向
  final Axis scrollDirection;
  
  /// 反向滚动
  final bool reverse;
  
  /// 主轴方向上的子组件数量
  final int? itemCount;

  const CustomListView({
    super.key,
    required this.dataSource,
    required this.itemBuilder,
    this.externalLogic,
    this.customHeader,
    this.customFooter,
    this.enableRefresh = true,
    this.enableLoadMore = true,
    this.enableItemAnimation = true,
    this.itemAnimationDelay = const Duration(milliseconds: 100),
    this.padding,
    this.separatorBuilder,
    this.emptyWidget,
    this.errorWidget,
    this.scrollController,
    this.physics,
    this.shrinkWrap = false,
    this.scrollDirection = Axis.vertical,
    this.reverse = false,
    this.itemCount,
  });

  @override
  State<CustomListView<T>> createState() => _CustomListViewState<T>();
}

class _CustomListViewState<T> extends State<CustomListView<T>>
    with TickerProviderStateMixin {
  late CustomListLogic<T> _logic;
  late ScrollController _scrollController;
  late AnimationController _refreshAnimationController;
  late AnimationController _loadAnimationController;
  
  // 动画相关
  late Animation<double> _refreshAnimation;
  late Animation<double> _loadAnimation;
  
  // 刷新状态
  bool _isRefreshing = false;
  bool _isLoadingMore = false;
  
  // 滚动监听
  bool _isScrolling = false;
  double _lastScrollOffset = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeComponents();
    _setupAnimations();
    _setupScrollListener();
  }

  /// 初始化组件
  void _initializeComponents() {
    // 🔥 如果外部提供了logic实例，使用外部的；否则创建新的
    _logic = widget.externalLogic ?? CustomListLogic<T>(dataSource: widget.dataSource);
    _scrollController = widget.scrollController ?? ScrollController();
  }

  /// 设置动画
  void _setupAnimations() {
    _refreshAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _loadAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _refreshAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _refreshAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _loadAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  /// 设置滚动监听
  void _setupScrollListener() {
    _scrollController.addListener(_onScroll);
  }

  /// 滚动监听处理
  void _onScroll() {
    final currentOffset = _scrollController.offset;
    final maxScrollExtent = _scrollController.position.maxScrollExtent;
    
    // 检测是否到达底部，触发加载更多
    if (widget.enableLoadMore && 
        currentOffset >= maxScrollExtent - 200 && 
        !_isLoadingMore &&
        _logic.hasMore) {
      _triggerLoadMore();
    }
    
    // 更新滚动状态
    setState(() {
      _isScrolling = currentOffset != _lastScrollOffset;
      _lastScrollOffset = currentOffset;
    });
  }

  /// 触发下拉刷新
  Future<void> _triggerRefresh() async {
    if (_isRefreshing) return;
    
    setState(() {
      _isRefreshing = true;
    });
    
    _refreshAnimationController.forward();
    
    try {
      await _logic.refresh();
    } finally {
      _refreshAnimationController.reverse();
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  /// 触发上拉加载
  Future<void> _triggerLoadMore() async {
    if (_isLoadingMore) return;
    
    setState(() {
      _isLoadingMore = true;
    });
    
    _loadAnimationController.forward();
    
    try {
      await _logic.loadMore();
    } finally {
      _loadAnimationController.reverse();
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _logic,
      child: Consumer<CustomListLogic<T>>(
        builder: (context, logic, child) {
          return _buildListView(logic);
        },
      ),
    );
  }

  /// 构建ListView
  Widget _buildListView(CustomListLogic<T> logic) {
    // 🔥 如果有customHeader，所有状态都需要包含header
    if (widget.customHeader != null) {
      return RefreshIndicator(
        onRefresh: widget.enableRefresh ? _triggerRefresh : () async {},
        child: _buildScrollableListWithStates(logic),
      );
    }

    // 🔥 没有customHeader时，保持原有逻辑
    if (logic.viewState.isLoading() && logic.items.isEmpty) {
      return _buildLoadingWidget();
    }

    if (logic.viewState.isError() && logic.items.isEmpty) {
      return _buildErrorWidget(logic);
    }

    if (logic.items.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: widget.enableRefresh ? _triggerRefresh : () async {},
      child: _buildScrollableList(logic),
    );
  }

  /// 🔥 构建带状态处理的可滚动列表（有customHeader时使用）
  Widget _buildScrollableListWithStates(CustomListLogic<T> logic) {
    return CustomScrollView(
      controller: _scrollController,
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      scrollDirection: widget.scrollDirection,
      reverse: widget.reverse,
      slivers: [
        // 🔥 头部视图 - 始终显示customHeader
        if (widget.customHeader != null)
          SliverToBoxAdapter(
            child: widget.customHeader!,
          ),

        // 🔥 状态内容区域 - 根据状态显示不同内容
        _buildStateSliver(logic),

        // 🔥 尾部视图 - 如果提供了customFooter
        if (widget.customFooter != null)
          SliverToBoxAdapter(
            child: widget.customFooter!,
          ),
      ],
    );
  }

  /// 构建可滚动列表
  Widget _buildScrollableList(CustomListLogic<T> logic) {
    // 🔥 使用CustomScrollView+Sliver架构支持头部视图
    return CustomScrollView(
      controller: _scrollController,
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      scrollDirection: widget.scrollDirection,
      reverse: widget.reverse,
      slivers: [
        // 🔥 头部视图 - 如果提供了customHeader
        if (widget.customHeader != null)
          SliverToBoxAdapter(
            child: widget.customHeader!,
          ),

        // 🔥 列表内容 - 使用SliverPadding包装
        SliverPadding(
          padding: widget.padding ?? EdgeInsets.zero,
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                // 处理分隔符
                if (widget.separatorBuilder != null && index.isOdd) {
                  final itemIndex = index ~/ 2;
                  if (itemIndex < logic.items.length) {
                    return widget.separatorBuilder!(context, itemIndex);
                  }
                  return null;
                }

                // 实际的列表项索引
                final itemIndex = widget.separatorBuilder != null ? index ~/ 2 : index;
                return _buildListItem(logic, itemIndex);
              },
              childCount: _getSliverChildCount(logic),
            ),
          ),
        ),

        // 🔥 尾部视图 - 如果提供了customFooter
        if (widget.customFooter != null)
          SliverToBoxAdapter(
            child: widget.customFooter!,
          ),
      ],
    );
  }

  /// 获取列表项数量（原ListView.separated使用）
  int _getItemCount(CustomListLogic<T> logic) {
    int count = logic.items.length;

    // 添加加载更多指示器
    if (widget.enableLoadMore && (logic.hasMore || _isLoadingMore)) {
      count += 1;
    }

    return count;
  }

  /// 🔥 构建状态Sliver（处理加载、错误、空数据状态）
  Widget _buildStateSliver(CustomListLogic<T> logic) {
    // 加载状态且无数据
    if (logic.viewState.isLoading() && logic.items.isEmpty) {
      return SliverFillRemaining(
        hasScrollBody: false,
        child: _buildLoadingWidget(),
      );
    }

    // 错误状态且无数据
    if (logic.viewState.isError() && logic.items.isEmpty) {
      return SliverFillRemaining(
        hasScrollBody: false,
        child: _buildErrorWidget(logic),
      );
    }

    // 空数据状态
    if (logic.items.isEmpty) {
      return SliverFillRemaining(
        hasScrollBody: false,
        child: _buildEmptyWidget(),
      );
    }

    // 有数据时，显示正常列表
    return SliverPadding(
      padding: widget.padding ?? EdgeInsets.zero,
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            // 处理分隔符
            if (widget.separatorBuilder != null && index.isOdd) {
              final itemIndex = index ~/ 2;
              if (itemIndex < logic.items.length) {
                return widget.separatorBuilder!(context, itemIndex);
              }
              return null;
            }

            // 实际的列表项索引
            final itemIndex = widget.separatorBuilder != null ? index ~/ 2 : index;
            return _buildListItem(logic, itemIndex);
          },
          childCount: _getSliverChildCount(logic),
        ),
      ),
    );
  }

  /// 🔥 获取Sliver子项数量（CustomScrollView使用）
  int _getSliverChildCount(CustomListLogic<T> logic) {
    int count = logic.items.length;

    // 如果有分隔符，需要计算分隔符的数量
    if (widget.separatorBuilder != null && count > 0) {
      count = count * 2 - 1; // 每个item之间一个分隔符，最后一个item后面没有分隔符
    }

    // 添加加载更多指示器
    if (widget.enableLoadMore && (logic.hasMore || _isLoadingMore)) {
      count += widget.separatorBuilder != null ? 2 : 1; // 如果有分隔符，需要加2（分隔符+加载指示器）
    }

    return count;
  }

  /// 构建列表项
  Widget _buildListItem(CustomListLogic<T> logic, int index) {
    // 加载更多指示器
    if (index >= logic.items.length) {
      return _buildLoadMoreIndicator();
    }
    
    final item = logic.items[index];
    
    // 使用动画包装
    if (widget.enableItemAnimation) {
      return AnimatedListItem(
        index: index,
        delay: widget.itemAnimationDelay,
        child: widget.itemBuilder(context, item, index),
      );
    }
    
    return widget.itemBuilder(context, item, index);
  }

  /// 构建加载指示器
  Widget _buildLoadingWidget() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget(CustomListLogic<T> logic) {
    return widget.errorWidget ?? Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text('加载失败: ${logic.viewState.errorMessage}'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => logic.retry(),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 构建空数据组件
  Widget _buildEmptyWidget() {
    return widget.emptyWidget ?? const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inbox, size: 48, color: Colors.grey),
          SizedBox(height: 16),
          Text('暂无数据', style: TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  /// 构建加载更多指示器
  Widget _buildLoadMoreIndicator() {
    return Container(
      height: 60,
      alignment: Alignment.center,
      child: _isLoadingMore
          ? const CircularProgressIndicator()
          : const Text('上拉加载更多', style: TextStyle(color: Colors.grey)),
    );
  }

  @override
  void dispose() {
    _refreshAnimationController.dispose();
    _loadAnimationController.dispose();
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    _logic.dispose();
    super.dispose();
  }
}
