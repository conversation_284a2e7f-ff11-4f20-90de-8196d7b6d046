import 'package:flutter_kit/src/base/core/base_result.dart';

/// 🎯 自定义列表数据源适配器
/// 
/// 功能特性：
/// - ✅ 支持泛型数据类型
/// - ✅ 统一API响应处理
/// - ✅ 分页参数配置
/// - ✅ 错误处理和重试机制
/// - ✅ 数据转换和验证
abstract class CustomListDataSource<T> {
  /// 每页数据量
  final int pageSize;
  
  /// 数据源名称
  final String name;

  const CustomListDataSource({
    this.pageSize = 20,
    required this.name,
  });

  /// 加载数据的抽象方法
  /// 
  /// [page] 页码，从1开始
  /// [pageSize] 每页数据量
  /// 
  /// 返回 [BaseResult<List<T>>] 统一响应格式
  Future<BaseResult<List<T>>> loadData({
    required int page,
    required int pageSize,
  });

  /// 数据转换方法（可选重写）
  /// 
  /// 用于将原始数据转换为目标类型
  List<T> transformData(dynamic rawData) {
    if (rawData is List<T>) {
      return rawData;
    }
    throw UnsupportedError('Data transformation not implemented for type: ${rawData.runtimeType}');
  }

  /// 验证数据方法（可选重写）
  /// 
  /// 用于验证数据的有效性
  bool validateData(List<T> data) {
    return data.isNotEmpty;
  }

  @override
  String toString() => 'CustomListDataSource<$T>(name: $name, pageSize: $pageSize)';
}

/// 🎯 API数据源适配器
/// 
/// 用于从API接口获取数据
class ApiListDataSource<T> extends CustomListDataSource<T> {
  /// API调用函数
  final Future<BaseResult<List<T>>> Function(int page, int pageSize) apiCall;
  
  /// 数据转换函数（可选）
  final List<T> Function(dynamic rawData)? dataTransformer;
  
  /// 数据验证函数（可选）
  final bool Function(List<T> data)? dataValidator;

  const ApiListDataSource({
    required this.apiCall,
    this.dataTransformer,
    this.dataValidator,
    super.pageSize = 20,
    super.name = 'ApiListDataSource',
  });

  @override
  Future<BaseResult<List<T>>> loadData({
    required int page,
    required int pageSize,
  }) async {
    try {
      print('🔍 ApiListDataSource - loadData: page=$page, pageSize=$pageSize');
      
      final response = await apiCall(page, pageSize);
      
      return response.when(
        success: (data) {
          print('🔍 ApiListDataSource - API success, raw data type: ${data.runtimeType}');
          
          List<T> transformedData;
          if (dataTransformer != null) {
            transformedData = dataTransformer!(data);
          } else {
            transformedData = transformData(data);
          }
          
          // 验证数据
          if (dataValidator != null) {
            if (!dataValidator!(transformedData)) {
              return BaseResult.failure('数据验证失败');
            }
          }
          
          print('🔍 ApiListDataSource - Transformed data count: ${transformedData.length}');
          return BaseResult.success(transformedData);
        },
        failure: (message) {
          print('🔍 ApiListDataSource - API failure: $message');
          return BaseResult.failure(message);
        },
        empty: () {
          print('🔍 ApiListDataSource - API empty');
          return BaseResult.empty();
        },
      );
    } catch (e) {
      print('🔍 ApiListDataSource - Exception: $e');
      return BaseResult.failure('网络请求失败: $e');
    }
  }
}

/// 🎯 本地数据源适配器
/// 
/// 用于从本地数据获取数据（如数据库、缓存等）
class LocalListDataSource<T> extends CustomListDataSource<T> {
  /// 本地数据获取函数
  final Future<List<T>> Function(int page, int pageSize) dataProvider;
  
  /// 总数据量获取函数（可选）
  final Future<int> Function()? totalCountProvider;

  const LocalListDataSource({
    required this.dataProvider,
    this.totalCountProvider,
    super.pageSize = 20,
    super.name = 'LocalListDataSource',
  });

  @override
  Future<BaseResult<List<T>>> loadData({
    required int page,
    required int pageSize,
  }) async {
    try {
      print('🔍 LocalListDataSource - loadData: page=$page, pageSize=$pageSize');

      final data = await dataProvider(page, pageSize);

      print('🔍 LocalListDataSource - Local data count: ${data.length}');

      if (data.isEmpty) {
        return BaseResult.empty();
      }

      return BaseResult.success(data);
    } catch (e) {
      print('🔍 LocalListDataSource - Exception: $e');
      return BaseResult.failure('本地数据加载失败: $e');
    }
  }
}

/// 🎯 静态数据源适配器
/// 
/// 用于静态数据列表
class StaticListDataSource<T> extends CustomListDataSource<T> {
  /// 静态数据列表
  final List<T> staticData;
  
  /// 是否启用分页
  final bool enablePaging;

  const StaticListDataSource({
    required this.staticData,
    this.enablePaging = true,
    super.pageSize = 20,
    super.name = 'StaticListDataSource',
  });

  @override
  Future<BaseResult<List<T>>> loadData({
    required int page,
    required int pageSize,
  }) async {
    try {
      print('🔍 StaticListDataSource - loadData: page=$page, pageSize=$pageSize, total=${staticData.length}');

      if (!enablePaging) {
        return BaseResult.success(staticData);
      }

      final startIndex = (page - 1) * pageSize;
      final endIndex = (startIndex + pageSize).clamp(0, staticData.length);

      if (startIndex >= staticData.length) {
        return BaseResult.empty();
      }

      final pageData = staticData.sublist(startIndex, endIndex);

      print('🔍 StaticListDataSource - Page data count: ${pageData.length}');

      return BaseResult.success(pageData);
    } catch (e) {
      print('🔍 StaticListDataSource - Exception: $e');
      return BaseResult.failure('静态数据加载失败: $e');
    }
  }
}

/// 🎯 混合数据源适配器
/// 
/// 支持多个数据源的组合使用
class MixedListDataSource<T> extends CustomListDataSource<T> {
  /// 数据源列表
  final List<CustomListDataSource<T>> dataSources;
  
  /// 当前使用的数据源索引
  int _currentSourceIndex = 0;

  MixedListDataSource({
    required this.dataSources,
    super.pageSize = 20,
    super.name = 'MixedListDataSource',
  }) : assert(dataSources.isNotEmpty, 'Data sources cannot be empty');

  @override
  Future<BaseResult<List<T>>> loadData({
    required int page,
    required int pageSize,
  }) async {
    print('🔍 MixedListDataSource - loadData: page=$page, pageSize=$pageSize, sourceIndex=$_currentSourceIndex');

    // 尝试从当前数据源加载
    final currentSource = dataSources[_currentSourceIndex];
    final response = await currentSource.loadData(page: page, pageSize: pageSize);

    return response.when(
      success: (data) {
        return BaseResult.success(data);
      },
      failure: (message) {
        // 如果当前数据源失败，尝试下一个数据源
        return _tryNextDataSource(page, pageSize, message);
      },
      empty: () {
        return BaseResult.empty();
      },
    );
  }

  /// 尝试下一个数据源
  Future<BaseResult<List<T>>> _tryNextDataSource(
    int page,
    int pageSize,
    String lastError,
  ) async {
    if (_currentSourceIndex < dataSources.length - 1) {
      _currentSourceIndex++;
      print('🔍 MixedListDataSource - Trying next source: $_currentSourceIndex');
      return loadData(page: page, pageSize: pageSize);
    } else {
      // 所有数据源都失败了
      print('🔍 MixedListDataSource - All sources failed');
      return BaseResult.failure('所有数据源都不可用: $lastError');
    }
  }

  /// 重置数据源索引
  void resetDataSource() {
    _currentSourceIndex = 0;
  }

  /// 切换到指定数据源
  void switchToDataSource(int index) {
    if (index >= 0 && index < dataSources.length) {
      _currentSourceIndex = index;
    }
  }

  /// 获取当前数据源
  CustomListDataSource<T> get currentDataSource => dataSources[_currentSourceIndex];
}

/// 🎯 缓存数据源适配器
/// 
/// 支持数据缓存的数据源
class CachedListDataSource<T> extends CustomListDataSource<T> {
  /// 主数据源
  final CustomListDataSource<T> primaryDataSource;
  
  /// 缓存数据
  final Map<String, List<T>> _cache = {};
  
  /// 缓存过期时间（毫秒）
  final int cacheExpireTime;
  
  /// 缓存时间戳
  final Map<String, int> _cacheTimestamps = {};

  CachedListDataSource({
    required this.primaryDataSource,
    this.cacheExpireTime = 300000, // 5分钟
    super.pageSize = 20,
    super.name = 'CachedListDataSource',
  });

  @override
  Future<BaseResult<List<T>>> loadData({
    required int page,
    required int pageSize,
  }) async {
    final cacheKey = '${page}_$pageSize';
    final now = DateTime.now().millisecondsSinceEpoch;

    // 检查缓存
    if (_cache.containsKey(cacheKey)) {
      final cacheTime = _cacheTimestamps[cacheKey] ?? 0;
      if (now - cacheTime < cacheExpireTime) {
        print('🔍 CachedListDataSource - Using cached data for $cacheKey');
        return BaseResult.success(_cache[cacheKey]!);
      }
    }

    // 从主数据源加载
    print('🔍 CachedListDataSource - Loading from primary source for $cacheKey');
    final response = await primaryDataSource.loadData(page: page, pageSize: pageSize);

    return response.when(
      success: (data) {
        // 缓存数据
        _cache[cacheKey] = data;
        _cacheTimestamps[cacheKey] = now;
        print('🔍 CachedListDataSource - Cached data for $cacheKey');
        return BaseResult.success(data);
      },
      failure: (message) {
        // 如果有缓存数据，返回缓存数据
        if (_cache.containsKey(cacheKey)) {
          print('🔍 CachedListDataSource - Using stale cache for $cacheKey');
          return BaseResult.success(_cache[cacheKey]!);
        }
        return BaseResult.failure(message);
      },
      empty: () {
        return BaseResult.empty();
      },
    );
  }

  /// 清除缓存
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
    print('🔍 CachedListDataSource - Cache cleared');
  }

  /// 清除指定页面的缓存
  void clearPageCache(int page, int pageSize) {
    final cacheKey = '${page}_$pageSize';
    _cache.remove(cacheKey);
    _cacheTimestamps.remove(cacheKey);
    print('🔍 CachedListDataSource - Cache cleared for $cacheKey');
  }
}
