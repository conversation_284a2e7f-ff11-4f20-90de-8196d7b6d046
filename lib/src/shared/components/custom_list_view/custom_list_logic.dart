import 'package:flutter/foundation.dart';
import 'package:flutter_kit/src/base/logic/view_state_paging_logic.dart';
import 'custom_list_data_source.dart';

/// 🎯 自定义列表业务逻辑
/// 
/// 功能特性：
/// - ✅ 继承ViewStatePagingLogic，符合项目架构
/// - ✅ 支持泛型数据类型
/// - ✅ 分页加载和刷新逻辑
/// - ✅ 状态管理和错误处理
/// - ✅ 内存优化和性能优化
class CustomListLogic<T> extends ViewStatePagingLogic {
  /// 数据源适配器
  final CustomListDataSource<T> dataSource;
  
  /// 列表数据
  List<T> _items = [];
  
  /// 是否还有更多数据
  bool _hasMore = true;
  
  /// 总数据量
  int _totalCount = 0;
  
  /// 加载状态标记
  bool _isRefreshing = false;
  bool _isLoadingMore = false;
  
  /// 错误信息
  String? _lastError;

  CustomListLogic({
    required this.dataSource,
  }) {
    // 初始化分页参数
    pageSize = dataSource.pageSize;
    // 🔥 设置初始状态为Loading，确保组件首次显示加载状态
    setLoading();
  }

  // Getters
  List<T> get items => List.unmodifiable(_items);

  @override
  bool get hasMore => _hasMore;
  int get totalCount => _totalCount;
  bool get isRefreshing => _isRefreshing;
  bool get isLoadingMore => _isLoadingMore;
  String? get lastError => _lastError;
  bool get isEmpty => _items.isEmpty;
  int get itemCount => _items.length;

  @override
  void loadData() {
    refresh();
  }

  @override
  void refreshPaging() {
    refresh();
  }

  @override
  void loadMorePaging() {
    loadMore();
  }

  /// 刷新数据（下拉刷新）
  Future<void> refresh() async {
    if (_isRefreshing) return;
    
    print('🔍 CustomListLogic - refresh() called');
    
    _isRefreshing = true;
    _lastError = null;
    notifyListeners();
    
    try {
      // 重置分页参数
      curPage = 1;
      _hasMore = true;
      
      // 调用数据源获取数据
      final result = await dataSource.loadData(
        page: curPage,
        pageSize: pageSize,
      );
      
      result.when(
        success: (data) {
          print('🔍 CustomListLogic - refresh success, data count: ${data?.length ?? 0}');
          _handleRefreshSuccess(data);
        },
        failure: (message) {
          print('🔍 CustomListLogic - refresh failure: $message');
          _handleError(message);
        },
        empty: () {
          print('🔍 CustomListLogic - refresh empty');
          _handleRefreshSuccess([]);
        },
      );
    } catch (e) {
      print('🔍 CustomListLogic - refresh error: $e');
      _handleError(e.toString());
    } finally {
      _isRefreshing = false;
      notifyListeners();
    }
  }

  /// 加载更多数据（上拉加载）
  Future<void> loadMore() async {
    if (_isLoadingMore || !_hasMore) return;
    
    print('🔍 CustomListLogic - loadMore() called, page: ${curPage + 1}');
    
    _isLoadingMore = true;
    _lastError = null;
    notifyListeners();
    
    try {
      // 增加页码
      curPage++;
      
      // 调用数据源获取数据
      final result = await dataSource.loadData(
        page: curPage,
        pageSize: pageSize,
      );
      
      result.when(
        success: (data) {
          print('🔍 CustomListLogic - loadMore success, data count: ${data?.length ?? 0}');
          _handleLoadMoreSuccess(data);
        },
        failure: (message) {
          print('🔍 CustomListLogic - loadMore failure: $message');
          // 加载失败时回退页码
          curPage--;
          _handleError(message);
        },
        empty: () {
          print('🔍 CustomListLogic - loadMore empty');
          _handleLoadMoreSuccess([]);
        },
      );
    } catch (e) {
      print('🔍 CustomListLogic - loadMore error: $e');
      // 加载失败时回退页码
      curPage--;
      _handleError(e.toString());
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  /// 处理刷新成功
  void _handleRefreshSuccess(List<T>? data) {
    if (data != null) {
      _items = List.from(data);
      _totalCount = data.length;
      
      // 判断是否还有更多数据
      _hasMore = data.length >= pageSize;
      
      // 更新ViewState
      if (_items.isEmpty) {
        setEmpty();
      } else {
        setSuccess(_items);
      }
    }
  }

  /// 处理加载更多成功
  void _handleLoadMoreSuccess(List<T>? data) {
    if (data != null) {
      _items.addAll(data);
      _totalCount = _items.length;
      
      // 判断是否还有更多数据
      _hasMore = data.length >= pageSize;
      
      // 更新ViewState
      setSuccess(_items);
    }
  }

  /// 处理错误
  void _handleError(String message) {
    _lastError = message;
    
    // 如果是首次加载失败，设置错误状态
    if (_items.isEmpty) {
      setError(message);
    } else {
      // 如果已有数据，只更新错误信息，不改变ViewState
      notifyListeners();
    }
  }

  /// 重试加载
  @override
  void retry() {
    print('🔍 CustomListLogic - retry() called');
    if (_items.isEmpty) {
      // 首次加载重试
      refresh();
    } else {
      // 加载更多重试
      loadMore();
    }
  }

  /// 添加单个项目
  void addItem(T item) {
    _items.add(item);
    _totalCount = _items.length;
    notifyListeners();
  }

  /// 插入项目到指定位置
  void insertItem(int index, T item) {
    if (index >= 0 && index <= _items.length) {
      _items.insert(index, item);
      _totalCount = _items.length;
      notifyListeners();
    }
  }

  /// 移除项目
  void removeItem(T item) {
    if (_items.remove(item)) {
      _totalCount = _items.length;
      notifyListeners();
    }
  }

  /// 根据索引移除项目
  void removeItemAt(int index) {
    if (index >= 0 && index < _items.length) {
      _items.removeAt(index);
      _totalCount = _items.length;
      notifyListeners();
    }
  }

  /// 更新项目
  void updateItem(int index, T item) {
    if (index >= 0 && index < _items.length) {
      _items[index] = item;
      notifyListeners();
    }
  }

  /// 清空数据
  void clear() {
    _items.clear();
    _totalCount = 0;
    _hasMore = true;
    curPage = 1;
    _lastError = null;
    setEmpty();
  }

  /// 获取指定索引的项目
  T? getItem(int index) {
    if (index >= 0 && index < _items.length) {
      return _items[index];
    }
    return null;
  }

  /// 查找项目索引
  int findItemIndex(bool Function(T item) predicate) {
    for (int i = 0; i < _items.length; i++) {
      if (predicate(_items[i])) {
        return i;
      }
    }
    return -1;
  }

  /// 过滤项目
  List<T> filterItems(bool Function(T item) predicate) {
    return _items.where(predicate).toList();
  }

  /// 排序项目
  void sortItems(int Function(T a, T b) compare) {
    _items.sort(compare);
    notifyListeners();
  }

  /// 检查是否包含项目
  bool containsItem(T item) {
    return _items.contains(item);
  }

  /// 获取数据源信息
  String get dataSourceInfo => dataSource.toString();

  /// 获取分页信息
  @override
  Map<String, dynamic> get pagingInfo => {
    'currentPage': curPage,
    'pageSize': pageSize,
    'totalCount': _totalCount,
    'hasMore': _hasMore,
    'itemCount': _items.length,
  };

  @override
  void dispose() {
    _items.clear();
    super.dispose();
  }

  @override
  String toString() {
    return 'CustomListLogic<$T>('
        'itemCount: ${_items.length}, '
        'hasMore: $_hasMore, '
        'currentPage: $curPage, '
        'pageSize: $pageSize, '
        'isRefreshing: $_isRefreshing, '
        'isLoadingMore: $_isLoadingMore'
        ')';
  }

}
