import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';

/// 🎯 列表内存优化工具
/// 
/// 功能特性：
/// - ✅ 自动内存回收
/// - ✅ 图片缓存管理
/// - ✅ 数据缓存优化
/// - ✅ 内存泄漏检测
/// - ✅ 内存使用监控
class ListMemoryOptimizer {
  static final ListMemoryOptimizer _instance = ListMemoryOptimizer._internal();
  factory ListMemoryOptimizer() => _instance;
  ListMemoryOptimizer._internal() {
    _startMemoryMonitoring();
  }

  // 内存监控定时器
  Timer? _memoryMonitorTimer;
  
  // 缓存管理
  final Map<String, CacheEntry> _dataCache = {};
  final Map<String, DateTime> _lastAccessTime = {};
  
  // 配置参数
  int maxCacheSize = 100; // 最大缓存条目数
  Duration cacheExpireTime = const Duration(minutes: 10); // 缓存过期时间
  Duration memoryCheckInterval = const Duration(seconds: 30); // 内存检查间隔
  int memoryWarningThreshold = 200 * 1024 * 1024; // 内存警告阈值 (200MB)
  
  // 回调函数
  void Function(int memoryUsage)? onMemoryWarning;
  void Function(String message)? onMemoryOptimized;

  /// 开始内存监控
  void _startMemoryMonitoring() {
    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = Timer.periodic(memoryCheckInterval, (timer) {
      _checkMemoryUsage();
      _cleanupExpiredCache();
    });
  }

  /// 检查内存使用情况
  void _checkMemoryUsage() {
    // 在实际项目中，这里可以使用dart:developer的Service类
    // 或者其他内存监控工具来获取真实的内存使用情况
    final estimatedMemory = _estimateMemoryUsage();
    
    if (estimatedMemory > memoryWarningThreshold) {
      debugPrint('⚠️ ListMemoryOptimizer - High memory usage detected: ${estimatedMemory ~/ (1024 * 1024)}MB');
      onMemoryWarning?.call(estimatedMemory);
      _performMemoryOptimization();
    }
  }

  /// 估算内存使用量
  int _estimateMemoryUsage() {
    int totalSize = 0;
    
    for (final entry in _dataCache.values) {
      totalSize += entry.estimatedSize;
    }
    
    return totalSize;
  }

  /// 执行内存优化
  void _performMemoryOptimization() {
    final beforeSize = _estimateMemoryUsage();
    
    // 1. 清理过期缓存
    _cleanupExpiredCache();
    
    // 2. 清理最久未访问的缓存
    _cleanupLeastRecentlyUsed();
    
    // 3. 强制垃圾回收（在debug模式下）
    if (kDebugMode) {
      // 在实际项目中可以调用System.gc()等方法
    }
    
    final afterSize = _estimateMemoryUsage();
    final freedMemory = beforeSize - afterSize;
    
    if (freedMemory > 0) {
      final message = 'Memory optimized: freed ${freedMemory ~/ (1024 * 1024)}MB';
      debugPrint('🔧 ListMemoryOptimizer - $message');
      onMemoryOptimized?.call(message);
    }
  }

  /// 清理过期缓存
  void _cleanupExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _lastAccessTime.entries) {
      if (now.difference(entry.value) > cacheExpireTime) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _dataCache.remove(key);
      _lastAccessTime.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      debugPrint('🔧 ListMemoryOptimizer - Cleaned ${expiredKeys.length} expired cache entries');
    }
  }

  /// 清理最久未访问的缓存
  void _cleanupLeastRecentlyUsed() {
    if (_dataCache.length <= maxCacheSize) return;
    
    // 按访问时间排序
    final sortedEntries = _lastAccessTime.entries.toList()
      ..sort((a, b) => a.value.compareTo(b.value));
    
    // 删除最久未访问的条目
    final removeCount = _dataCache.length - maxCacheSize;
    for (int i = 0; i < removeCount && i < sortedEntries.length; i++) {
      final key = sortedEntries[i].key;
      _dataCache.remove(key);
      _lastAccessTime.remove(key);
    }
    
    debugPrint('🔧 ListMemoryOptimizer - Cleaned $removeCount LRU cache entries');
  }

  /// 缓存数据
  void cacheData<T>(String key, T data, {int? estimatedSize}) {
    final size = estimatedSize ?? _estimateDataSize(data);
    
    _dataCache[key] = CacheEntry(
      data: data,
      estimatedSize: size,
      createdAt: DateTime.now(),
    );
    
    _lastAccessTime[key] = DateTime.now();
    
    // 检查缓存大小
    if (_dataCache.length > maxCacheSize) {
      _cleanupLeastRecentlyUsed();
    }
  }

  /// 获取缓存数据
  T? getCachedData<T>(String key) {
    final entry = _dataCache[key];
    if (entry != null) {
      _lastAccessTime[key] = DateTime.now();
      return entry.data as T?;
    }
    return null;
  }

  /// 移除缓存数据
  void removeCachedData(String key) {
    _dataCache.remove(key);
    _lastAccessTime.remove(key);
  }

  /// 清空所有缓存
  void clearAllCache() {
    final beforeSize = _estimateMemoryUsage();
    
    _dataCache.clear();
    _lastAccessTime.clear();
    
    debugPrint('🔧 ListMemoryOptimizer - Cleared all cache, freed ${beforeSize ~/ (1024 * 1024)}MB');
  }

  /// 估算数据大小
  int _estimateDataSize(dynamic data) {
    if (data == null) return 0;
    
    if (data is String) {
      return data.length * 2; // UTF-16编码
    } else if (data is List) {
      return data.length * 8 + data.fold<int>(0, (sum, item) => sum + _estimateDataSize(item));
    } else if (data is Map) {
      return data.length * 16 + data.entries.fold<int>(0, (sum, entry) => 
          sum + _estimateDataSize(entry.key) + _estimateDataSize(entry.value));
    } else {
      return 64; // 默认对象大小估算
    }
  }

  /// 获取缓存统计信息
  CacheStatistics getCacheStatistics() {
    final totalSize = _estimateMemoryUsage();
    final entryCount = _dataCache.length;
    final averageSize = entryCount > 0 ? totalSize / entryCount : 0.0;
    
    return CacheStatistics(
      totalSize: totalSize,
      entryCount: entryCount,
      averageSize: averageSize,
      maxCacheSize: maxCacheSize,
      hitRate: 0.0, // 需要额外统计
    );
  }

  /// 优化配置
  void configure({
    int? maxCacheSize,
    Duration? cacheExpireTime,
    Duration? memoryCheckInterval,
    int? memoryWarningThreshold,
  }) {
    if (maxCacheSize != null) this.maxCacheSize = maxCacheSize;
    if (cacheExpireTime != null) this.cacheExpireTime = cacheExpireTime;
    if (memoryWarningThreshold != null) this.memoryWarningThreshold = memoryWarningThreshold;
    
    if (memoryCheckInterval != null) {
      this.memoryCheckInterval = memoryCheckInterval;
      _startMemoryMonitoring(); // 重启监控
    }
  }

  /// 停止内存监控
  void dispose() {
    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = null;
    clearAllCache();
  }

  /// 获取内存优化建议
  List<String> getOptimizationSuggestions() {
    final stats = getCacheStatistics();
    final suggestions = <String>[];
    
    if (stats.totalSize > memoryWarningThreshold) {
      suggestions.add('当前内存使用过高，建议清理缓存');
    }
    
    if (stats.entryCount > maxCacheSize * 0.8) {
      suggestions.add('缓存条目接近上限，建议增加maxCacheSize或减少缓存时间');
    }
    
    if (stats.averageSize > 1024 * 1024) { // 1MB
      suggestions.add('平均缓存条目较大，建议优化数据结构或压缩数据');
    }
    
    return suggestions;
  }
}

/// 缓存条目类
class CacheEntry {
  final dynamic data;
  final int estimatedSize;
  final DateTime createdAt;

  const CacheEntry({
    required this.data,
    required this.estimatedSize,
    required this.createdAt,
  });
}

/// 缓存统计信息类
class CacheStatistics {
  final int totalSize;
  final int entryCount;
  final double averageSize;
  final int maxCacheSize;
  final double hitRate;

  const CacheStatistics({
    required this.totalSize,
    required this.entryCount,
    required this.averageSize,
    required this.maxCacheSize,
    required this.hitRate,
  });

  @override
  String toString() {
    return 'CacheStatistics('
        'totalSize: ${totalSize ~/ (1024 * 1024)}MB, '
        'entryCount: $entryCount, '
        'averageSize: ${averageSize ~/ 1024}KB, '
        'usage: ${(entryCount / maxCacheSize * 100).toStringAsFixed(1)}%, '
        'hitRate: ${(hitRate * 100).toStringAsFixed(1)}%'
        ')';
  }
}

/// 内存优化Mixin
mixin ListMemoryOptimizerMixin<T extends StatefulWidget> on State<T> {
  late String _cachePrefix;

  void initMemoryOptimizer() {
    _cachePrefix = '${T.toString()}_$hashCode';
  }

  void disposeMemoryOptimizer() {
    // 清理相关缓存
    _clearRelatedCache();
  }

  /// 缓存数据
  void cacheData<D>(String key, D data, {int? estimatedSize}) {
    final fullKey = '${_cachePrefix}_$key';
    ListMemoryOptimizer().cacheData(fullKey, data, estimatedSize: estimatedSize);
  }

  /// 获取缓存数据
  D? getCachedData<D>(String key) {
    final fullKey = '${_cachePrefix}_$key';
    return ListMemoryOptimizer().getCachedData<D>(fullKey);
  }

  /// 清理相关缓存
  void _clearRelatedCache() {
    // 这里可以实现更精确的缓存清理逻辑
    // 例如只清理与当前组件相关的缓存

    debugPrint('🔧 ListMemoryOptimizerMixin - Cleared cache for ${T.toString()}');
  }
}
