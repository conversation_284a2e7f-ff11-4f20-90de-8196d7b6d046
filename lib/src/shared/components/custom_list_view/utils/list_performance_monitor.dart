import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// 🎯 列表性能监控工具
/// 
/// 功能特性：
/// - ✅ 监控渲染性能
/// - ✅ 内存使用监控
/// - ✅ 滚动性能分析
/// - ✅ 数据加载时间统计
/// - ✅ 性能报告生成
class ListPerformanceMonitor {
  static final ListPerformanceMonitor _instance = ListPerformanceMonitor._internal();
  factory ListPerformanceMonitor() => _instance;
  ListPerformanceMonitor._internal();

  // 性能数据
  final Map<String, PerformanceData> _performanceData = {};
  
  // 是否启用监控
  bool _isEnabled = kDebugMode;
  
  // 监控回调
  void Function(String listId, PerformanceReport report)? onPerformanceReport;

  /// 启用/禁用监控
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// 开始监控列表
  void startMonitoring(String listId) {
    if (!_isEnabled) return;
    
    _performanceData[listId] = PerformanceData(
      listId: listId,
      startTime: DateTime.now(),
    );
    
    debugPrint('🔍 ListPerformanceMonitor - Started monitoring: $listId');
  }

  /// 停止监控列表
  void stopMonitoring(String listId) {
    if (!_isEnabled) return;
    
    final data = _performanceData[listId];
    if (data != null) {
      data.endTime = DateTime.now();
      _generateReport(data);
      _performanceData.remove(listId);
    }
    
    debugPrint('🔍 ListPerformanceMonitor - Stopped monitoring: $listId');
  }

  /// 记录数据加载时间
  void recordDataLoadTime(String listId, Duration loadTime, int itemCount) {
    if (!_isEnabled) return;
    
    final data = _performanceData[listId];
    if (data != null) {
      data.dataLoadTimes.add(loadTime);
      data.itemCounts.add(itemCount);
      
      debugPrint('🔍 ListPerformanceMonitor - Data load: $listId, time: ${loadTime.inMilliseconds}ms, items: $itemCount');
    }
  }

  /// 记录渲染时间
  void recordRenderTime(String listId, Duration renderTime) {
    if (!_isEnabled) return;
    
    final data = _performanceData[listId];
    if (data != null) {
      data.renderTimes.add(renderTime);
      
      debugPrint('🔍 ListPerformanceMonitor - Render time: $listId, time: ${renderTime.inMilliseconds}ms');
    }
  }

  /// 记录滚动性能
  void recordScrollPerformance(String listId, double fps, int droppedFrames) {
    if (!_isEnabled) return;
    
    final data = _performanceData[listId];
    if (data != null) {
      data.scrollFps.add(fps);
      data.droppedFrames += droppedFrames;
      
      if (fps < 50) {
        debugPrint('⚠️ ListPerformanceMonitor - Low FPS detected: $listId, fps: $fps');
      }
    }
  }

  /// 记录内存使用
  void recordMemoryUsage(String listId, int memoryUsage) {
    if (!_isEnabled) return;
    
    final data = _performanceData[listId];
    if (data != null) {
      data.memoryUsages.add(memoryUsage);
      
      if (memoryUsage > 100 * 1024 * 1024) { // 100MB
        debugPrint('⚠️ ListPerformanceMonitor - High memory usage: $listId, memory: ${memoryUsage ~/ (1024 * 1024)}MB');
      }
    }
  }

  /// 生成性能报告
  void _generateReport(PerformanceData data) {
    final report = PerformanceReport(
      listId: data.listId,
      totalTime: data.endTime!.difference(data.startTime),
      averageDataLoadTime: _calculateAverage(data.dataLoadTimes),
      averageRenderTime: _calculateAverage(data.renderTimes),
      averageFps: _calculateAverageDouble(data.scrollFps),
      totalDroppedFrames: data.droppedFrames,
      peakMemoryUsage: data.memoryUsages.isNotEmpty ? data.memoryUsages.reduce((a, b) => a > b ? a : b) : 0,
      totalItemsLoaded: data.itemCounts.isNotEmpty ? data.itemCounts.reduce((a, b) => a + b) : 0,
    );
    
    debugPrint('📊 ListPerformanceMonitor - Performance Report for ${data.listId}:');
    debugPrint('   Total Time: ${report.totalTime.inMilliseconds}ms');
    debugPrint('   Avg Data Load: ${report.averageDataLoadTime.inMilliseconds}ms');
    debugPrint('   Avg Render: ${report.averageRenderTime.inMilliseconds}ms');
    debugPrint('   Avg FPS: ${report.averageFps.toStringAsFixed(1)}');
    debugPrint('   Dropped Frames: ${report.totalDroppedFrames}');
    debugPrint('   Peak Memory: ${report.peakMemoryUsage ~/ (1024 * 1024)}MB');
    debugPrint('   Total Items: ${report.totalItemsLoaded}');
    
    onPerformanceReport?.call(data.listId, report);
  }

  /// 计算平均值
  Duration _calculateAverage(List<Duration> durations) {
    if (durations.isEmpty) return Duration.zero;
    
    final totalMs = durations.fold<int>(0, (sum, duration) => sum + duration.inMilliseconds);
    return Duration(milliseconds: totalMs ~/ durations.length);
  }

  /// 计算平均值（double）
  double _calculateAverageDouble(List<double> values) {
    if (values.isEmpty) return 0.0;
    
    final total = values.fold<double>(0.0, (sum, value) => sum + value);
    return total / values.length;
  }

  /// 获取当前监控的列表
  List<String> get monitoringLists => _performanceData.keys.toList();

  /// 清除所有监控数据
  void clearAll() {
    _performanceData.clear();
    debugPrint('🔍 ListPerformanceMonitor - Cleared all monitoring data');
  }
}

/// 性能数据类
class PerformanceData {
  final String listId;
  final DateTime startTime;
  DateTime? endTime;
  
  final List<Duration> dataLoadTimes = [];
  final List<Duration> renderTimes = [];
  final List<double> scrollFps = [];
  final List<int> memoryUsages = [];
  final List<int> itemCounts = [];
  
  int droppedFrames = 0;

  PerformanceData({
    required this.listId,
    required this.startTime,
  });
}

/// 性能报告类
class PerformanceReport {
  final String listId;
  final Duration totalTime;
  final Duration averageDataLoadTime;
  final Duration averageRenderTime;
  final double averageFps;
  final int totalDroppedFrames;
  final int peakMemoryUsage;
  final int totalItemsLoaded;

  const PerformanceReport({
    required this.listId,
    required this.totalTime,
    required this.averageDataLoadTime,
    required this.averageRenderTime,
    required this.averageFps,
    required this.totalDroppedFrames,
    required this.peakMemoryUsage,
    required this.totalItemsLoaded,
  });

  /// 获取性能等级
  PerformanceLevel get performanceLevel {
    if (averageFps >= 55 && averageRenderTime.inMilliseconds <= 16 && peakMemoryUsage <= 50 * 1024 * 1024) {
      return PerformanceLevel.excellent;
    } else if (averageFps >= 45 && averageRenderTime.inMilliseconds <= 32 && peakMemoryUsage <= 100 * 1024 * 1024) {
      return PerformanceLevel.good;
    } else if (averageFps >= 30 && averageRenderTime.inMilliseconds <= 50 && peakMemoryUsage <= 200 * 1024 * 1024) {
      return PerformanceLevel.fair;
    } else {
      return PerformanceLevel.poor;
    }
  }

  /// 获取优化建议
  List<String> get optimizationSuggestions {
    final suggestions = <String>[];
    
    if (averageFps < 45) {
      suggestions.add('考虑禁用Item动画以提高滚动性能');
      suggestions.add('减少Item的复杂度和嵌套层级');
    }
    
    if (averageRenderTime.inMilliseconds > 32) {
      suggestions.add('优化Item的build方法，避免复杂计算');
      suggestions.add('使用const构造函数减少重建');
    }
    
    if (peakMemoryUsage > 100 * 1024 * 1024) {
      suggestions.add('使用图片缓存和压缩');
      suggestions.add('及时释放不需要的资源');
      suggestions.add('考虑使用分页加载减少内存占用');
    }
    
    if (averageDataLoadTime.inMilliseconds > 2000) {
      suggestions.add('优化网络请求和数据处理');
      suggestions.add('使用数据缓存减少重复请求');
    }
    
    if (totalDroppedFrames > 10) {
      suggestions.add('检查主线程是否有阻塞操作');
      suggestions.add('使用Isolate处理耗时计算');
    }
    
    return suggestions;
  }

  @override
  String toString() {
    return 'PerformanceReport('
        'listId: $listId, '
        'level: ${performanceLevel.name}, '
        'fps: ${averageFps.toStringAsFixed(1)}, '
        'renderTime: ${averageRenderTime.inMilliseconds}ms, '
        'memory: ${peakMemoryUsage ~/ (1024 * 1024)}MB'
        ')';
  }
}

/// 性能等级枚举
enum PerformanceLevel {
  excellent('优秀'),
  good('良好'),
  fair('一般'),
  poor('较差');

  const PerformanceLevel(this.displayName);
  final String displayName;
}

/// 性能监控Mixin
mixin ListPerformanceMonitorMixin<T extends StatefulWidget> on State<T> {
  late String _listId;
  late Stopwatch _renderStopwatch;

  void initPerformanceMonitor() {
    _listId = '${T.toString()}_$hashCode';
    _renderStopwatch = Stopwatch();

    ListPerformanceMonitor().startMonitoring(_listId);
  }

  Widget buildWithPerformanceMonitoring(BuildContext context, Widget Function(BuildContext) builder) {
    _renderStopwatch.start();
    final result = builder(context);
    _renderStopwatch.stop();

    ListPerformanceMonitor().recordRenderTime(_listId, _renderStopwatch.elapsed);
    _renderStopwatch.reset();

    return result;
  }

  void disposePerformanceMonitor() {
    ListPerformanceMonitor().stopMonitoring(_listId);
  }

  /// 记录数据加载时间
  void recordDataLoad(Duration loadTime, int itemCount) {
    ListPerformanceMonitor().recordDataLoadTime(_listId, loadTime, itemCount);
  }

  /// 记录滚动性能
  void recordScrollPerformance(double fps, int droppedFrames) {
    ListPerformanceMonitor().recordScrollPerformance(_listId, fps, droppedFrames);
  }
}
