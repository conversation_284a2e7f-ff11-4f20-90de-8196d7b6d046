import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_color.dart';

/// 多状态枚举
enum MultiViewState {
  loading,    // 加载中
  success,    // 成功/内容
  error,      // 错误
  empty,      // 空数据
  network,    // 网络错误
  custom,     // 自定义状态
}

/// 多状态控制器
class MultiStateController extends ChangeNotifier {
  MultiViewState _currentState;
  String? _errorMessage;
  String? _customMessage;
  bool _hasEverLoaded = false; // 标记是否曾经加载过

  MultiStateController(this._currentState);

  MultiViewState get currentState => _currentState;
  String? get errorMessage => _errorMessage;
  String? get customMessage => _customMessage;
  bool get hasEverLoaded => _hasEverLoaded;

  /// 显示加载状态
  void showLoading() {
    _currentState = MultiViewState.loading;
    _hasEverLoaded = true; // 标记已经开始加载
    notifyListeners();
  }

  /// 显示内容
  void showContent() {
    _currentState = MultiViewState.success;
    _hasEverLoaded = true;
    notifyListeners();
  }

  /// 显示错误
  void showError([String? message]) {
    _currentState = MultiViewState.error;
    _errorMessage = message;
    _hasEverLoaded = true;
    notifyListeners();
  }

  /// 显示空数据
  void showEmpty() {
    _currentState = MultiViewState.empty;
    _hasEverLoaded = true;
    notifyListeners();
  }
  
  /// 显示网络错误
  void showNetworkError() {
    _currentState = MultiViewState.network;
    notifyListeners();
  }
  
  /// 显示自定义状态
  void showCustom([String? message]) {
    _currentState = MultiViewState.custom;
    _customMessage = message;
    notifyListeners();
  }
  
  /// 重置到初始状态
  void reset() {
    _currentState = MultiViewState.loading;
    _errorMessage = null;
    _customMessage = null;
    _hasEverLoaded = false; // 重置加载标记
    notifyListeners();
  }

  /// 智能状态更新：避免在初始加载时显示空数据
  void updateStateIntelligently(MultiViewState newState, {String? message}) {
    // 如果从未加载过，且新状态是empty，则先显示loading
    if (!_hasEverLoaded && newState == MultiViewState.empty) {
      _currentState = MultiViewState.loading;
      _hasEverLoaded = true;
    } else {
      _currentState = newState;
      _hasEverLoaded = true;

      // 设置相应的消息
      if (newState == MultiViewState.error) {
        _errorMessage = message;
      } else if (newState == MultiViewState.custom) {
        _customMessage = message;
      }
    }
    notifyListeners();
  }
}

/// 多状态视图组件
class MultiStateView extends StatelessWidget {
  final MultiStateController controller;
  final Widget contentWidget;
  
  // 自定义状态Widget
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final Widget? emptyWidget;
  final Widget? networkWidget;
  final Widget? customWidget;
  
  // 动画配置
  final Duration animationDuration;
  final bool enableAnimation;
  
  // 回调函数
  final VoidCallback? onRetry;
  final VoidCallback? onRefresh;
  
  // 背景色配置
  final Color? backgroundColor;
  final bool transparentBackground;
  
  const MultiStateView({
    super.key,
    required this.controller,
    required this.contentWidget,
    this.loadingWidget,
    this.errorWidget,
    this.emptyWidget,
    this.networkWidget,
    this.customWidget,
    this.animationDuration = const Duration(milliseconds: 300),
    this.enableAnimation = true,
    this.onRetry,
    this.onRefresh,
    this.backgroundColor,
    this.transparentBackground = true,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        Widget currentWidget = _buildCurrentStateWidget(context);
        
        if (enableAnimation) {
          return AnimatedSwitcher(
            duration: animationDuration,
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            child: Container(
              key: ValueKey(controller.currentState),
              color: transparentBackground ? Colors.transparent : backgroundColor,
              child: currentWidget,
            ),
          );
        } else {
          return Container(
            color: transparentBackground ? Colors.transparent : backgroundColor,
            child: currentWidget,
          );
        }
      },
    );
  }
  
  Widget _buildCurrentStateWidget(BuildContext context) {
    // 智能状态判断：如果从未加载过且当前状态是empty，则显示loading
    if (!controller.hasEverLoaded && controller.currentState == MultiViewState.empty) {
      return loadingWidget ?? _buildDefaultLoading();
    }

    switch (controller.currentState) {
      case MultiViewState.loading:
        return loadingWidget ?? _buildDefault2Loading();
      case MultiViewState.success:
        return contentWidget;
      case MultiViewState.error:
        return errorWidget ?? _buildDefaultError();
      case MultiViewState.empty:
        return emptyWidget ?? _buildDefaultEmpty();
      case MultiViewState.network:
        return networkWidget ?? _buildDefaultNetwork();
      case MultiViewState.custom:
        return customWidget ?? _buildDefaultCustom();
    }
  }
  
  Widget _buildDefaultLoading() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildDefault2Loading() {
    return const Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircularProgressIndicator(color: AppColors.primary,strokeWidth: 3)
      ],
    );
  }
  
  Widget _buildDefaultError() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: AppColors.primary),
          const SizedBox(height: 16),
          Text(
            controller.errorMessage ?? '加载失败',
            style: const TextStyle(fontSize: 16),
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('重试'),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildDefaultEmpty() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            '暂无数据',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          if (onRefresh != null) ...[
            const SizedBox(height: 16),
            TextButton(
              onPressed: onRefresh,
              child: const Text('刷新'),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildDefaultNetwork() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.wifi_off, size: 64, color: Colors.orange),
          const SizedBox(height: 16),
          const Text(
            '网络连接失败',
            style: TextStyle(fontSize: 16),
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('重试'),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildDefaultCustom() {
    return Center(
      child: Text(
        controller.customMessage ?? '自定义状态',
        style: const TextStyle(fontSize: 16),
      ),
    );
  }
}
