import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_color.dart';
import '../../../features/resume/theme/resume_theme.dart';
import '../../utils/safe_area_manager.dart';

/// 通用AppBar
/// 参考joke_fun项目的简洁设计
AppBar commonAppBar({
  Color statusBarColor = Colors.transparent,
  Color? backgroundColor,
  bool iconDark = true,
  PreferredSizeWidget? bottom,
}) {
  return AppBar(
    elevation: 0,
    toolbarHeight: 0,
    scrolledUnderElevation: 0,
    systemOverlayStyle: SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: iconDark ? Brightness.dark : Brightness.light,
    ),
    backgroundColor: backgroundColor ?? Colors.white,
    bottom: bottom,
  );
}

/// 通用标题栏
PreferredSizeWidget? commonTitleBar({
  required BuildContext context,
  String leftIcon = 'ic_back',
  String title = '',
  String? rightIcon,
  String? rightText,
  Widget? rightWidget,
  GestureTapCallback? leftClick,
  GestureTapCallback? rightClick,
  Color? contentColor,
  Color? backgroundColor,
}) {
  Widget? right;
  if (rightText != null || rightIcon != null) {
    right = Container(
      width: 160.w,
      alignment: Alignment.centerRight,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: rightClick,
        child: (rightIcon != null)
            ? Icon(Icons.more_vert,
                size: 24.w, color: contentColor ?? AppColors.color_333333)
            : Text(
                rightText!,
                style: TextStyle(
                  color: contentColor ?? AppColors.color_333333,
                  fontSize: 16.sp,
                ),
              ),
      ),
    );
  } else {
    right = (rightWidget != null)
        ? GestureDetector(
            onTap: rightClick,
            child: Container(
              width: 160.w,
              alignment: Alignment.centerRight,
              child: rightWidget,
            ),
          )
        : Container(width: 160.w);
  }

  return PreferredSize(
    preferredSize: Size(double.infinity, 56.h),
    child: Container(
      height: 56.h,
      color: backgroundColor ?? Colors.transparent,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(width: 16.w),
          Container(
            alignment: Alignment.centerLeft,
            width: 160.w,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: leftClick ?? () => Navigator.of(context).pop(),
              child: Icon(
                Icons.arrow_back_ios,
                size: 20.w,
                color: contentColor ?? AppColors.color_333333,
              ),
            ),
          ),
          Expanded(
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                  color: contentColor ?? AppColors.color_333333,
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          right,
          SizedBox(width: 16.w),
        ],
      ),
    ),
  );
}

/// 更新状态栏颜色
void updateStatusBarColor(Color color, bool iconDark) {
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    statusBarColor: color,
    statusBarIconBrightness: iconDark ? Brightness.dark : Brightness.light,
  ));
}

/// 构建应用栏
Widget customTitleBar(BuildContext context, String title,
    {VoidCallback? leftCallback}) {
  // // 使用缓存的安全区域值，避免MediaQuery重复调用
  final topPadding = SafeAreaManager.instance.isInitialized
      ? SafeAreaManager.instance.topPadding
      : MediaQuery.of(context).padding.top;

  return Container(
    // AppBar 的总高度 = 内边距 + 内容高度
    padding: EdgeInsets.fromLTRB(16.0, topPadding + 8.0, 16.0, 8.0),
    color: Colors.white,
    child: Stack(
      // 使用 Stack 来堆叠组件
      // alignment: Alignment.center 使子组件默认居中对齐
      alignment: Alignment.center,
      children: [
        // 1. 居中的标题
        // 它会自然地在 Stack 的中心位置
        Text(
          title,
          style: TextStyle(
            fontSize: 18.w,
            fontWeight: FontWeight.w500,
            // 从主题中获取文字颜色
            color: Theme.of(context).textTheme.titleLarge?.color,
          ),
        ),

        // 2. 左侧的返回按钮
        // 使用 Align 组件将其精确放置在左侧
        Align(
          alignment: Alignment.centerLeft,
          child: GestureDetector(
            onTap: () {
              // 如果提供了回调函数，则执行它
              // 在实际应用中，这里通常是 Navigator.of(context).pop();
              if (leftCallback != null) {
                leftCallback!();
              } else {
                // 默认行为：返回上一页
                if (Navigator.canPop(context)) {
                  Navigator.of(context).pop();
                }
              }
            },
            // 给点击区域增加一些内边距，使其更容易点击
            child: Container(
              // 为了视觉效果，背景设为透明
              color: Colors.transparent,
              padding: const EdgeInsets.all(8.0),
              child: const Icon(
                Icons.arrow_back_ios_new,
                size: 18.0,
                color: Color(0xFF333333),
              ),
            ),
          ),
        ),
      ],
    ),
  );
}
