import 'package:flutter/foundation.dart';

/// 🔥 表单验证Mixin - 为Logic类提供统一的验证能力
/// 
/// 参考优秀GitHub项目的设计模式：
/// - 职责单一：只负责验证状态管理
/// - 易于使用：简单的API设计
/// - 高度复用：任何Logic类都可以混入
/// - 类型安全：强类型约束
mixin FormValidationMixin on ChangeNotifier {
  
  /// 字段错误信息存储
  final Map<String, String> _fieldErrors = {};
  
  /// 必填字段配置 - 子类可以重写
  Map<String, String> get requiredFields => {};
  
  // ==================== 公共API ====================
  
  /// 🔥 获取所有字段错误
  Map<String, String> get fieldErrors => Map.unmodifiable(_fieldErrors);
  
  /// 🔥 设置字段错误
  void setFieldError(String? error, String fieldName) {
    if (error != null && error.isNotEmpty) {
      _fieldErrors[fieldName] = error;
    } else {
      _fieldErrors.remove(fieldName);
    }
    notifyListeners();
  }
  
  /// 🔥 获取字段错误信息
  String? getFieldError(String fieldName) {
    return _fieldErrors[fieldName];
  }
  
  /// 🔥 检查字段是否有错误
  bool hasFieldError(String fieldName) {
    return _fieldErrors.containsKey(fieldName);
  }
  
  /// 🔥 清除字段错误
  void clearFieldError(String fieldName) {
    _fieldErrors.remove(fieldName);
    notifyListeners();
  }
  
  /// 🔥 清除所有错误
  void clearAllErrors() {
    _fieldErrors.clear();
    notifyListeners();
  }
  
  /// 🔥 批量设置错误
  void setFieldErrors(Map<String, String> errors) {
    _fieldErrors.clear();
    _fieldErrors.addAll(errors);
    notifyListeners();
  }
  
  /// 🔥 验证必填字段
  bool validateRequiredFields(Map<String, dynamic> data) {
    bool isValid = true;
    _fieldErrors.clear();
    
    for (final entry in requiredFields.entries) {
      final fieldName = entry.key;
      final fieldLabel = entry.value;
      final value = data[fieldName];
      
      if (value == null || value.toString().trim().isEmpty) {
        _fieldErrors[fieldName] = '请填写$fieldLabel';
        isValid = false;
      }
    }
    
    notifyListeners();
    return isValid;
  }
  
  /// 🔥 获取错误数量
  int get errorCount => _fieldErrors.length;
  
  /// 🔥 是否有任何错误
  bool get hasAnyError => _fieldErrors.isNotEmpty;
  
  /// 🔥 是否表单有效（无错误）
  bool get isFormValid => _fieldErrors.isEmpty;
  
  /// 🔥 获取错误摘要信息
  String get errorSummary {
    if (_fieldErrors.isEmpty) return '';
    return '请检查并修正 ${_fieldErrors.length} 个字段的错误';
  }
  
  /// 🔥 获取所有错误信息列表
  List<String> get allErrorMessages => _fieldErrors.values.toList();
  
  /// 🔥 获取第一个错误信息
  String? get firstError => _fieldErrors.values.isNotEmpty 
      ? _fieldErrors.values.first 
      : null;
}

/// 🔥 表单字段配置类 - 用于定义字段验证规则
class FormFieldConfig {
  final String fieldName;
  final String label;
  final bool isRequired;
  final String? requiredMessage;
  
  const FormFieldConfig({
    required this.fieldName,
    required this.label,
    this.isRequired = false,
    this.requiredMessage,
  });
  
  /// 获取必填错误信息
  String get requiredErrorMessage => 
      requiredMessage ?? '请填写$label';
}

/// 🔥 表单验证辅助类 - 提供常用的验证方法
class FormValidationHelper {
  
  /// 🔥 从字段配置列表生成必填字段Map
  static Map<String, String> generateRequiredFields(
    List<FormFieldConfig> configs
  ) {
    return Map.fromEntries(
      configs
          .where((config) => config.isRequired)
          .map((config) => MapEntry(config.fieldName, config.label))
    );
  }
  
  /// 🔥 验证单个字段是否为空
  static String? validateRequired(dynamic value, String fieldLabel) {
    if (value == null || value.toString().trim().isEmpty) {
      return '请填写$fieldLabel';
    }
    return null;
  }
  
  /// 🔥 验证字段长度
  static String? validateLength(
    dynamic value, 
    String fieldLabel, 
    {int? minLength, int? maxLength}
  ) {
    if (value == null) return null;
    
    final stringValue = value.toString();
    
    if (minLength != null && stringValue.length < minLength) {
      return '$fieldLabel长度不能少于$minLength位';
    }
    
    if (maxLength != null && stringValue.length > maxLength) {
      return '$fieldLabel长度不能超过$maxLength位';
    }
    
    return null;
  }
  
  /// 🔥 组合多个验证器
  static String? combineValidators(
    dynamic value,
    List<String? Function(dynamic)> validators
  ) {
    for (final validator in validators) {
      final error = validator(value);
      if (error != null) return error;
    }
    return null;
  }
}

/// 🔥 常用字段配置预设
class CommonFieldConfigs {
  
  /// 基本信息字段配置
  static const List<FormFieldConfig> baseInfoFields = [
    FormFieldConfig(fieldName: 'name', label: '姓名', isRequired: true),
    FormFieldConfig(fieldName: 'phone', label: '手机号', isRequired: true),
    FormFieldConfig(fieldName: 'email', label: '邮箱', isRequired: false),
    FormFieldConfig(fieldName: 'genderCode', label: '性别', isRequired: true),
    FormFieldConfig(fieldName: 'birthday', label: '生日', isRequired: true),
    FormFieldConfig(fieldName: 'educationCode', label: '学历', isRequired: true),
    FormFieldConfig(fieldName: 'workingAgeCode', label: '工龄', isRequired: true),
    FormFieldConfig(fieldName: 'liveAreaName', label: '居住地', isRequired: true),
  ];
  
  /// 联系信息字段配置
  static const List<FormFieldConfig> contactFields = [
    FormFieldConfig(fieldName: 'phone', label: '手机号', isRequired: true),
    FormFieldConfig(fieldName: 'email', label: '邮箱', isRequired: false),
    FormFieldConfig(fieldName: 'wechat', label: '微信号', isRequired: false),
    FormFieldConfig(fieldName: 'qq', label: 'QQ号', isRequired: false),
  ];
}
