import 'package:flutter/foundation.dart';
import '../models/config_models.dart';
import '../parsers/xml_config_parser.dart';
import 'config_storage.dart';
import 'config_repository.dart';

/// 配置服务主入口
/// 协调网络请求、本地存储、版本控制等功能
class ConfigService {
  final ConfigStorage _storage;
  final ConfigRepository _repository;
  
  ConfigConstant? _cachedConfig;
  bool _isInitialized = false;
  bool _isInitializing = false;

  ConfigService({
    ConfigStorage? storage,
    ConfigRepository? repository,
  }) : _storage = storage ?? ConfigStorage(),
        _repository = repository ?? ConfigRepository();

  /// 初始化配置（应用启动时调用）
  Future<void> initializeConfig() async {
    if (_isInitialized || _isInitializing) {
      return;
    }

    _isInitializing = true;
    
    try {
      debugPrint('开始初始化配置服务');
      
      // 1. 尝试从本地加载配置
      final localConfig = await _loadLocalConfig();
      
      if (localConfig != null) {
        // 有本地缓存，先使用本地数据
        _cachedConfig = localConfig;
        _isInitialized = true;
        debugPrint('使用本地缓存配置，版本: ${localConfig.version}');
        
        // 后台检查更新（不阻塞初始化）
        _checkUpdateInBackground();
      } else {
        // 无本地缓存，使用默认配置并后台获取
        debugPrint('无本地缓存，使用默认配置');
        await _useDefaultConfig();

        // 后台获取真实配置（不阻塞初始化）
        _fetchConfigInBackground();
      }
      
      debugPrint('配置服务初始化完成');
    } catch (e) {
      debugPrint('配置初始化失败: $e');
      // 降级处理：使用默认配置
      await _useDefaultConfig();
    } finally {
      _isInitializing = false;
    }
  }

  /// 检查并更新配置
  Future<bool> checkAndUpdateConfig() async {
    try {
      debugPrint('检查配置更新');
      
      // 检查网络连接
      final isConnected = await _repository.testConnection();
      if (!isConnected) {
        debugPrint('网络不可用，跳过更新检查');
        return false;
      }
      
      // 获取本地更新时间
      final lastUpdate = await _storage.getLastUpdateTime();
      
      // 检查是否需要更新
      final hasUpdate = await _repository.hasRemoteUpdate(
        lastLocalUpdate: lastUpdate,
      );
      
      if (hasUpdate) {
        debugPrint('发现配置更新，开始下载');
        return await _fetchAndSaveConfig();
      } else {
        debugPrint('配置已是最新版本');
        return false;
      }
    } catch (e) {
      debugPrint('检查配置更新失败: $e');
      return false;
    }
  }

  /// 强制刷新配置
  Future<bool> forceRefreshConfig() async {
    try {
      debugPrint('强制刷新配置');
      return await _fetchAndSaveConfig();
    } catch (e) {
      debugPrint('强制刷新配置失败: $e');
      return false;
    }
  }

  /// 获取当前配置
  Future<ConfigConstant?> getCurrentConfig() async {
    await _ensureInitialized();
    return _cachedConfig;
  }

  /// 清除所有配置数据
  Future<bool> clearAllConfig() async {
    try {
      debugPrint('清除所有配置数据');
      
      final success = await _storage.clearAll();
      if (success) {
        _cachedConfig = null;
        _isInitialized = false;
      }
      
      return success;
    } catch (e) {
      debugPrint('清除配置数据失败: $e');
      return false;
    }
  }

  /// 获取配置状态信息
  Future<Map<String, dynamic>> getConfigStatus() async {
    try {
      final storageInfo = await _storage.getStorageInfo();
      final networkInfo = await _repository.getNetworkInfo();
      
      return {
        'isInitialized': _isInitialized,
        'isInitializing': _isInitializing,
        'hasCachedConfig': _cachedConfig != null,
        'cachedVersion': _cachedConfig?.version,
        'storage': storageInfo,
        'network': networkInfo,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('获取配置状态失败: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  // 私有方法

  /// 确保已初始化
  Future<void> _ensureInitialized() async {
    if (!_isInitialized && !_isInitializing) {
      await initializeConfig();
    }
    
    // 等待初始化完成
    while (_isInitializing) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  /// 从本地加载配置
  Future<ConfigConstant?> _loadLocalConfig() async {
    try {
      // 优先从解析后的数据加载
      final configData = await _storage.getConfigData();
      if (configData != null) {
        debugPrint('从本地加载解析后的配置数据');
        return configData;
      }
      
      // 如果没有解析后的数据，尝试从XML加载
      final xmlContent = await _storage.getXmlContent();
      if (xmlContent != null && xmlContent.isNotEmpty) {
        debugPrint('从本地XML解析配置数据');
        final config = XmlConfigParser.parseXmlString(xmlContent);
        
        // 保存解析后的数据以便下次快速加载
        await _storage.saveConfigData(config);
        
        return config;
      }
      
      return null;
    } catch (e) {
      debugPrint('从本地加载配置失败: $e');
      return null;
    }
  }

  /// 获取并保存配置
  Future<bool> _fetchAndSaveConfig() async {
    try {
      // 获取远程XML
      final xmlContent = await _repository.fetchRemoteConfig();
      
      // 验证XML格式
      if (!XmlConfigParser.validateXmlFormat(xmlContent)) {
        throw Exception('XML格式验证失败');
      }
      
      // 解析XML
      final config = XmlConfigParser.parseXmlString(xmlContent);
      
      // 保存到本地
      final saveXmlSuccess = await _storage.saveXmlContent(xmlContent);
      final saveVersionSuccess = await _storage.saveVersion(config.version);
      final saveDataSuccess = await _storage.saveConfigData(config);
      
      if (saveXmlSuccess && saveVersionSuccess && saveDataSuccess) {
        _cachedConfig = config;
        _isInitialized = true;
        debugPrint('配置更新成功，版本: ${config.version}');
        return true;
      } else {
        throw Exception('保存配置到本地失败');
      }
    } catch (e) {
      debugPrint('获取并保存配置失败: $e');
      return false;
    }
  }

  /// 后台检查更新
  void _checkUpdateInBackground() {
    Future.delayed(const Duration(seconds: 2), () async {
      try {
        await checkAndUpdateConfig();
      } catch (e) {
        debugPrint('后台更新检查失败: $e');
      }
    });
  }

  /// 后台获取配置（用于首次启动时无本地缓存的情况）
  void _fetchConfigInBackground() {
    Future.delayed(const Duration(seconds: 1), () async {
      try {
        debugPrint('后台获取配置');
        await _fetchAndSaveConfig();
      } catch (e) {
        debugPrint('后台获取配置失败: $e');
      }
    });
  }

  /// 使用默认配置
  Future<void> _useDefaultConfig() async {
    try {
      debugPrint('使用默认配置');
      
      final defaultConfig = XmlConfigParser.createDefaultConfig();
      
      // 保存默认配置
      await _storage.saveConfigData(defaultConfig);
      await _storage.saveVersion(defaultConfig.version);
      
      _cachedConfig = defaultConfig;
      _isInitialized = true;
      
      debugPrint('默认配置加载完成');
    } catch (e) {
      debugPrint('加载默认配置失败: $e');
      _isInitialized = true; // 即使失败也标记为已初始化，避免无限循环
    }
  }

  /// 获取配置组数量
  int get groupCount => _cachedConfig?.groups.length ?? 0;

  /// 获取配置版本
  String get version => _cachedConfig?.version ?? '0.0.0';

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 是否正在初始化
  bool get isInitializing => _isInitializing;
}
