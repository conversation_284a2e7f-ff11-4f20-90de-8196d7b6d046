import 'package:equatable/equatable.dart';

/// 配置项节点
/// 对应XML中的Node元素
class ConfigItem extends Equatable {
  /// 键值
  final String key;
  
  /// 显示值
  final String value;
  
  /// 缩写（可选）
  final String? abbr;

  const ConfigItem({
    required this.key,
    required this.value,
    this.abbr,
  });

  /// 从Map创建ConfigItem
  factory ConfigItem.fromMap(Map<String, dynamic> map) {
    return ConfigItem(
      key: map['key'] as String,
      value: map['value'] as String,
      abbr: map['abbr'] as String?,
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'key': key,
      'value': value,
      if (abbr != null) 'abbr': abbr,
    };
  }

  /// 复制并修改
  ConfigItem copyWith({
    String? key,
    String? value,
    String? abbr,
  }) {
    return ConfigItem(
      key: key ?? this.key,
      value: value ?? this.value,
      abbr: abbr ?? this.abbr,
    );
  }

  @override
  List<Object?> get props => [key, value, abbr];

  @override
  String toString() => 'ConfigItem(key: $key, value: $value, abbr: $abbr)';
}

/// 配置组
/// 对应XML中的Item元素
class ConfigGroup extends Equatable {
  /// 组名
  final String name;
  
  /// 配置项列表
  final List<ConfigItem> items;

  const ConfigGroup({
    required this.name,
    required this.items,
  });

  /// 从Map创建ConfigGroup
  factory ConfigGroup.fromMap(Map<String, dynamic> map) {
    return ConfigGroup(
      name: map['name'] as String,
      items: (map['items'] as List<dynamic>)
          .map((item) => ConfigItem.fromMap(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'items': items.map((item) => item.toMap()).toList(),
    };
  }

  /// 通过key获取ConfigItem
  ConfigItem? getItemByKey(String key) {
    try {
      return items.firstWhere((item) => item.key == key);
    } catch (e) {
      return null;
    }
  }

  /// 通过value获取ConfigItem
  ConfigItem? getItemByValue(String value) {
    try {
      return items.firstWhere((item) => item.value == value);
    } catch (e) {
      return null;
    }
  }

  /// 通过key获取value
  String? getValueByKey(String key) {
    return getItemByKey(key)?.value;
  }

  /// 通过value获取key
  String? getKeyByValue(String value) {
    return getItemByValue(value)?.key;
  }

  /// 获取所有key列表
  List<String> get keys => items.map((item) => item.key).toList();

  /// 获取所有value列表
  List<String> get values => items.map((item) => item.value).toList();

  /// 复制并修改
  ConfigGroup copyWith({
    String? name,
    List<ConfigItem>? items,
  }) {
    return ConfigGroup(
      name: name ?? this.name,
      items: items ?? this.items,
    );
  }

  @override
  List<Object?> get props => [name, items];

  @override
  String toString() => 'ConfigGroup(name: $name, items: ${items.length})';
}

/// 配置常量
/// 对应XML根元素Constant
class ConfigConstant extends Equatable {
  /// 版本号
  final String version;
  
  /// 配置组列表
  final List<ConfigGroup> groups;

  const ConfigConstant({
    required this.version,
    required this.groups,
  });

  /// 从Map创建ConfigConstant
  factory ConfigConstant.fromMap(Map<String, dynamic> map) {
    return ConfigConstant(
      version: map['version'] as String,
      groups: (map['groups'] as List<dynamic>)
          .map((group) => ConfigGroup.fromMap(group as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'version': version,
      'groups': groups.map((group) => group.toMap()).toList(),
    };
  }

  /// 通过组名获取ConfigGroup
  ConfigGroup? getGroupByName(String name) {
    try {
      return groups.firstWhere((group) => group.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 通过组名和key获取value
  String? getValueByKey(String groupName, String key) {
    return getGroupByName(groupName)?.getValueByKey(key);
  }

  /// 通过组名和value获取key
  String? getKeyByValue(String groupName, String value) {
    return getGroupByName(groupName)?.getKeyByValue(value);
  }

  /// 获取所有组名列表
  List<String> get groupNames => groups.map((group) => group.name).toList();

  /// 复制并修改
  ConfigConstant copyWith({
    String? version,
    List<ConfigGroup>? groups,
  }) {
    return ConfigConstant(
      version: version ?? this.version,
      groups: groups ?? this.groups,
    );
  }

  @override
  List<Object?> get props => [version, groups];

  @override
  String toString() => 'ConfigConstant(version: $version, groups: ${groups.length})';
}

/// 配置常量名称
class ConfigNames {
  static const String gender = 'Gender';
  static const String welfare = 'Welfare';
  static const String jobNature = 'JobNature';
  
  /// 所有配置名称列表
  static const List<String> all = [
    gender,
    welfare,
    jobNature,
  ];
}
