import 'package:xml/xml.dart';
import '../models/config_models.dart';

/// XML配置解析器
/// 负责解析远程XML配置文件
class XmlConfigParser {
  /// 解析XML字符串为ConfigConstant对象
  static ConfigConstant parseXmlString(String xmlContent) {
    try {
      // 解析XML文档
      final document = XmlDocument.parse(xmlContent);
      
      // 获取根元素Constant
      final constantElement = document.rootElement;
      
      if (constantElement.name.local != 'Constant') {
        throw FormatException('XML根元素必须是Constant');
      }
      
      // 获取版本号
      final version = constantElement.getAttribute('version') ?? '';
      
      // 解析所有Item元素
      final groups = <ConfigGroup>[];
      
      for (final itemElement in constantElement.findElements('Item')) {
        final group = _parseItemElement(itemElement);
        if (group != null) {
          groups.add(group);
        }
      }
      
      return ConfigConstant(
        version: version,
        groups: groups,
      );
    } catch (e) {
      throw FormatException('XML解析失败: $e');
    }
  }
  
  /// 解析Item元素为ConfigGroup
  static ConfigGroup? _parseItemElement(XmlElement itemElement) {
    try {
      // 获取Item的name属性
      final name = itemElement.getAttribute('name');
      if (name == null || name.isEmpty) {
        return null;
      }
      
      // 解析所有Node元素
      final items = <ConfigItem>[];
      
      for (final nodeElement in itemElement.findElements('Node')) {
        final item = _parseNodeElement(nodeElement);
        if (item != null) {
          items.add(item);
        }
      }
      
      return ConfigGroup(
        name: name,
        items: items,
      );
    } catch (e) {
      return null;
    }
  }
  
  /// 解析Node元素为ConfigItem
  static ConfigItem? _parseNodeElement(XmlElement nodeElement) {
    try {
      // 获取Node的属性
      final key = nodeElement.getAttribute('key');
      final value = nodeElement.getAttribute('value');
      final abbr = nodeElement.getAttribute('abbr');
      
      if (key == null || key.isEmpty || value == null || value.isEmpty) {
        return null;
      }
      
      return ConfigItem(
        key: key,
        value: value,
        abbr: abbr,
      );
    } catch (e) {
      return null;
    }
  }
  
  /// 验证XML格式是否正确
  static bool validateXmlFormat(String xmlContent) {
    try {
      final document = XmlDocument.parse(xmlContent);
      final constantElement = document.rootElement;
      
      // 检查根元素
      if (constantElement.name.local != 'Constant') {
        return false;
      }
      
      // 检查是否有version属性
      if (constantElement.getAttribute('version') == null) {
        return false;
      }
      
      // 检查是否有Item元素
      final items = constantElement.findElements('Item');
      if (items.isEmpty) {
        return false;
      }
      
      // 检查每个Item是否有name属性和Node子元素
      for (final item in items) {
        if (item.getAttribute('name') == null) {
          return false;
        }
        
        final nodes = item.findElements('Node');
        if (nodes.isEmpty) {
          return false;
        }
        
        // 检查每个Node是否有必要的属性
        for (final node in nodes) {
          if (node.getAttribute('key') == null || 
              node.getAttribute('value') == null) {
            return false;
          }
        }
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// 提取XML版本号
  static String? extractVersion(String xmlContent) {
    try {
      final document = XmlDocument.parse(xmlContent);
      final constantElement = document.rootElement;
      return constantElement.getAttribute('version');
    } catch (e) {
      return null;
    }
  }
  
  /// 获取XML中的所有组名
  static List<String> extractGroupNames(String xmlContent) {
    try {
      final document = XmlDocument.parse(xmlContent);
      final constantElement = document.rootElement;
      
      final groupNames = <String>[];
      for (final itemElement in constantElement.findElements('Item')) {
        final name = itemElement.getAttribute('name');
        if (name != null && name.isNotEmpty) {
          groupNames.add(name);
        }
      }
      
      return groupNames;
    } catch (e) {
      return [];
    }
  }
  
  /// 创建默认配置（用于降级处理）
  static ConfigConstant createDefaultConfig() {
    return const ConfigConstant(
      version: '0.0.0',
      groups: [
        ConfigGroup(
          name: 'Gender',
          items: [
            ConfigItem(key: 'A01', value: '男'),
            ConfigItem(key: 'A02', value: '女'),
          ],
        ),
        ConfigGroup(
          name: 'JobNature',
          items: [
            ConfigItem(key: 'A01', value: '全职'),
            ConfigItem(key: 'A02', value: '兼职'),
            ConfigItem(key: 'A03', value: '临时工'),
            ConfigItem(key: 'A04', value: '小时工'),
          ],
        ),
      ],
    );
  }
}
