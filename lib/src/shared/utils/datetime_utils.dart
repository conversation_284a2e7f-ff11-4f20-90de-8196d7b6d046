import 'package:flutter/foundation.dart';

/// 🔥 时间处理工具类 - 参考优秀GitHub项目设计
/// 
/// 参考项目：
/// - flutter_datetime_picker: 时间选择器设计模式
/// - intl: 国际化日期格式处理
/// - date_format: 多格式日期解析
/// 
/// 功能特性：
/// - 多格式字符串转日期
/// - 灵活的日期格式化输出
/// - 精确的年龄计算
/// - 完善的日期验证
/// - 工作年龄范围计算
/// - 类型安全和错误处理
class DateTimeUtils {
  
  /// 🔥 支持的日期格式列表
  static const List<String> _supportedFormats = [
    'yyyy-MM-dd',           // 2024-01-15
    'yyyy-MM',              // 2024-01
    'yyyy年MM月dd日',        // 2024年01月15日
    'yyyy年MM月',            // 2024年01月
    'yyyy/MM/dd',           // 2024/01/15
    'yyyy/MM',              // 2024/01
    'dd/MM/yyyy',           // 15/01/2024
    'MM/dd/yyyy',           // 01/15/2024
    'dd-MM-yyyy',           // 15-01-2024
    'MM-dd-yyyy',           // 01-15-2024
    'yyyyMMdd',             // 20240115
    'yyyyMM',               // 202401
  ];

  /// 🔥 中国法定工作年龄常量
  static const int _minWorkAge = 16;  // 最小工作年龄
  static const int _maxWorkAge = 65;  // 最大工作年龄（退休年龄）

  // ==================== 字符串转日期 ====================

  /// 🔥 智能解析日期字符串 - 自动识别格式
  static DateTime? parseDate(String? dateString) {
    if (dateString == null || dateString.trim().isEmpty) return null;
    
    final cleanString = dateString.trim();
    
    // 尝试各种格式解析
    for (final format in _supportedFormats) {
      try {
        final result = _parseWithFormat(cleanString, format);
        if (result != null) return result;
      } catch (e) {
        // 继续尝试下一个格式
        continue;
      }
    }
    
    // 如果所有格式都失败，尝试DateTime.parse
    try {
      return DateTime.parse(cleanString);
    } catch (e) {
      debugPrint('DateTimeUtils: 无法解析日期字符串: $dateString');
      return null;
    }
  }

  /// 🔥 使用指定格式解析日期
  static DateTime? parseWithFormat(String dateString, String format) {
    try {
      return _parseWithFormat(dateString, format);
    } catch (e) {
      debugPrint('DateTimeUtils: 格式解析失败 - $dateString with $format: $e');
      return null;
    }
  }

  /// 内部格式解析方法
  static DateTime? _parseWithFormat(String dateString, String format) {
    // 简化的格式解析实现
    switch (format) {
      case 'yyyy-MM-dd':
        final parts = dateString.split('-');
        if (parts.length == 3) {
          return DateTime(int.parse(parts[0]), int.parse(parts[1]), int.parse(parts[2]));
        }
        break;
      case 'yyyy-MM':
        final parts = dateString.split('-');
        if (parts.length == 2) {
          return DateTime(int.parse(parts[0]), int.parse(parts[1]), 1);
        }
        break;
      case 'yyyy年MM月dd日':
        final regex = RegExp(r'(\d{4})年(\d{1,2})月(\d{1,2})日');
        final match = regex.firstMatch(dateString);
        if (match != null) {
          return DateTime(
            int.parse(match.group(1)!),
            int.parse(match.group(2)!),
            int.parse(match.group(3)!),
          );
        }
        break;
      case 'yyyy年MM月':
        final regex = RegExp(r'(\d{4})年(\d{1,2})月');
        final match = regex.firstMatch(dateString);
        if (match != null) {
          return DateTime(
            int.parse(match.group(1)!),
            int.parse(match.group(2)!),
            1,
          );
        }
        break;
      case 'yyyy/MM/dd':
        final parts = dateString.split('/');
        if (parts.length == 3) {
          return DateTime(int.parse(parts[0]), int.parse(parts[1]), int.parse(parts[2]));
        }
        break;
      case 'yyyy/MM':
        final parts = dateString.split('/');
        if (parts.length == 2) {
          return DateTime(int.parse(parts[0]), int.parse(parts[1]), 1);
        }
        break;
      case 'yyyyMMdd':
        if (dateString.length == 8) {
          return DateTime(
            int.parse(dateString.substring(0, 4)),
            int.parse(dateString.substring(4, 6)),
            int.parse(dateString.substring(6, 8)),
          );
        }
        break;
      case 'yyyyMM':
        if (dateString.length == 6) {
          return DateTime(
            int.parse(dateString.substring(0, 4)),
            int.parse(dateString.substring(4, 6)),
            1,
          );
        }
        break;
    }
    return null;
  }

  // ==================== 日期格式化 ====================

  /// 🔥 格式化为年月日格式
  static String formatToYearMonthDay(DateTime? date) {
    if (date == null) return '';
    return '${date.year}年${date.month.toString().padLeft(2, '0')}月${date.day.toString().padLeft(2, '0')}日';
  }

  /// 🔥 格式化为年月格式
  static String formatToYearMonth(DateTime? date) {
    if (date == null) return '';
    return '${date.year}年${date.month.toString().padLeft(2, '0')}月';
  }

  /// 🔥 格式化为标准日期格式
  static String formatToStandard(DateTime? date) {
    if (date == null) return '';
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 🔥 自定义格式化
  static String formatCustom(DateTime? date, String format) {
    if (date == null) return '';
    
    String result = format;
    result = result.replaceAll('yyyy', date.year.toString());
    result = result.replaceAll('MM', date.month.toString().padLeft(2, '0'));
    result = result.replaceAll('dd', date.day.toString().padLeft(2, '0'));
    result = result.replaceAll('M', date.month.toString());
    result = result.replaceAll('d', date.day.toString());
    
    return result;
  }

  // ==================== 年龄计算 ====================

  /// 🔥 根据生日计算当前年龄
  static int calculateAge(DateTime? birthDate) {
    if (birthDate == null) return 0;
    
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    
    // 如果还没到生日，年龄减1
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    
    return age < 0 ? 0 : age;
  }

  /// 🔥 根据年龄计算大概的出生年份
  static int calculateBirthYear(int age) {
    return DateTime.now().year - age;
  }

  /// 🔥 检查是否为工作年龄
  static bool isWorkingAge(DateTime? birthDate) {
    if (birthDate == null) return false;
    final age = calculateAge(birthDate);
    return age >= _minWorkAge && age <= _maxWorkAge;
  }

  // ==================== 日期验证 ====================

  /// 🔥 验证日期字符串的有效性
  static DateValidationResult validateDate(String? dateString) {
    if (dateString == null || dateString.trim().isEmpty) {
      return DateValidationResult(false, '日期不能为空');
    }
    
    final date = parseDate(dateString);
    if (date == null) {
      return DateValidationResult(false, '日期格式不正确');
    }
    
    // 检查日期是否合理（不能是未来日期）
    if (date.isAfter(DateTime.now())) {
      return DateValidationResult(false, '日期不能是未来时间');
    }
    
    // 检查日期是否过于久远（超过150年）
    final minDate = DateTime.now().subtract(const Duration(days: 365 * 150));
    if (date.isBefore(minDate)) {
      return DateValidationResult(false, '日期不能超过150年前');
    }
    
    return DateValidationResult(true, null);
  }

  /// 🔥 验证生日日期
  static DateValidationResult validateBirthDate(String? dateString) {
    final basicResult = validateDate(dateString);
    if (!basicResult.isValid) return basicResult;
    
    final date = parseDate(dateString)!;
    final age = calculateAge(date);
    
    // 检查年龄是否合理（0-150岁）
    if (age < 0) {
      return DateValidationResult(false, '生日不能是未来时间');
    }
    
    if (age > 150) {
      return DateValidationResult(false, '年龄不能超过150岁');
    }
    
    return DateValidationResult(true, null);
  }

  // ==================== 工作年龄范围计算 ====================

  /// 🔥 获取法定工作年龄对应的出生年份范围
  static DateRange getWorkingAgeBirthYearRange() {
    final currentYear = DateTime.now().year;
    return DateRange(
      start: currentYear - _maxWorkAge,  // 最早出生年份
      end: currentYear - _minWorkAge,    // 最晚出生年份
    );
  }

  /// 🔥 获取工作年龄对应的出生日期范围
  static DateTimeRange getWorkingAgeBirthDateRange() {
    final now = DateTime.now();
    return DateTimeRange(
      start: DateTime(now.year - _maxWorkAge, 1, 1),
      end: DateTime(now.year - _minWorkAge, 12, 31),
    );
  }

  /// 🔥 自定义年龄范围对应的出生年份范围
  static DateRange getBirthYearRange({int minAge = 0, int maxAge = 100}) {
    final currentYear = DateTime.now().year;
    return DateRange(
      start: currentYear - maxAge,
      end: currentYear - minAge,
    );
  }

  // ==================== 工具方法 ====================

  /// 🔥 检查是否为闰年
  static bool isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }

  /// 🔥 获取月份的天数
  static int getDaysInMonth(int year, int month) {
    if (month == 2) {
      return isLeapYear(year) ? 29 : 28;
    } else if ([4, 6, 9, 11].contains(month)) {
      return 30;
    } else {
      return 31;
    }
  }

  /// 🔥 获取支持的日期格式列表
  static List<String> get supportedFormats => List.unmodifiable(_supportedFormats);
}

/// 🔥 日期验证结果类
class DateValidationResult {
  final bool isValid;
  final String? errorMessage;

  const DateValidationResult(this.isValid, this.errorMessage);

  bool get isSuccess => isValid;
  bool get isFailure => !isValid;

  @override
  String toString() {
    return 'DateValidationResult(isValid: $isValid, errorMessage: $errorMessage)';
  }
}

/// 🔥 日期范围类（年份）
class DateRange {
  final int start;
  final int end;

  const DateRange({required this.start, required this.end});

  bool contains(int year) => year >= start && year <= end;
  int get length => end - start + 1;
  List<int> get years => List.generate(length, (index) => start + index);

  @override
  String toString() => 'DateRange(start: $start, end: $end)';
}

/// 🔥 日期时间范围类
class DateTimeRange {
  final DateTime start;
  final DateTime end;

  const DateTimeRange({required this.start, required this.end});

  bool contains(DateTime date) => 
      date.isAfter(start.subtract(const Duration(days: 1))) && 
      date.isBefore(end.add(const Duration(days: 1)));

  Duration get duration => end.difference(start);

  @override
  String toString() => 'DateTimeRange(start: $start, end: $end)';
}
