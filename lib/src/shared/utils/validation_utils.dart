import 'package:flutter/foundation.dart';

/// 🔥 正则表达式验证工具类
/// 
/// 提供常用的表单验证功能，包括：
/// - 邮箱验证
/// - 手机号验证
/// - 身份证验证
/// - 姓名验证
/// - 密码验证
/// - 数字验证
/// - 网址验证
class ValidationUtils {
  
  /// 🔥 邮箱正则表达式
  /// 支持常见的邮箱格式：<EMAIL>
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  /// 🔥 中国大陆手机号正则表达式
  /// 支持13x, 14x, 15x, 16x, 17x, 18x, 19x开头的11位手机号
  static final RegExp _phoneRegex = RegExp(
    r'^1[3-9]\d{9}$',
  );

  /// 🔥 中国大陆身份证号正则表达式
  /// 支持18位身份证号码
  static final RegExp _idCardRegex = RegExp(
    r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$',
  );

  /// 🔥 中文姓名正则表达式
  /// 支持2-10个中文字符，包含·符号（如：欧阳·娜娜）
  static final RegExp _chineseNameRegex = RegExp(
    r'^[\u4e00-\u9fa5·]{2,10}$',
  );

  /// 🔥 密码正则表达式
  /// 8-20位，包含字母、数字、特殊字符中的至少两种
  static final RegExp _passwordRegex = RegExp(
    r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,20}$',
  );

  /// 🔥 强密码正则表达式
  /// 8-20位，必须包含大写字母、小写字母、数字、特殊字符
  static final RegExp _strongPasswordRegex = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$',
  );

  /// 🔥 数字正则表达式（整数）
  static final RegExp _numberRegex = RegExp(r'^\d+$');

  /// 🔥 小数正则表达式
  static final RegExp _decimalRegex = RegExp(r'^\d+(\.\d+)?$');

  /// 🔥 网址正则表达式
  static final RegExp _urlRegex = RegExp(
    r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
  );

  /// 🔥 QQ号正则表达式
  /// 5-11位数字，不能以0开头
  static final RegExp _qqRegex = RegExp(r'^[1-9]\d{4,10}$');

  /// 🔥 微信号正则表达式
  /// 6-20位，字母开头，可包含字母、数字、下划线、减号
  static final RegExp _wechatRegex = RegExp(r'^[a-zA-Z][a-zA-Z0-9_-]{5,19}$');

  /// 🔥 银行卡号正则表达式
  /// 16-19位数字
  static final RegExp _bankCardRegex = RegExp(r'^\d{16,19}$');

  // ==================== 验证方法 ====================

  /// 🔥 验证邮箱
  static ValidationResult validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return ValidationResult(false, '邮箱不能为空');
    }
    
    if (!_emailRegex.hasMatch(email)) {
      return ValidationResult(false, '请输入正确的邮箱格式');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 验证手机号
  static ValidationResult validatePhone(String? phone) {
    if (phone == null || phone.isEmpty) {
      return ValidationResult(false, '手机号不能为空');
    }
    
    if (!_phoneRegex.hasMatch(phone)) {
      return ValidationResult(false, '请输入正确的手机号格式');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 验证身份证号
  static ValidationResult validateIdCard(String? idCard) {
    if (idCard == null || idCard.isEmpty) {
      return ValidationResult(false, '身份证号不能为空');
    }
    
    if (!_idCardRegex.hasMatch(idCard)) {
      return ValidationResult(false, '请输入正确的身份证号格式');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 验证中文姓名
  static ValidationResult validateChineseName(String? name) {
    if (name == null || name.isEmpty) {
      return ValidationResult(false, '姓名不能为空');
    }
    
    if (!_chineseNameRegex.hasMatch(name)) {
      return ValidationResult(false, '请输入2-10位中文姓名');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 验证密码
  static ValidationResult validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return ValidationResult(false, '密码不能为空');
    }
    
    if (password.length < 8) {
      return ValidationResult(false, '密码长度不能少于8位');
    }
    
    if (password.length > 20) {
      return ValidationResult(false, '密码长度不能超过20位');
    }
    
    if (!_passwordRegex.hasMatch(password)) {
      return ValidationResult(false, '密码必须包含字母和数字');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 验证强密码
  static ValidationResult validateStrongPassword(String? password) {
    if (password == null || password.isEmpty) {
      return ValidationResult(false, '密码不能为空');
    }
    
    if (password.length < 8) {
      return ValidationResult(false, '密码长度不能少于8位');
    }
    
    if (password.length > 20) {
      return ValidationResult(false, '密码长度不能超过20位');
    }
    
    if (!_strongPasswordRegex.hasMatch(password)) {
      return ValidationResult(false, '密码必须包含大小写字母、数字和特殊字符');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 验证数字
  static ValidationResult validateNumber(String? number) {
    if (number == null || number.isEmpty) {
      return ValidationResult(false, '数字不能为空');
    }
    
    if (!_numberRegex.hasMatch(number)) {
      return ValidationResult(false, '请输入正确的数字格式');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 验证小数
  static ValidationResult validateDecimal(String? decimal) {
    if (decimal == null || decimal.isEmpty) {
      return ValidationResult(false, '数值不能为空');
    }
    
    if (!_decimalRegex.hasMatch(decimal)) {
      return ValidationResult(false, '请输入正确的数值格式');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 验证网址
  static ValidationResult validateUrl(String? url) {
    if (url == null || url.isEmpty) {
      return ValidationResult(false, '网址不能为空');
    }
    
    if (!_urlRegex.hasMatch(url)) {
      return ValidationResult(false, '请输入正确的网址格式');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 验证QQ号
  static ValidationResult validateQQ(String? qq) {
    if (qq == null || qq.isEmpty) {
      return ValidationResult(false, 'QQ号不能为空');
    }
    
    if (!_qqRegex.hasMatch(qq)) {
      return ValidationResult(false, '请输入正确的QQ号格式');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 验证微信号
  static ValidationResult validateWechat(String? wechat) {
    if (wechat == null || wechat.isEmpty) {
      return ValidationResult(false, '微信号不能为空');
    }
    
    if (!_wechatRegex.hasMatch(wechat)) {
      return ValidationResult(false, '微信号格式不正确（6-20位，字母开头）');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 验证银行卡号
  static ValidationResult validateBankCard(String? bankCard) {
    if (bankCard == null || bankCard.isEmpty) {
      return ValidationResult(false, '银行卡号不能为空');
    }
    
    if (!_bankCardRegex.hasMatch(bankCard)) {
      return ValidationResult(false, '请输入正确的银行卡号格式');
    }
    
    return ValidationResult(true, null);
  }

  // ==================== 自定义验证 ====================

  /// 🔥 自定义正则验证
  static ValidationResult validateCustom(String? value, RegExp regex, String errorMessage) {
    if (value == null || value.isEmpty) {
      return ValidationResult(false, '内容不能为空');
    }
    
    if (!regex.hasMatch(value)) {
      return ValidationResult(false, errorMessage);
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 长度验证
  static ValidationResult validateLength(String? value, int minLength, int maxLength, String fieldName) {
    if (value == null || value.isEmpty) {
      return ValidationResult(false, '$fieldName不能为空');
    }
    
    if (value.length < minLength) {
      return ValidationResult(false, '$fieldName长度不能少于$minLength位');
    }
    
    if (value.length > maxLength) {
      return ValidationResult(false, '$fieldName长度不能超过$maxLength位');
    }
    
    return ValidationResult(true, null);
  }

  /// 🔥 必填验证
  static ValidationResult validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult(false, '$fieldName不能为空');
    }
    
    return ValidationResult(true, null);
  }
}

/// 🔥 验证结果类
class ValidationResult {
  final bool isValid;
  final String? errorMessage;

  const ValidationResult(this.isValid, this.errorMessage);

  /// 是否验证成功
  bool get isSuccess => isValid;

  /// 是否验证失败
  bool get isFailure => !isValid;

  @override
  String toString() {
    return 'ValidationResult(isValid: $isValid, errorMessage: $errorMessage)';
  }
}

/// 🔥 验证类型枚举
enum ValidationType {
  email,          // 邮箱
  phone,          // 手机号
  idCard,         // 身份证
  chineseName,    // 中文姓名
  password,       // 密码
  strongPassword, // 强密码
  number,         // 数字
  decimal,        // 小数
  url,            // 网址
  qq,             // QQ号
  wechat,         // 微信号
  bankCard,       // 银行卡号
  custom,         // 自定义
}
