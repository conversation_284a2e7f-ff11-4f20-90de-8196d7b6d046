import 'package:flutter/material.dart';

/// 全局安全区域管理器
/// 
/// 用于缓存设备的安全区域信息，避免重复调用MediaQuery
/// 在应用启动时初始化一次，后续直接使用缓存值
class SafeAreaManager {
  static SafeAreaManager? _instance;
  static SafeAreaManager get instance => _instance ??= SafeAreaManager._();
  
  SafeAreaManager._();

  // 缓存的安全区域值
  double? _topPadding;
  double? _bottomPadding;
  double? _leftPadding;
  double? _rightPadding;
  
  // 缓存的屏幕尺寸
  Size? _screenSize;
  double? _devicePixelRatio;

  // 缓存的视图插入值（键盘等）
  EdgeInsets? _viewInsets;

  /// 初始化安全区域信息
  /// 应在应用启动时调用一次
  void initialize(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final padding = mediaQuery.padding;
    
    _topPadding = padding.top;
    _bottomPadding = padding.bottom;
    _leftPadding = padding.left;
    _rightPadding = padding.right;
    
    _screenSize = mediaQuery.size;
    _devicePixelRatio = mediaQuery.devicePixelRatio;
    _viewInsets = mediaQuery.viewInsets;

    debugPrint('SafeAreaManager initialized:');
    debugPrint('  Top: $_topPadding');
    debugPrint('  Bottom: $_bottomPadding');
    debugPrint('  Left: $_leftPadding');
    debugPrint('  Right: $_rightPadding');
    debugPrint('  Screen: $_screenSize');
    debugPrint('  ViewInsets: $_viewInsets');
  }

  /// 获取顶部安全区域高度
  double get topPadding {
    assert(_topPadding != null, 'SafeAreaManager not initialized. Call initialize() first.');
    return _topPadding!;
  }

  /// 获取底部安全区域高度
  double get bottomPadding {
    assert(_bottomPadding != null, 'SafeAreaManager not initialized. Call initialize() first.');
    return _bottomPadding!;
  }

  /// 获取左侧安全区域宽度
  double get leftPadding {
    assert(_leftPadding != null, 'SafeAreaManager not initialized. Call initialize() first.');
    return _leftPadding!;
  }

  /// 获取右侧安全区域宽度
  double get rightPadding {
    assert(_rightPadding != null, 'SafeAreaManager not initialized. Call initialize() first.');
    return _rightPadding!;
  }

  /// 获取屏幕尺寸
  Size get screenSize {
    assert(_screenSize != null, 'SafeAreaManager not initialized. Call initialize() first.');
    return _screenSize!;
  }

  /// 获取设备像素比
  double get devicePixelRatio {
    assert(_devicePixelRatio != null, 'SafeAreaManager not initialized. Call initialize() first.');
    return _devicePixelRatio!;
  }

  /// 获取视图插入值（键盘高度等）
  /// 注意：这个值在初始化时获取，通常键盘是收起的，所以bottom为0
  /// 如果需要实时键盘高度，仍需使用MediaQuery.of(context).viewInsets.bottom
  EdgeInsets get viewInsets {
    assert(_viewInsets != null, 'SafeAreaManager not initialized. Call initialize() first.');
    return _viewInsets!;
  }

  /// 获取初始化时的键盘高度（通常为0）
  double get keyboardHeight => viewInsets.bottom;

  /// 检查是否已初始化
  bool get isInitialized => _topPadding != null;

  /// 重置缓存（用于测试或特殊情况）
  void reset() {
    _topPadding = null;
    _bottomPadding = null;
    _leftPadding = null;
    _rightPadding = null;
    _screenSize = null;
    _devicePixelRatio = null;
    _viewInsets = null;
  }

  /// 获取实时键盘高度的便捷方法
  /// 这个方法仍然需要BuildContext，因为键盘高度是动态的
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }
}
