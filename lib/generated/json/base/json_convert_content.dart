// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import 'package:flutter_kit/src/datasource/models/area_model_entity.dart';
import 'package:flutter_kit/src/datasource/models/base_info_entity.dart';
import 'package:flutter_kit/src/datasource/models/collect_enterprise_entity.dart';
import 'package:flutter_kit/src/datasource/models/enterprise_detail_entity.dart';
import 'package:flutter_kit/src/datasource/models/job_detail_entity.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';
import 'package:flutter_kit/src/datasource/models/job_record_entity.dart';
import 'package:flutter_kit/src/datasource/models/read_job_entity.dart';
import 'package:flutter_kit/src/datasource/models/resume_entity.dart';
import 'package:flutter_kit/src/datasource/models/user_account_entity.dart';

JsonConvert jsonConvert = JsonConvert();

typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(Object error, StackTrace stackTrace);
extension MapSafeExt<K, V> on Map<K, V> {
  T? getOrNull<T>(K? key) {
    if (!containsKey(key) || key == null) {
      return null;
    } else {
      return this[key] as T?;
    }
  }
}

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value
          .map((dynamic e) => _asT<T>(e, enumConvert: enumConvert))
          .toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) =>
      _asT<T>(e, enumConvert: enumConvert)!).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        var covertFunc = convertFuncMap[type]!;
        if (covertFunc is Map<String, dynamic>) {
          return covertFunc(value as Map<String, dynamic>) as T;
        } else {
          return covertFunc(Map<String, dynamic>.from(value)) as T;
        }
      } else {
        throw UnimplementedError(
            '$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if (<AreaModelEntity>[] is M) {
      return data.map<AreaModelEntity>((Map<String, dynamic> e) =>
          AreaModelEntity.fromJson(e)).toList() as M;
    }
    if (<BaseInfoEntity>[] is M) {
      return data.map<BaseInfoEntity>((Map<String, dynamic> e) =>
          BaseInfoEntity.fromJson(e)).toList() as M;
    }
    if (<CollectEnterpriseEntity>[] is M) {
      return data.map<CollectEnterpriseEntity>((Map<String, dynamic> e) =>
          CollectEnterpriseEntity.fromJson(e)).toList() as M;
    }
    if (<EnterpriseDetailEntity>[] is M) {
      return data.map<EnterpriseDetailEntity>((Map<String, dynamic> e) =>
          EnterpriseDetailEntity.fromJson(e)).toList() as M;
    }
    if (<EnterpriseDetailJobs>[] is M) {
      return data.map<EnterpriseDetailJobs>((Map<String, dynamic> e) =>
          EnterpriseDetailJobs.fromJson(e)).toList() as M;
    }
    if (<EnterpriseDetailJobsUserAuths>[] is M) {
      return data.map<EnterpriseDetailJobsUserAuths>((Map<String, dynamic> e) =>
          EnterpriseDetailJobsUserAuths.fromJson(e)).toList() as M;
    }
    if (<JobDetailEntity>[] is M) {
      return data.map<JobDetailEntity>((Map<String, dynamic> e) =>
          JobDetailEntity.fromJson(e)).toList() as M;
    }
    if (<JobDetailUserAuths>[] is M) {
      return data.map<JobDetailUserAuths>((Map<String, dynamic> e) =>
          JobDetailUserAuths.fromJson(e)).toList() as M;
    }
    if (<JobInfoEntity>[] is M) {
      return data.map<JobInfoEntity>((Map<String, dynamic> e) =>
          JobInfoEntity.fromJson(e)).toList() as M;
    }
    if (<JobInfoUserAuths>[] is M) {
      return data.map<JobInfoUserAuths>((Map<String, dynamic> e) =>
          JobInfoUserAuths.fromJson(e)).toList() as M;
    }
    if (<JobRecordEntity>[] is M) {
      return data.map<JobRecordEntity>((Map<String, dynamic> e) =>
          JobRecordEntity.fromJson(e)).toList() as M;
    }
    if (<JobRecordJob>[] is M) {
      return data.map<JobRecordJob>((Map<String, dynamic> e) =>
          JobRecordJob.fromJson(e)).toList() as M;
    }
    if (<JobRecordJobUserAuths>[] is M) {
      return data.map<JobRecordJobUserAuths>((Map<String, dynamic> e) =>
          JobRecordJobUserAuths.fromJson(e)).toList() as M;
    }
    if (<JobRecordResume>[] is M) {
      return data.map<JobRecordResume>((Map<String, dynamic> e) =>
          JobRecordResume.fromJson(e)).toList() as M;
    }
    if (<ReadJobEntity>[] is M) {
      return data.map<ReadJobEntity>((Map<String, dynamic> e) =>
          ReadJobEntity.fromJson(e)).toList() as M;
    }
    if (<ResumeEntity>[] is M) {
      return data.map<ResumeEntity>((Map<String, dynamic> e) =>
          ResumeEntity.fromJson(e)).toList() as M;
    }
    if (<ResumeWorkExperience>[] is M) {
      return data.map<ResumeWorkExperience>((Map<String, dynamic> e) =>
          ResumeWorkExperience.fromJson(e)).toList() as M;
    }
    if (<ResumeEduExperience>[] is M) {
      return data.map<ResumeEduExperience>((Map<String, dynamic> e) =>
          ResumeEduExperience.fromJson(e)).toList() as M;
    }
    if (<UserAccountEntity>[] is M) {
      return data.map<UserAccountEntity>((Map<String, dynamic> e) =>
          UserAccountEntity.fromJson(e)).toList() as M;
    }

    debugPrint("$M not found");

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(
          json.map((dynamic e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (AreaModelEntity).toString(): AreaModelEntity.fromJson,
    (BaseInfoEntity).toString(): BaseInfoEntity.fromJson,
    (CollectEnterpriseEntity).toString(): CollectEnterpriseEntity.fromJson,
    (EnterpriseDetailEntity).toString(): EnterpriseDetailEntity.fromJson,
    (EnterpriseDetailJobs).toString(): EnterpriseDetailJobs.fromJson,
    (EnterpriseDetailJobsUserAuths).toString(): EnterpriseDetailJobsUserAuths
        .fromJson,
    (JobDetailEntity).toString(): JobDetailEntity.fromJson,
    (JobDetailUserAuths).toString(): JobDetailUserAuths.fromJson,
    (JobInfoEntity).toString(): JobInfoEntity.fromJson,
    (JobInfoUserAuths).toString(): JobInfoUserAuths.fromJson,
    (JobRecordEntity).toString(): JobRecordEntity.fromJson,
    (JobRecordJob).toString(): JobRecordJob.fromJson,
    (JobRecordJobUserAuths).toString(): JobRecordJobUserAuths.fromJson,
    (JobRecordResume).toString(): JobRecordResume.fromJson,
    (ReadJobEntity).toString(): ReadJobEntity.fromJson,
    (ResumeEntity).toString(): ResumeEntity.fromJson,
    (ResumeWorkExperience).toString(): ResumeWorkExperience.fromJson,
    (ResumeEduExperience).toString(): ResumeEduExperience.fromJson,
    (UserAccountEntity).toString(): UserAccountEntity.fromJson,
  };

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}