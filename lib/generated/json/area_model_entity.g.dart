import 'package:flutter_kit/generated/json/base/json_convert_content.dart';
import 'package:flutter_kit/src/datasource/models/area_model_entity.dart';

AreaModelEntity $AreaModelEntityFromJson(Map<String, dynamic> json) {
  final AreaModelEntity areaModelEntity = AreaModelEntity();
  final String? id = jsonConvert.convert<String>(json['Id']);
  if (id != null) {
    areaModelEntity.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['Name']);
  if (name != null) {
    areaModelEntity.name = name;
  }
  final String? parent = jsonConvert.convert<String>(json['Parent']);
  if (parent != null) {
    areaModelEntity.parent = parent;
  }
  return areaModelEntity;
}

Map<String, dynamic> $AreaModelEntityToJson(AreaModelEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Id'] = entity.id;
  data['Name'] = entity.name;
  data['Parent'] = entity.parent;
  return data;
}

extension AreaModelEntityExtension on AreaModelEntity {
  AreaModelEntity copyWith({
    String? id,
    String? name,
    String? parent,
  }) {
    return AreaModelEntity()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..parent = parent ?? this.parent;
  }
}