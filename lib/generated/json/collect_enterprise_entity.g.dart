import 'package:flutter_kit/generated/json/base/json_convert_content.dart';
import 'package:flutter_kit/src/datasource/models/collect_enterprise_entity.dart';

CollectEnterpriseEntity $CollectEnterpriseEntityFromJson(
    Map<String, dynamic> json) {
  final CollectEnterpriseEntity collectEnterpriseEntity = CollectEnterpriseEntity();
  final String? enterpriseCollectId = jsonConvert.convert<String>(
      json['EnterpriseCollectId']);
  if (enterpriseCollectId != null) {
    collectEnterpriseEntity.enterpriseCollectId = enterpriseCollectId;
  }
  final String? collectTime = jsonConvert.convert<String>(json['CollectTime']);
  if (collectTime != null) {
    collectEnterpriseEntity.collectTime = collectTime;
  }
  final String? enterpriseId = jsonConvert.convert<String>(
      json['EnterpriseId']);
  if (enterpriseId != null) {
    collectEnterpriseEntity.enterpriseId = enterpriseId;
  }
  final String? enterpriseName = jsonConvert.convert<String>(
      json['EnterpriseName']);
  if (enterpriseName != null) {
    collectEnterpriseEntity.enterpriseName = enterpriseName;
  }
  return collectEnterpriseEntity;
}

Map<String, dynamic> $CollectEnterpriseEntityToJson(
    CollectEnterpriseEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['EnterpriseCollectId'] = entity.enterpriseCollectId;
  data['CollectTime'] = entity.collectTime;
  data['EnterpriseId'] = entity.enterpriseId;
  data['EnterpriseName'] = entity.enterpriseName;
  return data;
}

extension CollectEnterpriseEntityExtension on CollectEnterpriseEntity {
  CollectEnterpriseEntity copyWith({
    String? enterpriseCollectId,
    String? collectTime,
    String? enterpriseId,
    String? enterpriseName,
  }) {
    return CollectEnterpriseEntity()
      ..enterpriseCollectId = enterpriseCollectId ?? this.enterpriseCollectId
      ..collectTime = collectTime ?? this.collectTime
      ..enterpriseId = enterpriseId ?? this.enterpriseId
      ..enterpriseName = enterpriseName ?? this.enterpriseName;
  }
}