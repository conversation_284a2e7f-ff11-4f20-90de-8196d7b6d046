import 'package:flutter_kit/generated/json/base/json_convert_content.dart';
import 'package:flutter_kit/src/datasource/models/user_account_entity.dart';

UserAccountEntity $UserAccountEntityFromJson(Map<String, dynamic> json) {
  final UserAccountEntity userAccountEntity = UserAccountEntity();
  final String? id = jsonConvert.convert<String>(json['Id']);
  if (id != null) {
    userAccountEntity.id = id;
  }
  final String? rold = jsonConvert.convert<String>(json['Rold']);
  if (rold != null) {
    userAccountEntity.rold = rold;
  }
  final String? nickName = jsonConvert.convert<String>(json['NickName']);
  if (nickName != null) {
    userAccountEntity.nickName = nickName;
  }
  final String? headPic = jsonConvert.convert<String>(json['HeadPic']);
  if (headPic != null) {
    userAccountEntity.headPic = headPic;
  }
  final String? phone = jsonConvert.convert<String>(json['Phone']);
  if (phone != null) {
    userAccountEntity.phone = phone;
  }
  final String? registerTime = jsonConvert.convert<String>(
      json['RegisterTime']);
  if (registerTime != null) {
    userAccountEntity.registerTime = registerTime;
  }
  final int? mailCount = jsonConvert.convert<int>(json['MailCount']);
  if (mailCount != null) {
    userAccountEntity.mailCount = mailCount;
  }
  final int? mailNotReadCount = jsonConvert.convert<int>(
      json['MailNotReadCount']);
  if (mailNotReadCount != null) {
    userAccountEntity.mailNotReadCount = mailNotReadCount;
  }
  final int? informCount = jsonConvert.convert<int>(json['InformCount']);
  if (informCount != null) {
    userAccountEntity.informCount = informCount;
  }
  final double? proportion = jsonConvert.convert<double>(json['Proportion']);
  if (proportion != null) {
    userAccountEntity.proportion = proportion;
  }
  final bool? isHasResumeBaseInfo = jsonConvert.convert<bool>(
      json['IsHasResumeBaseInfo']);
  if (isHasResumeBaseInfo != null) {
    userAccountEntity.isHasResumeBaseInfo = isHasResumeBaseInfo;
  }
  final bool? isHasResume = jsonConvert.convert<bool>(json['IsHasResume']);
  if (isHasResume != null) {
    userAccountEntity.isHasResume = isHasResume;
  }
  final int? jobCollectsCount = jsonConvert.convert<int>(
      json['JobCollectsCount']);
  if (jobCollectsCount != null) {
    userAccountEntity.jobCollectsCount = jobCollectsCount;
  }
  final int? enterpriseCollectsCount = jsonConvert.convert<int>(
      json['EnterpriseCollectsCount']);
  if (enterpriseCollectsCount != null) {
    userAccountEntity.enterpriseCollectsCount = enterpriseCollectsCount;
  }
  final int? jobApplysCount = jsonConvert.convert<int>(json['JobApplysCount']);
  if (jobApplysCount != null) {
    userAccountEntity.jobApplysCount = jobApplysCount;
  }
  final List<dynamic>? collectJobIds = (json['CollectJobIds'] as List<dynamic>?)
      ?.map(
          (e) => e)
      .toList();
  if (collectJobIds != null) {
    userAccountEntity.collectJobIds = collectJobIds;
  }
  final List<dynamic>? jobApplyIds = (json['JobApplyIds'] as List<dynamic>?)
      ?.map(
          (e) => e)
      .toList();
  if (jobApplyIds != null) {
    userAccountEntity.jobApplyIds = jobApplyIds;
  }
  final String? realName = jsonConvert.convert<String>(json['RealName']);
  if (realName != null) {
    userAccountEntity.realName = realName;
  }
  return userAccountEntity;
}

Map<String, dynamic> $UserAccountEntityToJson(UserAccountEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Id'] = entity.id;
  data['Rold'] = entity.rold;
  data['NickName'] = entity.nickName;
  data['HeadPic'] = entity.headPic;
  data['Phone'] = entity.phone;
  data['RegisterTime'] = entity.registerTime;
  data['MailCount'] = entity.mailCount;
  data['MailNotReadCount'] = entity.mailNotReadCount;
  data['InformCount'] = entity.informCount;
  data['Proportion'] = entity.proportion;
  data['IsHasResumeBaseInfo'] = entity.isHasResumeBaseInfo;
  data['IsHasResume'] = entity.isHasResume;
  data['JobCollectsCount'] = entity.jobCollectsCount;
  data['EnterpriseCollectsCount'] = entity.enterpriseCollectsCount;
  data['JobApplysCount'] = entity.jobApplysCount;
  data['CollectJobIds'] = entity.collectJobIds;
  data['JobApplyIds'] = entity.jobApplyIds;
  data['RealName'] = entity.realName;
  return data;
}

extension UserAccountEntityExtension on UserAccountEntity {
  UserAccountEntity copyWith({
    String? id,
    String? rold,
    String? nickName,
    String? headPic,
    String? phone,
    String? registerTime,
    int? mailCount,
    int? mailNotReadCount,
    int? informCount,
    double? proportion,
    bool? isHasResumeBaseInfo,
    bool? isHasResume,
    int? jobCollectsCount,
    int? enterpriseCollectsCount,
    int? jobApplysCount,
    List<dynamic>? collectJobIds,
    List<dynamic>? jobApplyIds,
    String? realName,
  }) {
    return UserAccountEntity()
      ..id = id ?? this.id
      ..rold = rold ?? this.rold
      ..nickName = nickName ?? this.nickName
      ..headPic = headPic ?? this.headPic
      ..phone = phone ?? this.phone
      ..registerTime = registerTime ?? this.registerTime
      ..mailCount = mailCount ?? this.mailCount
      ..mailNotReadCount = mailNotReadCount ?? this.mailNotReadCount
      ..informCount = informCount ?? this.informCount
      ..proportion = proportion ?? this.proportion
      ..isHasResumeBaseInfo = isHasResumeBaseInfo ?? this.isHasResumeBaseInfo
      ..isHasResume = isHasResume ?? this.isHasResume
      ..jobCollectsCount = jobCollectsCount ?? this.jobCollectsCount
      ..enterpriseCollectsCount = enterpriseCollectsCount ??
          this.enterpriseCollectsCount
      ..jobApplysCount = jobApplysCount ?? this.jobApplysCount
      ..collectJobIds = collectJobIds ?? this.collectJobIds
      ..jobApplyIds = jobApplyIds ?? this.jobApplyIds
      ..realName = realName ?? this.realName;
  }
}