import 'package:flutter_kit/generated/json/base/json_convert_content.dart';
import 'package:flutter_kit/src/datasource/models/read_job_entity.dart';

ReadJobEntity $ReadJobEntityFromJson(Map<String, dynamic> json) {
  final ReadJobEntity readJobEntity = ReadJobEntity();
  final String? id = jsonConvert.convert<String>(json['Id']);
  if (id != null) {
    readJobEntity.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['Name']);
  if (name != null) {
    readJobEntity.name = name;
  }
  final String? image = jsonConvert.convert<String>(json['Image']);
  if (image != null) {
    readJobEntity.image = image;
  }
  final String? enterpriseName = jsonConvert.convert<String>(
      json['EnterpriseName']);
  if (enterpriseName != null) {
    readJobEntity.enterpriseName = enterpriseName;
  }
  final String? payWay = jsonConvert.convert<String>(json['PayWay']);
  if (payWay != null) {
    readJobEntity.payWay = payWay;
  }
  final String? pay = jsonConvert.convert<String>(json['Pay']);
  if (pay != null) {
    readJobEntity.pay = pay;
  }
  final String? time = jsonConvert.convert<String>(json['Time']);
  if (time != null) {
    readJobEntity.time = time;
  }
  return readJobEntity;
}

Map<String, dynamic> $ReadJobEntityToJson(ReadJobEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Id'] = entity.id;
  data['Name'] = entity.name;
  data['Image'] = entity.image;
  data['EnterpriseName'] = entity.enterpriseName;
  data['PayWay'] = entity.payWay;
  data['Pay'] = entity.pay;
  data['Time'] = entity.time;
  return data;
}

extension ReadJobEntityExtension on ReadJobEntity {
  ReadJobEntity copyWith({
    String? id,
    String? name,
    String? image,
    String? enterpriseName,
    String? payWay,
    String? pay,
    String? time,
  }) {
    return ReadJobEntity()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..image = image ?? this.image
      ..enterpriseName = enterpriseName ?? this.enterpriseName
      ..payWay = payWay ?? this.payWay
      ..pay = pay ?? this.pay
      ..time = time ?? this.time;
  }
}