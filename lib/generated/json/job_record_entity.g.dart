import 'package:flutter_kit/generated/json/base/json_convert_content.dart';
import 'package:flutter_kit/src/datasource/models/job_record_entity.dart';

JobRecordEntity $JobRecordEntityFromJson(Map<String, dynamic> json) {
  final JobRecordEntity jobRecordEntity = JobRecordEntity();
  final String? id = jsonConvert.convert<String>(json['Id']);
  if (id != null) {
    jobRecordEntity.id = id;
  }
  final String? applyTime = jsonConvert.convert<String>(json['ApplyTime']);
  if (applyTime != null) {
    jobRecordEntity.applyTime = applyTime;
  }
  final bool? isDispose = jsonConvert.convert<bool>(json['IsDispose']);
  if (isDispose != null) {
    jobRecordEntity.isDispose = isDispose;
  }
  final String? disposeTime = jsonConvert.convert<String>(json['DisposeTime']);
  if (disposeTime != null) {
    jobRecordEntity.disposeTime = disposeTime;
  }
  final bool? isPass = jsonConvert.convert<bool>(json['IsPass']);
  if (isPass != null) {
    jobRecordEntity.isPass = isPass;
  }
  final String? disposeContent = jsonConvert.convert<String>(
      json['DisposeContent']);
  if (disposeContent != null) {
    jobRecordEntity.disposeContent = disposeContent;
  }
  final String? replyContent = jsonConvert.convert<String>(
      json['ReplyContent']);
  if (replyContent != null) {
    jobRecordEntity.replyContent = replyContent;
  }
  final String? jobSeekerId = jsonConvert.convert<String>(json['JobSeekerId']);
  if (jobSeekerId != null) {
    jobRecordEntity.jobSeekerId = jobSeekerId;
  }
  final JobRecordJob? job = jsonConvert.convert<JobRecordJob>(json['Job']);
  if (job != null) {
    jobRecordEntity.job = job;
  }
  final JobRecordResume? resume = jsonConvert.convert<JobRecordResume>(
      json['Resume']);
  if (resume != null) {
    jobRecordEntity.resume = resume;
  }
  final String? jobseekerName = jsonConvert.convert<String>(
      json['JobseekerName']);
  if (jobseekerName != null) {
    jobRecordEntity.jobseekerName = jobseekerName;
  }
  final int? resumeIntegral = jsonConvert.convert<int>(json['ResumeIntegral']);
  if (resumeIntegral != null) {
    jobRecordEntity.resumeIntegral = resumeIntegral;
  }
  final bool? isResumeSelectAuth = jsonConvert.convert<bool>(
      json['IsResumeSelectAuth']);
  if (isResumeSelectAuth != null) {
    jobRecordEntity.isResumeSelectAuth = isResumeSelectAuth;
  }
  final int? jobApplyDisposeStatus = jsonConvert.convert<int>(
      json['JobApplyDisposeStatus']);
  if (jobApplyDisposeStatus != null) {
    jobRecordEntity.jobApplyDisposeStatus = jobApplyDisposeStatus;
  }
  final bool? isRead = jsonConvert.convert<bool>(json['IsRead']);
  if (isRead != null) {
    jobRecordEntity.isRead = isRead;
  }
  return jobRecordEntity;
}

Map<String, dynamic> $JobRecordEntityToJson(JobRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Id'] = entity.id;
  data['ApplyTime'] = entity.applyTime;
  data['IsDispose'] = entity.isDispose;
  data['DisposeTime'] = entity.disposeTime;
  data['IsPass'] = entity.isPass;
  data['DisposeContent'] = entity.disposeContent;
  data['ReplyContent'] = entity.replyContent;
  data['JobSeekerId'] = entity.jobSeekerId;
  data['Job'] = entity.job.toJson();
  data['Resume'] = entity.resume.toJson();
  data['JobseekerName'] = entity.jobseekerName;
  data['ResumeIntegral'] = entity.resumeIntegral;
  data['IsResumeSelectAuth'] = entity.isResumeSelectAuth;
  data['JobApplyDisposeStatus'] = entity.jobApplyDisposeStatus;
  data['IsRead'] = entity.isRead;
  return data;
}

extension JobRecordEntityExtension on JobRecordEntity {
  JobRecordEntity copyWith({
    String? id,
    String? applyTime,
    bool? isDispose,
    String? disposeTime,
    bool? isPass,
    String? disposeContent,
    String? replyContent,
    String? jobSeekerId,
    JobRecordJob? job,
    JobRecordResume? resume,
    String? jobseekerName,
    int? resumeIntegral,
    bool? isResumeSelectAuth,
    int? jobApplyDisposeStatus,
    bool? isRead,
  }) {
    return JobRecordEntity()
      ..id = id ?? this.id
      ..applyTime = applyTime ?? this.applyTime
      ..isDispose = isDispose ?? this.isDispose
      ..disposeTime = disposeTime ?? this.disposeTime
      ..isPass = isPass ?? this.isPass
      ..disposeContent = disposeContent ?? this.disposeContent
      ..replyContent = replyContent ?? this.replyContent
      ..jobSeekerId = jobSeekerId ?? this.jobSeekerId
      ..job = job ?? this.job
      ..resume = resume ?? this.resume
      ..jobseekerName = jobseekerName ?? this.jobseekerName
      ..resumeIntegral = resumeIntegral ?? this.resumeIntegral
      ..isResumeSelectAuth = isResumeSelectAuth ?? this.isResumeSelectAuth
      ..jobApplyDisposeStatus = jobApplyDisposeStatus ??
          this.jobApplyDisposeStatus
      ..isRead = isRead ?? this.isRead;
  }
}

JobRecordJob $JobRecordJobFromJson(Map<String, dynamic> json) {
  final JobRecordJob jobRecordJob = JobRecordJob();
  final String? id = jsonConvert.convert<String>(json['Id']);
  if (id != null) {
    jobRecordJob.id = id;
  }
  final String? enterpriseId = jsonConvert.convert<String>(
      json['EnterpriseId']);
  if (enterpriseId != null) {
    jobRecordJob.enterpriseId = enterpriseId;
  }
  final String? enterpriseName = jsonConvert.convert<String>(
      json['EnterpriseName']);
  if (enterpriseName != null) {
    jobRecordJob.enterpriseName = enterpriseName;
  }
  final List<JobRecordJobUserAuths>? userAuths = (json['UserAuths'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<JobRecordJobUserAuths>(e) as JobRecordJobUserAuths)
      .toList();
  if (userAuths != null) {
    jobRecordJob.userAuths = userAuths;
  }
  final String? enterpriseLogoSmall = jsonConvert.convert<String>(
      json['EnterpriseLogoSmall']);
  if (enterpriseLogoSmall != null) {
    jobRecordJob.enterpriseLogoSmall = enterpriseLogoSmall;
  }
  final String? name = jsonConvert.convert<String>(json['Name']);
  if (name != null) {
    jobRecordJob.name = name;
  }
  final String? nature = jsonConvert.convert<String>(json['Nature']);
  if (nature != null) {
    jobRecordJob.nature = nature;
  }
  final String? jobTypeId = jsonConvert.convert<String>(json['JobTypeId']);
  if (jobTypeId != null) {
    jobRecordJob.jobTypeId = jobTypeId;
  }
  final String? jobTypeName = jsonConvert.convert<String>(json['JobTypeName']);
  if (jobTypeName != null) {
    jobRecordJob.jobTypeName = jobTypeName;
  }
  final String? department = jsonConvert.convert<String>(json['Department']);
  if (department != null) {
    jobRecordJob.department = department;
  }
  final int? recruitingCount = jsonConvert.convert<int>(
      json['RecruitingCount']);
  if (recruitingCount != null) {
    jobRecordJob.recruitingCount = recruitingCount;
  }
  final String? payWay = jsonConvert.convert<String>(json['PayWay']);
  if (payWay != null) {
    jobRecordJob.payWay = payWay;
  }
  final String? workAreaId = jsonConvert.convert<String>(json['WorkAreaId']);
  if (workAreaId != null) {
    jobRecordJob.workAreaId = workAreaId;
  }
  final String? workAreaName = jsonConvert.convert<String>(
      json['WorkAreaName']);
  if (workAreaName != null) {
    jobRecordJob.workAreaName = workAreaName;
  }
  final String? workAreaCascadeName = jsonConvert.convert<String>(
      json['WorkAreaCascadeName']);
  if (workAreaCascadeName != null) {
    jobRecordJob.workAreaCascadeName = workAreaCascadeName;
  }
  final String? pay = jsonConvert.convert<String>(json['Pay']);
  if (pay != null) {
    jobRecordJob.pay = pay;
  }
  final String? jobPayUnit = jsonConvert.convert<String>(json['JobPayUnit']);
  if (jobPayUnit != null) {
    jobRecordJob.jobPayUnit = jobPayUnit;
  }
  final String? welfare = jsonConvert.convert<String>(json['Welfare']);
  if (welfare != null) {
    jobRecordJob.welfare = welfare;
  }
  final String? welfareValue = jsonConvert.convert<String>(
      json['WelfareValue']);
  if (welfareValue != null) {
    jobRecordJob.welfareValue = welfareValue;
  }
  final String? mapLocation = jsonConvert.convert<String>(json['MapLocation']);
  if (mapLocation != null) {
    jobRecordJob.mapLocation = mapLocation;
  }
  final String? street = jsonConvert.convert<String>(json['Street']);
  if (street != null) {
    jobRecordJob.street = street;
  }
  final String? demandEducationCode = jsonConvert.convert<String>(
      json['DemandEducationCode']);
  if (demandEducationCode != null) {
    jobRecordJob.demandEducationCode = demandEducationCode;
  }
  final String? workAddress = jsonConvert.convert<String>(json['WorkAddress']);
  if (workAddress != null) {
    jobRecordJob.workAddress = workAddress;
  }
  final String? releaseTime = jsonConvert.convert<String>(json['ReleaseTime']);
  if (releaseTime != null) {
    jobRecordJob.releaseTime = releaseTime;
  }
  final bool? isPutaway = jsonConvert.convert<bool>(json['IsPutaway']);
  if (isPutaway != null) {
    jobRecordJob.isPutaway = isPutaway;
  }
  final int? applyCount = jsonConvert.convert<int>(json['ApplyCount']);
  if (applyCount != null) {
    jobRecordJob.applyCount = applyCount;
  }
  final int? selectCount = jsonConvert.convert<int>(json['SelectCount']);
  if (selectCount != null) {
    jobRecordJob.selectCount = selectCount;
  }
  final String? releaseChannel = jsonConvert.convert<String>(
      json['ReleaseChannel']);
  if (releaseChannel != null) {
    jobRecordJob.releaseChannel = releaseChannel;
  }
  return jobRecordJob;
}

Map<String, dynamic> $JobRecordJobToJson(JobRecordJob entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Id'] = entity.id;
  data['EnterpriseId'] = entity.enterpriseId;
  data['EnterpriseName'] = entity.enterpriseName;
  data['UserAuths'] = entity.userAuths.map((v) => v.toJson()).toList();
  data['EnterpriseLogoSmall'] = entity.enterpriseLogoSmall;
  data['Name'] = entity.name;
  data['Nature'] = entity.nature;
  data['JobTypeId'] = entity.jobTypeId;
  data['JobTypeName'] = entity.jobTypeName;
  data['Department'] = entity.department;
  data['RecruitingCount'] = entity.recruitingCount;
  data['PayWay'] = entity.payWay;
  data['WorkAreaId'] = entity.workAreaId;
  data['WorkAreaName'] = entity.workAreaName;
  data['WorkAreaCascadeName'] = entity.workAreaCascadeName;
  data['Pay'] = entity.pay;
  data['JobPayUnit'] = entity.jobPayUnit;
  data['Welfare'] = entity.welfare;
  data['WelfareValue'] = entity.welfareValue;
  data['MapLocation'] = entity.mapLocation;
  data['Street'] = entity.street;
  data['DemandEducationCode'] = entity.demandEducationCode;
  data['WorkAddress'] = entity.workAddress;
  data['ReleaseTime'] = entity.releaseTime;
  data['IsPutaway'] = entity.isPutaway;
  data['ApplyCount'] = entity.applyCount;
  data['SelectCount'] = entity.selectCount;
  data['ReleaseChannel'] = entity.releaseChannel;
  return data;
}

extension JobRecordJobExtension on JobRecordJob {
  JobRecordJob copyWith({
    String? id,
    String? enterpriseId,
    String? enterpriseName,
    List<JobRecordJobUserAuths>? userAuths,
    String? enterpriseLogoSmall,
    String? name,
    String? nature,
    String? jobTypeId,
    String? jobTypeName,
    String? department,
    int? recruitingCount,
    String? payWay,
    String? workAreaId,
    String? workAreaName,
    String? workAreaCascadeName,
    String? pay,
    String? jobPayUnit,
    String? welfare,
    String? welfareValue,
    String? mapLocation,
    String? street,
    String? demandEducationCode,
    String? workAddress,
    String? releaseTime,
    bool? isPutaway,
    int? applyCount,
    int? selectCount,
    String? releaseChannel,
  }) {
    return JobRecordJob()
      ..id = id ?? this.id
      ..enterpriseId = enterpriseId ?? this.enterpriseId
      ..enterpriseName = enterpriseName ?? this.enterpriseName
      ..userAuths = userAuths ?? this.userAuths
      ..enterpriseLogoSmall = enterpriseLogoSmall ?? this.enterpriseLogoSmall
      ..name = name ?? this.name
      ..nature = nature ?? this.nature
      ..jobTypeId = jobTypeId ?? this.jobTypeId
      ..jobTypeName = jobTypeName ?? this.jobTypeName
      ..department = department ?? this.department
      ..recruitingCount = recruitingCount ?? this.recruitingCount
      ..payWay = payWay ?? this.payWay
      ..workAreaId = workAreaId ?? this.workAreaId
      ..workAreaName = workAreaName ?? this.workAreaName
      ..workAreaCascadeName = workAreaCascadeName ?? this.workAreaCascadeName
      ..pay = pay ?? this.pay
      ..jobPayUnit = jobPayUnit ?? this.jobPayUnit
      ..welfare = welfare ?? this.welfare
      ..welfareValue = welfareValue ?? this.welfareValue
      ..mapLocation = mapLocation ?? this.mapLocation
      ..street = street ?? this.street
      ..demandEducationCode = demandEducationCode ?? this.demandEducationCode
      ..workAddress = workAddress ?? this.workAddress
      ..releaseTime = releaseTime ?? this.releaseTime
      ..isPutaway = isPutaway ?? this.isPutaway
      ..applyCount = applyCount ?? this.applyCount
      ..selectCount = selectCount ?? this.selectCount
      ..releaseChannel = releaseChannel ?? this.releaseChannel;
  }
}

JobRecordJobUserAuths $JobRecordJobUserAuthsFromJson(
    Map<String, dynamic> json) {
  final JobRecordJobUserAuths jobRecordJobUserAuths = JobRecordJobUserAuths();
  final String? type = jsonConvert.convert<String>(json['Type']);
  if (type != null) {
    jobRecordJobUserAuths.type = type;
  }
  final String? addTime = jsonConvert.convert<String>(json['AddTime']);
  if (addTime != null) {
    jobRecordJobUserAuths.addTime = addTime;
  }
  return jobRecordJobUserAuths;
}

Map<String, dynamic> $JobRecordJobUserAuthsToJson(
    JobRecordJobUserAuths entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Type'] = entity.type;
  data['AddTime'] = entity.addTime;
  return data;
}

extension JobRecordJobUserAuthsExtension on JobRecordJobUserAuths {
  JobRecordJobUserAuths copyWith({
    String? type,
    String? addTime,
  }) {
    return JobRecordJobUserAuths()
      ..type = type ?? this.type
      ..addTime = addTime ?? this.addTime;
  }
}

JobRecordResume $JobRecordResumeFromJson(Map<String, dynamic> json) {
  final JobRecordResume jobRecordResume = JobRecordResume();
  final String? id = jsonConvert.convert<String>(json['Id']);
  if (id != null) {
    jobRecordResume.id = id;
  }
  final String? title = jsonConvert.convert<String>(json['Title']);
  if (title != null) {
    jobRecordResume.title = title;
  }
  final String? realname = jsonConvert.convert<String>(json['Realname']);
  if (realname != null) {
    jobRecordResume.realname = realname;
  }
  final List<dynamic>? userAuths = (json['UserAuths'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (userAuths != null) {
    jobRecordResume.userAuths = userAuths;
  }
  final String? genderCode = jsonConvert.convert<String>(json['GenderCode']);
  if (genderCode != null) {
    jobRecordResume.genderCode = genderCode;
  }
  final String? nationCode = jsonConvert.convert<String>(json['NationCode']);
  if (nationCode != null) {
    jobRecordResume.nationCode = nationCode;
  }
  final String? maritalStatusCode = jsonConvert.convert<String>(
      json['MaritalStatusCode']);
  if (maritalStatusCode != null) {
    jobRecordResume.maritalStatusCode = maritalStatusCode;
  }
  final int? stature = jsonConvert.convert<int>(json['Stature']);
  if (stature != null) {
    jobRecordResume.stature = stature;
  }
  final String? address = jsonConvert.convert<String>(json['Address']);
  if (address != null) {
    jobRecordResume.address = address;
  }
  final String? computerLevelCode = jsonConvert.convert<String>(
      json['ComputerLevelCode']);
  if (computerLevelCode != null) {
    jobRecordResume.computerLevelCode = computerLevelCode;
  }
  final String? englishLevelCode = jsonConvert.convert<String>(
      json['EnglishLevelCode']);
  if (englishLevelCode != null) {
    jobRecordResume.englishLevelCode = englishLevelCode;
  }
  final String? nativePlaceAreaId = jsonConvert.convert<String>(
      json['NativePlaceAreaId']);
  if (nativePlaceAreaId != null) {
    jobRecordResume.nativePlaceAreaId = nativePlaceAreaId;
  }
  final String? nativePlaceAreaName = jsonConvert.convert<String>(
      json['NativePlaceAreaName']);
  if (nativePlaceAreaName != null) {
    jobRecordResume.nativePlaceAreaName = nativePlaceAreaName;
  }
  final String? nativePlaceAreaCascadeName = jsonConvert.convert<String>(
      json['NativePlaceAreaCascadeName']);
  if (nativePlaceAreaCascadeName != null) {
    jobRecordResume.nativePlaceAreaCascadeName = nativePlaceAreaCascadeName;
  }
  final String? graduateSchool = jsonConvert.convert<String>(
      json['GraduateSchool']);
  if (graduateSchool != null) {
    jobRecordResume.graduateSchool = graduateSchool;
  }
  final String? majorIn = jsonConvert.convert<String>(json['MajorIn']);
  if (majorIn != null) {
    jobRecordResume.majorIn = majorIn;
  }
  final String? headImage = jsonConvert.convert<String>(json['HeadImage']);
  if (headImage != null) {
    jobRecordResume.headImage = headImage;
  }
  final String? idcard = jsonConvert.convert<String>(json['Idcard']);
  if (idcard != null) {
    jobRecordResume.idcard = idcard;
  }
  final String? gender = jsonConvert.convert<String>(json['Gender']);
  if (gender != null) {
    jobRecordResume.gender = gender;
  }
  final String? nation = jsonConvert.convert<String>(json['Nation']);
  if (nation != null) {
    jobRecordResume.nation = nation;
  }
  final String? qQ = jsonConvert.convert<String>(json['QQ']);
  if (qQ != null) {
    jobRecordResume.qQ = qQ;
  }
  final String? birthday = jsonConvert.convert<String>(json['Birthday']);
  if (birthday != null) {
    jobRecordResume.birthday = birthday;
  }
  final String? liveAreaId = jsonConvert.convert<String>(json['LiveAreaId']);
  if (liveAreaId != null) {
    jobRecordResume.liveAreaId = liveAreaId;
  }
  final String? liveAreaName = jsonConvert.convert<String>(
      json['LiveAreaName']);
  if (liveAreaName != null) {
    jobRecordResume.liveAreaName = liveAreaName;
  }
  final String? liveAreaCascadeName = jsonConvert.convert<String>(
      json['LiveAreaCascadeName']);
  if (liveAreaCascadeName != null) {
    jobRecordResume.liveAreaCascadeName = liveAreaCascadeName;
  }
  final String? intentionAreaIds = jsonConvert.convert<String>(
      json['IntentionAreaIds']);
  if (intentionAreaIds != null) {
    jobRecordResume.intentionAreaIds = intentionAreaIds;
  }
  final String? intentionAreaNames = jsonConvert.convert<String>(
      json['IntentionAreaNames']);
  if (intentionAreaNames != null) {
    jobRecordResume.intentionAreaNames = intentionAreaNames;
  }
  final String? intentionJobTypeId = jsonConvert.convert<String>(
      json['IntentionJobTypeId']);
  if (intentionJobTypeId != null) {
    jobRecordResume.intentionJobTypeId = intentionJobTypeId;
  }
  final String? intentionJobType = jsonConvert.convert<String>(
      json['IntentionJobType']);
  if (intentionJobType != null) {
    jobRecordResume.intentionJobType = intentionJobType;
  }
  final String? intentionJobTypeIds = jsonConvert.convert<String>(
      json['IntentionJobTypeIds']);
  if (intentionJobTypeIds != null) {
    jobRecordResume.intentionJobTypeIds = intentionJobTypeIds;
  }
  final String? intentionJobTypeNames = jsonConvert.convert<String>(
      json['IntentionJobTypeNames']);
  if (intentionJobTypeNames != null) {
    jobRecordResume.intentionJobTypeNames = intentionJobTypeNames;
  }
  final String? workStatusCode = jsonConvert.convert<String>(
      json['WorkStatusCode']);
  if (workStatusCode != null) {
    jobRecordResume.workStatusCode = workStatusCode;
  }
  final String? workingAgeCode = jsonConvert.convert<String>(
      json['WorkingAgeCode']);
  if (workingAgeCode != null) {
    jobRecordResume.workingAgeCode = workingAgeCode;
  }
  final String? intentionPayCode = jsonConvert.convert<String>(
      json['IntentionPayCode']);
  if (intentionPayCode != null) {
    jobRecordResume.intentionPayCode = intentionPayCode;
  }
  final String? intentionPayValue = jsonConvert.convert<String>(
      json['IntentionPayValue']);
  if (intentionPayValue != null) {
    jobRecordResume.intentionPayValue = intentionPayValue;
  }
  final String? intentionPayWayCode = jsonConvert.convert<String>(
      json['IntentionPayWayCode']);
  if (intentionPayWayCode != null) {
    jobRecordResume.intentionPayWayCode = intentionPayWayCode;
  }
  final String? educationCode = jsonConvert.convert<String>(
      json['EducationCode']);
  if (educationCode != null) {
    jobRecordResume.educationCode = educationCode;
  }
  final String? workExperience = jsonConvert.convert<String>(
      json['WorkExperience']);
  if (workExperience != null) {
    jobRecordResume.workExperience = workExperience;
  }
  final String? eduExperience = jsonConvert.convert<String>(
      json['EduExperience']);
  if (eduExperience != null) {
    jobRecordResume.eduExperience = eduExperience;
  }
  final String? personalProfile = jsonConvert.convert<String>(
      json['PersonalProfile']);
  if (personalProfile != null) {
    jobRecordResume.personalProfile = personalProfile;
  }
  final double? proportion = jsonConvert.convert<double>(json['Proportion']);
  if (proportion != null) {
    jobRecordResume.proportion = proportion;
  }
  final String? certificate = jsonConvert.convert<String>(json['Certificate']);
  if (certificate != null) {
    jobRecordResume.certificate = certificate;
  }
  final String? skill = jsonConvert.convert<String>(json['Skill']);
  if (skill != null) {
    jobRecordResume.skill = skill;
  }
  final bool? isDefault = jsonConvert.convert<bool>(json['IsDefault']);
  if (isDefault != null) {
    jobRecordResume.isDefault = isDefault;
  }
  final int? integral = jsonConvert.convert<int>(json['Integral']);
  if (integral != null) {
    jobRecordResume.integral = integral;
  }
  final String? phone = jsonConvert.convert<String>(json['Phone']);
  if (phone != null) {
    jobRecordResume.phone = phone;
  }
  final String? email = jsonConvert.convert<String>(json['Email']);
  if (email != null) {
    jobRecordResume.email = email;
  }
  final String? userId = jsonConvert.convert<String>(json['UserId']);
  if (userId != null) {
    jobRecordResume.userId = userId;
  }
  final String? releaseTime = jsonConvert.convert<String>(json['ReleaseTime']);
  if (releaseTime != null) {
    jobRecordResume.releaseTime = releaseTime;
  }
  final String? labels = jsonConvert.convert<String>(json['Labels']);
  if (labels != null) {
    jobRecordResume.labels = labels;
  }
  final String? mapLocation = jsonConvert.convert<String>(json['MapLocation']);
  if (mapLocation != null) {
    jobRecordResume.mapLocation = mapLocation;
  }
  final String? street = jsonConvert.convert<String>(json['Street']);
  if (street != null) {
    jobRecordResume.street = street;
  }
  final String? jobSeekerGroupCode = jsonConvert.convert<String>(
      json['JobSeekerGroupCode']);
  if (jobSeekerGroupCode != null) {
    jobRecordResume.jobSeekerGroupCode = jobSeekerGroupCode;
  }
  final int? selectCount = jsonConvert.convert<int>(json['SelectCount']);
  if (selectCount != null) {
    jobRecordResume.selectCount = selectCount;
  }
  return jobRecordResume;
}

Map<String, dynamic> $JobRecordResumeToJson(JobRecordResume entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Id'] = entity.id;
  data['Title'] = entity.title;
  data['Realname'] = entity.realname;
  data['UserAuths'] = entity.userAuths;
  data['GenderCode'] = entity.genderCode;
  data['NationCode'] = entity.nationCode;
  data['MaritalStatusCode'] = entity.maritalStatusCode;
  data['Stature'] = entity.stature;
  data['Address'] = entity.address;
  data['ComputerLevelCode'] = entity.computerLevelCode;
  data['EnglishLevelCode'] = entity.englishLevelCode;
  data['NativePlaceAreaId'] = entity.nativePlaceAreaId;
  data['NativePlaceAreaName'] = entity.nativePlaceAreaName;
  data['NativePlaceAreaCascadeName'] = entity.nativePlaceAreaCascadeName;
  data['GraduateSchool'] = entity.graduateSchool;
  data['MajorIn'] = entity.majorIn;
  data['HeadImage'] = entity.headImage;
  data['Idcard'] = entity.idcard;
  data['Gender'] = entity.gender;
  data['Nation'] = entity.nation;
  data['QQ'] = entity.qQ;
  data['Birthday'] = entity.birthday;
  data['LiveAreaId'] = entity.liveAreaId;
  data['LiveAreaName'] = entity.liveAreaName;
  data['LiveAreaCascadeName'] = entity.liveAreaCascadeName;
  data['IntentionAreaIds'] = entity.intentionAreaIds;
  data['IntentionAreaNames'] = entity.intentionAreaNames;
  data['IntentionJobTypeId'] = entity.intentionJobTypeId;
  data['IntentionJobType'] = entity.intentionJobType;
  data['IntentionJobTypeIds'] = entity.intentionJobTypeIds;
  data['IntentionJobTypeNames'] = entity.intentionJobTypeNames;
  data['WorkStatusCode'] = entity.workStatusCode;
  data['WorkingAgeCode'] = entity.workingAgeCode;
  data['IntentionPayCode'] = entity.intentionPayCode;
  data['IntentionPayValue'] = entity.intentionPayValue;
  data['IntentionPayWayCode'] = entity.intentionPayWayCode;
  data['EducationCode'] = entity.educationCode;
  data['WorkExperience'] = entity.workExperience;
  data['EduExperience'] = entity.eduExperience;
  data['PersonalProfile'] = entity.personalProfile;
  data['Proportion'] = entity.proportion;
  data['Certificate'] = entity.certificate;
  data['Skill'] = entity.skill;
  data['IsDefault'] = entity.isDefault;
  data['Integral'] = entity.integral;
  data['Phone'] = entity.phone;
  data['Email'] = entity.email;
  data['UserId'] = entity.userId;
  data['ReleaseTime'] = entity.releaseTime;
  data['Labels'] = entity.labels;
  data['MapLocation'] = entity.mapLocation;
  data['Street'] = entity.street;
  data['JobSeekerGroupCode'] = entity.jobSeekerGroupCode;
  data['SelectCount'] = entity.selectCount;
  return data;
}

extension JobRecordResumeExtension on JobRecordResume {
  JobRecordResume copyWith({
    String? id,
    String? title,
    String? realname,
    List<dynamic>? userAuths,
    String? genderCode,
    String? nationCode,
    String? maritalStatusCode,
    int? stature,
    String? address,
    String? computerLevelCode,
    String? englishLevelCode,
    String? nativePlaceAreaId,
    String? nativePlaceAreaName,
    String? nativePlaceAreaCascadeName,
    String? graduateSchool,
    String? majorIn,
    String? headImage,
    String? idcard,
    String? gender,
    String? nation,
    String? qQ,
    String? birthday,
    String? liveAreaId,
    String? liveAreaName,
    String? liveAreaCascadeName,
    String? intentionAreaIds,
    String? intentionAreaNames,
    String? intentionJobTypeId,
    String? intentionJobType,
    String? intentionJobTypeIds,
    String? intentionJobTypeNames,
    String? workStatusCode,
    String? workingAgeCode,
    String? intentionPayCode,
    String? intentionPayValue,
    String? intentionPayWayCode,
    String? educationCode,
    String? workExperience,
    String? eduExperience,
    String? personalProfile,
    double? proportion,
    String? certificate,
    String? skill,
    bool? isDefault,
    int? integral,
    String? phone,
    String? email,
    String? userId,
    String? releaseTime,
    String? labels,
    String? mapLocation,
    String? street,
    String? jobSeekerGroupCode,
    int? selectCount,
  }) {
    return JobRecordResume()
      ..id = id ?? this.id
      ..title = title ?? this.title
      ..realname = realname ?? this.realname
      ..userAuths = userAuths ?? this.userAuths
      ..genderCode = genderCode ?? this.genderCode
      ..nationCode = nationCode ?? this.nationCode
      ..maritalStatusCode = maritalStatusCode ?? this.maritalStatusCode
      ..stature = stature ?? this.stature
      ..address = address ?? this.address
      ..computerLevelCode = computerLevelCode ?? this.computerLevelCode
      ..englishLevelCode = englishLevelCode ?? this.englishLevelCode
      ..nativePlaceAreaId = nativePlaceAreaId ?? this.nativePlaceAreaId
      ..nativePlaceAreaName = nativePlaceAreaName ?? this.nativePlaceAreaName
      ..nativePlaceAreaCascadeName = nativePlaceAreaCascadeName ??
          this.nativePlaceAreaCascadeName
      ..graduateSchool = graduateSchool ?? this.graduateSchool
      ..majorIn = majorIn ?? this.majorIn
      ..headImage = headImage ?? this.headImage
      ..idcard = idcard ?? this.idcard
      ..gender = gender ?? this.gender
      ..nation = nation ?? this.nation
      ..qQ = qQ ?? this.qQ
      ..birthday = birthday ?? this.birthday
      ..liveAreaId = liveAreaId ?? this.liveAreaId
      ..liveAreaName = liveAreaName ?? this.liveAreaName
      ..liveAreaCascadeName = liveAreaCascadeName ?? this.liveAreaCascadeName
      ..intentionAreaIds = intentionAreaIds ?? this.intentionAreaIds
      ..intentionAreaNames = intentionAreaNames ?? this.intentionAreaNames
      ..intentionJobTypeId = intentionJobTypeId ?? this.intentionJobTypeId
      ..intentionJobType = intentionJobType ?? this.intentionJobType
      ..intentionJobTypeIds = intentionJobTypeIds ?? this.intentionJobTypeIds
      ..intentionJobTypeNames = intentionJobTypeNames ??
          this.intentionJobTypeNames
      ..workStatusCode = workStatusCode ?? this.workStatusCode
      ..workingAgeCode = workingAgeCode ?? this.workingAgeCode
      ..intentionPayCode = intentionPayCode ?? this.intentionPayCode
      ..intentionPayValue = intentionPayValue ?? this.intentionPayValue
      ..intentionPayWayCode = intentionPayWayCode ?? this.intentionPayWayCode
      ..educationCode = educationCode ?? this.educationCode
      ..workExperience = workExperience ?? this.workExperience
      ..eduExperience = eduExperience ?? this.eduExperience
      ..personalProfile = personalProfile ?? this.personalProfile
      ..proportion = proportion ?? this.proportion
      ..certificate = certificate ?? this.certificate
      ..skill = skill ?? this.skill
      ..isDefault = isDefault ?? this.isDefault
      ..integral = integral ?? this.integral
      ..phone = phone ?? this.phone
      ..email = email ?? this.email
      ..userId = userId ?? this.userId
      ..releaseTime = releaseTime ?? this.releaseTime
      ..labels = labels ?? this.labels
      ..mapLocation = mapLocation ?? this.mapLocation
      ..street = street ?? this.street
      ..jobSeekerGroupCode = jobSeekerGroupCode ?? this.jobSeekerGroupCode
      ..selectCount = selectCount ?? this.selectCount;
  }
}