# 项目介绍和使用指南

> Flutter 企业级应用开发框架 - 基于 MVVM 架构的标准化开发方案

## 📋 目录

- [项目架构](#项目架构)
  - [技术栈选型](#技术栈选型)
  - [整体架构设计](#整体架构设计)
  - [分层架构](#分层架构)
- [目前正在使用的架构和模块](#目前正在使用的架构和模块)
  - [目录结构](#目录结构)
- [新建页面完整流程](#新建页面完整流程)
  - [创建页面结构](#创建页面结构)
  - [实现页面代码](#实现页面代码)
  - [配置依赖注入](#配置依赖注入)
  - [配置路由](#配置路由)
- [常见错误及解决方案](#常见错误及解决方案)
- [检查清单](#检查清单)
- [特殊情况处理](#特殊情况处理)

---

## 项目架构

### 技术栈选型

```
核心框架: Flutter 3.0+
状态管理: Provider + ChangeNotifier
路由管理: AutoRoute
依赖注入: GetIt
网络请求: Dio
本地存储: SharedPreferences
屏幕适配: flutter_screenutil
国际化: flutter_localizations + intl
代码生成: build_runner + json_serializable
```

### 整体架构设计

#### 分层架构

项目采用清晰的分层架构，确保代码的可维护性和可扩展性：

```
┌─────────────────────────────────────────────────────────┐
│                    🎨 UI Layer (表现层)                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────┐  │
│  │     Screen      │  │      View       │  │ Widgets  │  │
│  │   (页面容器)      │  │   (页面内容)      │  │ (组件库)   │  │
│  │ - ViewStateWidget│  │ - StatefulWidget│  │ - 通用组件 │  │
│  │ - 状态管理       │  │ - UI构建         │  │ - 自定义组件│  │
│  └─────────────────┘  └─────────────────┘  └──────────┘  │
├─────────────────────────────────────────────────────────┤
│                   🧠 Logic Layer (逻辑层)                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────┐  │
│  │      Logic      │  │   ViewState     │  │ Provider │  │
│  │   (业务逻辑)      │  │   (状态管理)      │  │ (状态通知) │  │
│  │ - ViewStateLogic│  │ - Loading       │  │ - Consumer│  │
│  │ - 数据处理       │  │ - Success       │  │ - Listener│  │
│  │ - 状态控制       │  │ - Error         │  │ - Builder │  │
│  └─────────────────┘  └─────────────────┘  └──────────┘  │
├─────────────────────────────────────────────────────────┤
│                   📊 Data Layer (数据层)                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────┐  │
│  │   Repository    │  │     Entity      │  │   API    │  │
│  │   (数据仓库)      │  │   (数据模型)      │  │ (网络请求) │  │
│  │ - BaseRepository│  │ - JSON序列化     │  │ - Dio配置 │  │
│  │ - 数据获取       │  │ - 数据验证       │  │ - 接口调用 │  │
│  │ - 错误处理       │  │ - 类型转换       │  │ - 响应处理 │  │
│  └─────────────────┘  └─────────────────┘  └──────────┘  │
├─────────────────────────────────────────────────────────┤
│                   🔧 Core Layer (核心层)                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────┐  │
│  │     Locator     │  │     Router      │  │ Services │  │
│  │   (依赖注入)      │  │   (路由管理)      │  │ (核心服务) │  │
│  │ - GetIt注册     │  │ - AutoRoute     │  │ - 工具类   │  │
│  │ - 服务管理       │  │ - 路由配置       │  │ - 常量定义 │  │
│  │ - 生命周期       │  │ - 导航控制       │  │ - 扩展方法 │  │
│  └─────────────────┘  └─────────────────┘  └──────────┘  │
└─────────────────────────────────────────────────────────┘
```

##### 🔄 数据流向

```
用户操作 → Screen → Logic → Repository → API
    ↓         ↓       ↓         ↓        ↓
   UI事件 → 状态管理 → 业务处理 → 数据获取 → 网络请求
    ↑         ↑       ↑         ↑        ↑
   UI更新 ← 状态通知 ← 数据处理 ← 响应解析 ← 服务器响应
```

##### 📋 各层职责

**🎨 UI Layer (表现层)**
- **Screen**: 页面容器，使用ViewStateWidget管理页面状态
- **View**: 具体的UI实现，处理用户交互和数据展示
- **Widgets**: 可复用的UI组件库

**🧠 Logic Layer (逻辑层)**
- **Logic**: 继承ViewStateLogic，处理业务逻辑和状态管理
- **ViewState**: 统一的状态管理（Loading/Success/Error）
- **Provider**: 使用ChangeNotifier进行状态通知

**📊 Data Layer (数据层)**
- **Repository**: 数据仓库模式，统一数据访问接口
- **Entity**: 数据模型，处理JSON序列化和数据验证
- **API**: 网络请求层，使用Dio进行HTTP通信

**🔧 Core Layer (核心层)**
- **Locator**: 使用GetIt进行依赖注入管理
- **Router**: 使用AutoRoute进行路由管理
- **Services**: 核心服务和工具类

##### 🎯 架构优势

1. **职责分离**: 每层都有明确的职责，降低耦合度
2. **可测试性**: 各层独立，便于单元测试
3. **可维护性**: 代码结构清晰，易于维护和扩展
4. **可复用性**: 组件化设计，提高代码复用率
5. **状态管理**: 统一的状态管理模式，简化开发复杂度

---

## 📁 目前正在使用的架构和模块

### 目录结构

```
lib/
├── src/
│   ├── base/                    # 基础架构层 ✅
│   │   ├── core/               # 核心状态定义
│   │   │   ├── view_state.dart          ✅ 已实现
│   │   │   ├── base_result.dart         ✅ 已实现
│   │   │   └── exceptions.dart          🔄 待完善
│   │   ├── logic/              # 业务逻辑基类
│   │   │   ├── view_state_logic.dart    ✅ 已实现
│   │   │   └── view_state_paging_logic.dart ✅ 已实现
│   │   ├── widgets/            # UI组件基类
│   │   │   ├── view_state_widget.dart   ✅ 已重构
│   │   │   ├── state_widget_builder.dart ✅ 已实现
│   │   │   └── view_state_paging_widget.dart ✅ 已实现
│   │   └── l10n/               # 国际化支持
│   │       └── base_localizations.dart  ✅ 已实现
│   ├── shared/                 # 共享组件层
│   │   ├── components/         # 通用UI组件
│   │   │   ├── app_bars/       # AppBar组件 🔄 待完善
│   │   │   ├── buttons/        # 按钮组件 ❌ 待实现
│   │   │   ├── forms/          # 表单组件 🔄 部分实现
│   │   │   └── cards/          # 卡片组件 ❌ 待实现
│   │   ├── extensions/         # 扩展方法 🔄 部分实现
│   │   ├── services/           # 共享服务
│   │   │   ├── storage/        ✅ 已实现
│   │   │   ├── logger/         ✅ 已实现
│   │   │   └── analytics/      ❌ 待实现
│   │   └── locator.dart        # 依赖注入配置 ✅ 已重构
│   ├── core/                   # 核心配置层
│   │   ├── theme/              # 主题配置 🔄 部分实现
│   │   ├── routing/            # 路由配置 ✅ 已实现
│   │   ├── application.dart    # 应用入口 ✅ 已实现
│   │   └── app_initializer.dart # 初始化配置 ✅ 已实现
│   ├── datasource/             # 数据源层
│   │   ├── http/               # 网络请求 ✅ 已实现
│   │   ├── models/             # 数据模型 🔄 部分实现
│   │   ├── repositories/       # 数据仓库 ✅ 已实现
│   │   └── local/              # 本地数据源 ❌ 待实现
│   └── features/               # 功能模块层
│       ├── home/               # 首页模块 ✅ 已实现
│       ├── login/              # 登录模块 🔄 部分实现
│       ├── jobDetail/          # 职位详情 🔄 部分实现
│       └── [其他模块]           # 待扩展
└── main.dart                   # 应用入口 ✅ 已实现
```

---

## 📋 新建页面完整流程

### 1. 创建页面结构

#### 1.1 创建目录结构
```
lib/src/features/[页面名称]/
├── ui/
│   ├── [页面名称]_screen.dart
│   └── widget/
│       └── [页面名称]_view.dart
├── logic/
│   └── [页面名称]_logic.dart
└── models/ (可选)
    └── [页面名称]_model.dart
```

#### 1.2 创建Repository (如果需要网络请求)
```
lib/src/datasource/repositories/[页面名称]_repository.dart
```

### 2. 实现页面代码

#### 2.1 创建Logic类
```dart
// lib/src/features/[页面名称]/logic/[页面名称]_logic.dart
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/datasource/repositories/[页面名称]_repository.dart';

class [页面名称]Logic extends ViewStateLogic {
  final [页面名称]Repository repository;

  [页面名称]Logic({required this.repository});

  @override
  void loadData() {
    // 实现数据加载逻辑
  }
}
```

#### 2.2 创建Repository类 (如果需要)
```dart
// lib/src/datasource/repositories/[页面名称]_repository.dart
import 'package:flutter_kit/src/datasource/http/api_service.dart';
import 'package:flutter_kit/src/datasource/repositories/base_repository.dart';
import 'package:flutter_kit/src/shared/locator.dart';

class [页面名称]Repository extends BaseRepository {
  final ApiService apiService;

  [页面名称]Repository({
    ApiService? apiService,
  }) : apiService = apiService ?? ApiService(dio: locator<DioConfig>().dio);

  // 实现具体的API调用方法
}
```

#### 2.3 创建Screen类
```dart
// lib/src/features/[页面名称]/ui/[页面名称]_screen.dart
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/base.dart';
import 'package:flutter_kit/src/features/[页面名称]/logic/[页面名称]_logic.dart';
import 'package:flutter_kit/src/features/[页面名称]/ui/widget/[页面名称]_page_view.dart';
import 'package:flutter_kit/src/shared/locator.dart';

@RoutePage()
class [页面名称]Screen extends ViewStateWidget<[页面名称]Logic> {
  const [页面名称]Screen({super.key});

  @override
  [页面名称]Logic createController() {
    return locator<[页面名称]Logic>();
  }

  @override
  PreferredSizeWidget? buildAppBar(BuildContext context, [页面名称]Logic logic) {
    return AppBar(
      title: const Text('页面标题'),
      backgroundColor: Colors.transparent,
      elevation: 0,
    );
  }

  @override
  Widget buildBody(BuildContext context, [页面名称]Logic logic) {
    return [页面名称]PageView(logic: logic);
  }

  @override
  bool hasData() {
    final logic = createController();
    return logic.数据字段 != null; // 根据实际数据字段调整
  }
}
```

#### 2.4 创建PageView类
```dart
// lib/src/features/[页面名称]/ui/widget/[页面名称]_page_view.dart
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/features/[页面名称]/logic/[页面名称]_logic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class [页面名称]PageView extends StatefulWidget {
  final [页面名称]Logic logic;

  const [页面名称]PageView({
    super.key,
    required this.logic,
  });

  @override
  State<[页面名称]PageView> createState() => _[页面名称]PageViewState();
}

class _[页面名称]PageViewState extends State<[页面名称]PageView> {
  @override
  void initState() {
    super.initState();
    widget.logic.addListener(_onLogicDataChanged);
  }

  @override
  void dispose() {
    widget.logic.removeListener(_onLogicDataChanged);
    super.dispose();
  }

  void _onLogicDataChanged() {
    // 处理数据变化
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          Text('页面内容'),
        ],
      ),
    );
  }
}
```

### 3. 配置依赖注入 (locator.dart)

#### 3.1 添加导入
```dart
// lib/src/shared/locator.dart
import 'package:flutter_kit/src/datasource/repositories/[页面名称]_repository.dart';
import 'package:flutter_kit/src/features/[页面名称]/logic/[页面名称]_logic.dart';
```

#### 3.2 注册服务
```dart
final GetIt locator = GetIt.instance
  // ... 其他注册
  ..registerLazySingleton(() => [页面名称]Repository()) // 注册Repository
  ..registerFactory(() => [页面名称]Logic(repository: locator<[页面名称]Repository>())) // 注册Logic
  // ... 其他注册
```

### 4. 配置路由 (app_router.dart)

#### 4.1 添加导入
```dart
// lib/src/core/routing/app_router.dart
import '../../features/[页面名称]/ui/[页面名称]_screen.dart';
```

#### 4.2 添加路由
```dart
@AutoRouterConfig(replaceInRouteName: 'Screen,Route')
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> routes = [
    // ... 其他路由
    AutoRoute(page: [页面名称]Route.page), // 添加新路由
    // ... 其他路由
  ];
}
```

### 5. 生成路由代码

```bash
dart run build_runner build --delete-conflicting-outputs
```

### 6. 页面跳转使用

```dart
// 在其他页面中跳转
context.router.push([页面名称]Route());

// 带参数跳转 (如果需要)
context.router.push([页面名称]Route(参数名: 参数值));
```

---

## ⚠️ 常见错误及解决方案

### 错误1: GetIt注册错误
```
Bad state: GetIt: Object/factory with type [类名] is not registered
```
**解决方案**: 检查 `locator.dart` 中是否正确注册了所有依赖

### 错误2: 路由跳转失败
```
context.router.push() 没有反应
```
**解决方案**:
1. 检查 `app_router.dart` 中是否添加了路由
2. 运行 `dart run build_runner build` 重新生成路由

### 错误3: 页面一直Loading
**解决方案**:
1. 检查Logic中的 `loadData()` 方法
2. 确保网络请求有正确的错误处理和超时设置
3. 在构造函数中设置初始状态: `setSuccess(null)`

---

## 📝 检查清单

创建新页面时，请按以下清单逐项检查：

- [ ] 1. 创建了完整的目录结构
- [ ] 2. 实现了Logic类并继承ViewStateLogic
- [ ] 3. 实现了Repository类 (如果需要网络请求)
- [ ] 4. 实现了Screen类并添加@RoutePage()注解
- [ ] 5. 实现了View类
- [ ] 6. 在locator.dart中注册了Repository和Logic
- [ ] 7. 在app_router.dart中添加了导入和路由配置
- [ ] 8. 运行了build_runner生成路由代码
- [ ] 9. 测试了页面跳转功能
- [ ] 10. 测试了页面的数据加载功能

---

## 📝 特殊情况处理

### _BaseinfoPageViewState 的 initState 写法

对于使用 StatefulWidget 且需要初始化数据的页面（如BaseInfoPageView），initState应该这样写：

```dart
class _BaseinfoPageViewState extends State<BaseInfoPageView> {
  late BaseInfoEntity _baseInfoEntity;

  @override
  void initState() {
    super.initState();
    // 初始化空的BaseInfoEntity，避免late变量未初始化错误
    _baseInfoEntity = BaseInfoEntity.empty();

    // 在下一帧调用接口获取数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadBaseInfo();
    });
  }

  void _loadBaseInfo() {
    final logic = locator<BaseInfoLogic>();
    logic.loadData();

    // 监听数据变化
    logic.addListener(() {
      if (logic.baseInfoEntity != null) {
        // 处理数据更新
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => locator<BaseInfoLogic>(),
      child: Consumer<BaseInfoLogic>(
        builder: (context, logic, child) {
          final baseInfo = logic.baseInfoEntity ?? _baseInfoEntity;

          return Column(children: [
            buildAppBar(context, '基本信息'),
            Expanded(child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                children: [
                  _buildPersonalInfoSection(baseInfo),
                  SizedBox(height: 16.h),
                ],
              ),
            ))
          ]);
        },
      ),
    );
  }
}
```

### 关键点说明：

1. **初始化空对象**: `_baseInfoEntity = BaseInfoEntity.empty()` 避免late变量未初始化
2. **延迟调用**: 使用 `addPostFrameCallback` 确保在build完成后调用接口
3. **数据监听**: 通过 `addListener` 监听Logic的数据变化
4. **空值处理**: 使用 `??` 操作符提供默认值

### 常见错误解决：

#### LateInitializationError: Field 'name' has not been initialized

**错误原因**: BaseInfoEntity使用了late关键字，但创建空对象时没有初始化字段

**解决方案**: 在BaseInfoEntity中添加工厂方法：

```dart
/// 创建带有默认值的BaseInfoEntity
factory BaseInfoEntity.empty() {
  final entity = BaseInfoEntity();
  entity.name = '';
  entity.genderCode = '';
  entity.nationCode = '';
  // ... 初始化所有late字段为默认值
  return entity;
}
```

然后在initState中使用：
```dart
_baseInfoEntity = BaseInfoEntity.empty();
```
